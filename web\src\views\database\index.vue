<template>
  <div class="database-container">
    <connection-manager />
    
    <!-- 诊断面板 -->
    <div class="api-status-panel" v-if="showDebugInfo">
      <h3>API状态信息</h3>
      <div v-if="apiStatus.loading">加载中...</div>
      <div v-else>
        <div><strong>API状态:</strong> {{ apiStatus.success ? '正常' : '异常' }}</div>
        <div><strong>消息:</strong> {{ apiStatus.message }}</div>
        <div><strong>连接数:</strong> {{ apiStatus.connectionCount }}</div>
        <div><strong>时间戳:</strong> {{ apiStatus.timestamp }}</div>
        <div v-if="apiStatus.error"><strong>错误:</strong> {{ apiStatus.error }}</div>
      </div>
      <el-button size="small" @click="checkApiStatus" type="primary">刷新状态</el-button>
      <el-button size="small" @click="showDebugInfo = false" type="danger">关闭</el-button>
    </div>
    <el-button 
      v-if="!showDebugInfo" 
      class="debug-button" 
      size="small" 
      type="info" 
      @click="showDebugInfo = true"
    >
      诊断
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import ConnectionManager from './components/connection-manager.vue';
import { getConnectionDebug } from '@/api/database/index';
import { useConnectionStore } from '@/store/modules/connection';

const connectionStore = useConnectionStore();
const showDebugInfo = ref(false);
const apiStatus = reactive({
  loading: false,
  success: false,
  message: '',
  connectionCount: 0,
  timestamp: '',
  error: ''
});

// 预加载数据库连接
const preloadConnections = async () => {
  // 如果store中已有数据，则不重新加载
  if (connectionStore.isLoaded && connectionStore.connections.length > 0) {
    console.log('数据库连接已预加载，使用缓存数据');
    return;
  }
  
  try {
    await connectionStore.fetchConnections();
    console.log(`成功预加载${connectionStore.connections.length}个数据库连接`);
  } catch (error) {
    console.error('预加载数据库连接失败:', error);
  }
};

// 检查API状态
const checkApiStatus = async () => {
  apiStatus.loading = true;
  try {
    const response = await getConnectionDebug();
    console.log('API状态检查结果:', response);
    
    apiStatus.success = response.success || false;
    apiStatus.message = response.message || '无消息';
    apiStatus.connectionCount = response.database_connections || 0;
    apiStatus.timestamp = response.timestamp || '';
    apiStatus.error = '';
  } catch (error: any) {
    console.error('API状态检查失败', error);
    apiStatus.success = false;
    apiStatus.message = '请求失败';
    apiStatus.error = error.message || '未知错误';
  } finally {
    apiStatus.loading = false;
  }
};

onMounted(() => {
  // 预加载数据库连接
  preloadConnections();
  
  // 如果URL中有debug参数，自动显示调试面板
  if (window.location.search.includes('debug=true')) {
    showDebugInfo.value = true;
    checkApiStatus();
  }
});
</script>

<style lang="scss" scoped>
.database-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 16px;
}

.api-status-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  max-width: 320px;
}

.debug-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9998;
}
</style> 