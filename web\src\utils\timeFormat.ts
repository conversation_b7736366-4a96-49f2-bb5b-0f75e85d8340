/**
 * 时间格式化工具
 */
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

// 启用持续时间插件
dayjs.extend(duration);

/**
 * 格式化时间为指定格式
 * @param time 时间戳或日期字符串
 * @param format 格式化模板，默认为 YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的时间字符串
 */
export function formatTime(time: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return '-';
  return dayjs(time).format(format);
}

/**
 * 格式化持续时间
 * @param milliseconds 毫秒数
 * @param format 格式化模板，默认为 HH:mm:ss
 * @returns 格式化后的持续时间字符串
 */
export function formatDuration(milliseconds: number, format = 'HH:mm:ss'): string {
  if (!milliseconds || milliseconds <= 0) return '00:00:00';
  
  const durationObj = dayjs.duration(milliseconds);
  
  // 自定义格式化，处理天、小时、分钟、秒
  if (format === 'HH:mm:ss') {
    const hours = String(Math.floor(durationObj.asHours())).padStart(2, '0');
    const minutes = String(durationObj.minutes()).padStart(2, '0');
    const seconds = String(durationObj.seconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  }
  
  // 更复杂的格式化，支持天
  if (format === 'DD:HH:mm:ss') {
    const days = String(Math.floor(durationObj.asDays())).padStart(2, '0');
    const hours = String(durationObj.hours()).padStart(2, '0');
    const minutes = String(durationObj.minutes()).padStart(2, '0');
    const seconds = String(durationObj.seconds()).padStart(2, '0');
    return `${days}天${hours}时${minutes}分${seconds}秒`;
  }
  
  return dayjs.duration(milliseconds).format(format);
}

/**
 * 计算两个时间之间的差值
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 格式化后的持续时间字符串
 */
export function calculateDuration(startTime: string | number | Date, endTime: string | number | Date): string {
  if (!startTime || !endTime) return '00:00:00';
  
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const diff = end.diff(start);
  
  return formatDuration(diff);
}

/**
 * 获取相对时间（如：3小时前，2天前）
 * @param time 时间戳或日期字符串
 * @returns 相对时间字符串
 */
export function getRelativeTime(time: string | number | Date): string {
  if (!time) return '-';
  
  const now = dayjs();
  const target = dayjs(time);
  const diffMinutes = now.diff(target, 'minute');
  
  if (diffMinutes < 1) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  
  const diffHours = now.diff(target, 'hour');
  if (diffHours < 24) return `${diffHours}小时前`;
  
  const diffDays = now.diff(target, 'day');
  if (diffDays < 30) return `${diffDays}天前`;
  
  const diffMonths = now.diff(target, 'month');
  if (diffMonths < 12) return `${diffMonths}个月前`;
  
  return `${now.diff(target, 'year')}年前`;
} 