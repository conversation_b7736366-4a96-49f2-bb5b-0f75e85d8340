<template>
  <div class="maincontent">
    <!-- 筛选搜索区域 -->
    <div class="top">
      <el-form :inline="true" :model="searchForm" ref="searchFormRef" class="searchform">
        <el-form-item label="模型名称：" prop="name">
          <el-input v-model="searchForm.name" placeholder="请输入模型名称" clearable class="!w-[180px]" />
        </el-form-item>
        <el-form-item label="状态：" prop="status">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="!w-[180px]">
            <el-option label="启用" value="true"></el-option>
            <el-option label="禁用" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格数据区域 -->
    <div ref="tableContainer" class="table">
      <el-table
        :data="tableData"
        class="el-table"
        :height="tableMaxHeight"
        :show-overflow-tooltip="tableConfig.showOverflowTooltip"
        :tooltip-options="tableConfig.tooltipOptions"
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="模型名称" min-width="120" align="center" />
        <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip align="center" />
        <el-table-column prop="source_type" label="数据库类型" width="180" align="center">
          <template #default="scope">
            <el-tag v-if="getDbTypeColor(scope.row.source_type)" :type="getDbTypeColor(scope.row.source_type)">
              {{ getDbTypeName(scope.row.source_type) }}
            </el-tag>
            <el-tag v-else>
              {{ getDbTypeName(scope.row.source_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="tableCount" label="表比对数量" width="120" align="center">
          <template #default="scope">
            {{ scope.row.tables ? scope.row.tables.length : 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180" align="center" />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #header>
            <el-button type="primary" size="small" @click="handleAdd">新增</el-button>
          </template>
          <template #default="scope">
            <div class="ellink">
              <el-link underline="never" type="primary" @click="handleEdit(scope.row)">编辑</el-link>
              <el-link underline="never" type="primary" @click="handleExecute(scope.row)">执行</el-link>
              <el-link underline="never" type="danger" @click="handleDelete(scope.row)">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :layout="'total, sizes, prev, pager, next, jumper'"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Search, Refresh } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { getCompareModels, deleteCompareModel } from "@/api/database/index";
import type { CompareModel } from "@/types/database";
import { createSafeTableConfig } from "@/utils/util";
import { getDbTypeColor, getDbTypeName } from "@/utils/db-utils";

defineOptions({
  name: "CompareModel"
});

const router = useRouter();
const searchFormRef = ref();
const loading = ref(false);
const tableContainer = ref(null);
const tableMaxHeight = ref(0);

// 搜索表单
const searchForm = reactive({
  name: "",
  status: ""
});

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 表格数据
const tableData = ref<CompareModel[]>([]);

// 表格配置
const tableConfig = createSafeTableConfig({
  showOverflowTooltip: true,
  border: false,
  stripe: true,
  size: 'default'
});

// 计算表格高度的函数
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainer.value) {
      // 获取父容器的高度
      const parentHeight = tableContainer.value.clientHeight;
      tableMaxHeight.value = parentHeight - 50; // 设置表格最大高度
    }
  });
};

// 搜索方法
const handleSearch = () => {
  pagination.currentPage = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  pagination.currentPage = 1;
  fetchData();
};

// 分页方法
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchData();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  fetchData();
};

// 获取数据
const fetchData = async () => {
  console.log('🔄 fetchData 被调用');
  console.log('📍 调用栈:', new Error().stack);
  console.log('📊 当前分页状态:', pagination);
  console.log('🔍 当前搜索条件:', searchForm);

  loading.value = true;
  try {
    // 调用API获取数据
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      status: searchForm.status || undefined
    };

    console.log('📤 发送API请求，参数:', params);
    const response = await getCompareModels(params);
    console.log('📥 收到API响应:', response);

    // 检查响应格式并正确提取数据
    if (response && response.success) {
      // 从BaseApiResponse格式中提取data字段
      const extractedData = Array.isArray(response.data) ? response.data : [];
      const extractedTotal = response.total || (response.data ? response.data.length : 0);

      console.log('✅ 数据提取成功:');
      console.log('  - 响应成功状态:', response.success);
      console.log('  - 响应代码:', response.code);
      console.log('  - 响应消息:', response.message);
      console.log('  - 提取的数据类型:', Array.isArray(extractedData) ? 'Array' : typeof extractedData);
      console.log('  - 数据长度:', extractedData.length);
      console.log('  - 总数:', extractedTotal);
      console.log('  - 第一条数据:', extractedData[0]);

      tableData.value = extractedData;
      pagination.total = extractedTotal;

      console.log('🎯 tableData.value 已更新:', tableData.value);
      console.log('📊 pagination.total 已更新:', pagination.total);
      console.log('🔍 Vue响应式检查 - tableData.value长度:', tableData.value.length);
      console.log('🔍 Vue响应式检查 - tableData是否为数组:', Array.isArray(tableData.value));

      // 强制触发Vue响应式更新
      if (extractedData.length > 0) {
        console.log('🔄 强制触发响应式更新');
        tableData.value = [...extractedData];
      }
    } else {
      console.warn('❌ API响应格式异常:', response);
      console.warn('  - success字段:', response?.success);
      console.warn('  - code字段:', response?.code);
      console.warn('  - message字段:', response?.message);
      console.warn('  - data字段:', response?.data);
      tableData.value = [];
      pagination.total = 0;
      ElMessage.warning('数据格式异常，请刷新重试');
    }
  } catch (error) {
    console.error('💥 获取数据失败:', error);
    ElMessage.error('获取数据失败，请重试');
    // 确保在错误情况下重置数据
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
    console.log('🏁 fetchData 完成，最终状态:');
    console.log('  - tableData.value:', tableData.value);
    console.log('  - pagination.total:', pagination.total);
    console.log('  - loading.value:', loading.value);

    // 数据加载完成后重新计算表格高度
    calculateTableHeight();
  }
};

// 处理添加
const handleAdd = () => {
  router.push({path: "/database/model/create"});
};

// 处理编辑
const handleEdit = (row: CompareModel) => {
  router.push({path: `/database/model/edit/${row.id}`});
};

// 处理执行
const handleExecute = (row: CompareModel) => {
  router.push({
    path: "/database/execute",
    query: { id: row.id }
  });
};

// 处理删除
const handleDelete = (row: CompareModel) => {
  ElMessageBox.confirm(
    `确定要删除模型 ${row.name} 吗？此操作不可逆`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    try {
      loading.value = true;
      // 调用API删除数据
      await deleteCompareModel(row.id.toString());
      ElMessage.success(`删除成功`);
      fetchData();  // 刷新数据
    } catch (error) {
      console.error('删除失败', error);
      ElMessage.error('删除失败，请重试');
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    ElMessage.info("已取消删除");
  });
};

onMounted(() => {
  const instanceId = Math.random().toString(36).substring(2, 11);
  console.log(`🚀 [${instanceId}] CompareModel组件已挂载`);
  console.log(`📍 [${instanceId}] 当前路由:`, router.currentRoute.value);
  console.log(`⏰ [${instanceId}] 挂载时间:`, new Date().toISOString());

  // 计算表格高度的函数并挂载监听事件
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);

  console.log(`📞 [${instanceId}] 直接调用fetchData()`);

  // 直接调用fetchData，不使用延迟
  fetchData();
});

// 在组件卸载前移除监听器
onBeforeUnmount(() => {
  console.log('🔚 CompareModel组件即将卸载');
  window.removeEventListener("resize", calculateTableHeight);
});
</script>

<style lang="scss" scoped>
:deep(.el-table .cell) {
  overflow: hidden; // 溢出隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap; // 规定段落中的文本不进行换行
}

.main-content {
  margin: 10px 10px 0 !important;
}

.maincontent {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 131px);
  background-color: var(--bg-color);
}

.left {
  min-width: 200px;
  margin-right: 10px;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.searchform {
  background-color: var(--el-bg-color);
  /* padding: 10px; */
  .el-form-item {
    margin: 10px;
  }
}

.table {
  flex: 1;
  margin-top: 10px;
  background-color: var(--el-bg-color);
  height: 100%;
  /* 解决element表格在flex布局下无法自适应窗口宽度缩小的问题 */
  position: relative;
  .el-table {
    position: absolute;
  }
  .el-pagination {
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: center;
    bottom: 0;
    height: 50px;
  }
}

.ellink {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>