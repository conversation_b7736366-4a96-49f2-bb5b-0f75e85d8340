<template>
  <div class="maincontent">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <el-page-header :icon="null" @back="goBack">
          <template #content>
            <span class="page-title-text">{{ isEdit ? '编辑比对模型' : '创建比对模型' }}</span>
          </template>
        </el-page-header>
      </div>
      <div class="page-actions">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="saveModel">保存</el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div ref="contentAreaRef" class="content-area" :style="{ height: contentAreaHeight + 'px' }">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="model-form"
      >
        <!-- Flexbox 布局 -->
        <div class="top-row">
          <!-- 基本信息卡片 - 使用模块化组件 -->
          <div class="basic-info-wrapper">
            <model-basic-info
              v-model:name="form.name"
              v-model:description="form.description"
              class="same-height-card"
            />
          </div>

          <!-- 数据源配置卡片 - 使用模块化组件 -->
          <div class="data-source-wrapper">
            <db-connections
              v-model:source_connection="form.source_connection"
              v-model:target_connection="form.target_connection"
              class="same-height-card"
            />
          </div>
        </div>

        <!-- 表比对配置 - 使用模块化组件 -->
        <table-config-panel
          v-model:tables="form.tables"
        />
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import {
  getCompareModel,
  createCompareModel,
  updateCompareModel
} from '@/api/database/index';
import { useConnectionStore } from '@/store/modules/connection';
import type { DataConnection, CompareModel, TableConfig } from '@/types/database';
import { emitter } from '@/utils/mitt';

// 导入模块化组件
import ModelBasicInfo from './components/model-basic-info.vue';
import DbConnections from './components/db-connections.vue';
import TableConfigPanel from './components/table-config-panel.vue';

// 定义内部使用的表配置接口
interface ComponentTableConfig {
  id: string;
  name: string;
  sql_1: string;
  sql_2: string;
  remark?: string;
  diffEnabled?: boolean;
  diffInfo?: {
    matchCount: number;
    missingCount: number;
    diffCount: number;
  };
}

const route = useRoute();
const router = useRouter();
const modelId = ref(route.params.id as string);
const isEdit = ref(!!modelId.value);

// 表单引用
const formRef = ref();

// 检查路由参数
onMounted(() => {
  if (isEdit.value && !modelId.value) {
    ElMessage.error('缺少必要的模型ID参数');
    router.push('/database/model');
  }
});

// 内容区域高度计算
const contentAreaRef = ref();
const contentAreaHeight = ref(500);

// 表单数据
const form = reactive({
  id: '',
  name: '',
  description: '',
  source_connection: '',
  target_connection: '',
  tables: [] as ComponentTableConfig[]
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度应为2到50个字符', trigger: 'blur' }
  ],
  source_connection: [
    { required: true, message: '请选择源数据库连接', trigger: 'change' }
  ],
  target_connection: [
    { required: true, message: '请选择目标数据库连接', trigger: 'change' }
  ]
};

// 路由导航
const goBack = () => {
  router.push('/database/model');
};

// 计算内容区域高度
const calculateContentHeight = () => {
  nextTick(() => {
    if (contentAreaRef.value) {
      const viewportHeight = window.innerHeight;
      const headerHeight = 60; // 页面头部高度
      const pageHeaderHeight = 66; // page-header高度
      const pageBottomMargin = 15; // 底部边距

      contentAreaHeight.value = viewportHeight - headerHeight - pageHeaderHeight - pageBottomMargin;
    }
  });
};

// 准备表数据以便保存
const prepareTableData = (): TableConfig[] => {
  return form.tables.map(table => {
    // 确保数据格式符合API要求
    return {
      table_id: table.id || '',
      remark: table.remark || '',
      sql_1: table.sql_1 || '',
      sql_2: table.sql_2 || ''
    };
  });
};

// 保存模型
const saveModel = async () => {
  try {
    await formRef.value.validate();

    // 保存前的数据处理
    const tableData = prepareTableData();
    const modelData: CompareModel = {
      id: form.id,
      name: form.name,
      description: form.description,
      source_connection: form.source_connection,
      target_connection: form.target_connection,
      tables: tableData
    };

    const loading = ElLoading.service({
      lock: true,
      text: '保存中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      let response;
    if (isEdit.value) {
        // 更新现有模型
        response = await updateCompareModel(modelData);
    } else {
        // 创建新模型
        response = await createCompareModel(modelData);
      }

      if (response.success) {
        ElMessage.success('保存成功');
        // 保存后返回列表页
        router.push('/database/model');
    } else {
        ElMessage.error(response.message || '保存失败');
      }
    } finally {
      loading.close();
    }
  } catch (error) {
    console.error('表单验证失败', error);
    ElMessage.error('请检查表单填写是否完整');
  }
};

// 加载模型详情
const loadModelDetail = async () => {
  if (!isEdit.value) return;

  const loading = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    const res = await getCompareModel(modelId.value);
    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res.success && res.data) {
      // 填充表单数据
      const modelData = res.data;
      form.id = modelData.id || '';
      form.name = modelData.name;
      form.description = modelData.description || '';
      // 确保连接ID是字符串类型
      form.source_connection = modelData.source_connection ? String(modelData.source_connection) : '';
      form.target_connection = modelData.target_connection ? String(modelData.target_connection) : '';

      // 处理表数据，转换数据结构
      if (Array.isArray(modelData.tables)) {
        form.tables = modelData.tables.map(table => ({
          id: table.table_id || '',
          name: table.remark || '',
          remark: table.remark || '',
          sql_1: table.sql_1 || '',
          sql_2: table.sql_2 || '',
          diffEnabled: false // 初始状态下关闭差异视图
        }));
      }
    } else {
      ElMessage.error(res.message || '加载模型详情失败');
      goBack();
    }
  } catch (error) {
    console.error('加载模型详情失败', error);
    ElMessage.error('加载模型详情失败');
    goBack();
  } finally {
    loading.close();
  }
};

// 生命周期钩子
onMounted(() => {
  calculateContentHeight();
  window.addEventListener('resize', calculateContentHeight);

  // 加载模型详情
  loadModelDetail();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateContentHeight);
});
</script>

<style lang="scss" scoped>
.main-content {
  margin: 10px 10px 0 !important;
}

.maincontent {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 131px);
  background-color: var(--bg-color, var(--el-bg-color-page));
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 15px;
  background-color: var(--el-bg-color);
  margin-bottom: 10px;

  .page-title {
    display: flex;
    align-items: center;

    .page-title-text {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .page-actions {
    display: flex;
    gap: 10px;
  }
}

.content-area {
  flex: 1;
  overflow: hidden;
  padding: 0;
  border: none;

  .model-form {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
  }
}

.top-row {
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  gap: 2px;
  flex-shrink: 0; /* 防止top-row被压缩 */

  .basic-info-wrapper {
    flex: 2;
    min-width: 0;
    display: flex;
  }

  .data-source-wrapper {
    flex: 3;
    min-width: 0;
    display: flex;
  }

  .same-height-card {
    height: 100%;

    :deep(.el-card) {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 0 !important;

      .el-card__body {
        flex-grow: 1;
        padding: 15px;
      }

      .el-card__header {
        padding: 10px 15px;
        border-bottom: 1px solid var(--el-border-color-lighter);
      }
    }
  }
}

/* 表配置面板样式 */
:deep(.table-config-panel) {
  flex: 1; /* 使表配置区域占满剩余空间 */
  min-height: 0; /* 允许内容区域缩小 */
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保高度占满容器 */

  .el-card {
    margin-bottom: 0;
    height: 100%; /* 卡片高度100% */
    display: flex;
    flex-direction: column;

    .el-card__header {
      padding: 10px 15px;
      flex-shrink: 0; /* 防止header被压缩 */
    }

    .el-card__body {
      padding: 10px 15px;
      flex: 1;
      overflow-y: auto;
      min-height: 0; /* 允许内容区域缩小 */
    }
  }
}

/* 统一卡片样式 */
:deep(.el-card) {
  border-radius: 0 !important;
  background-color: var(--el-bg-color);

  .el-card__header {
    padding: 10px 15px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-card__body {
    padding: 15px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }

  .el-form-item:last-child {
    margin-bottom: 5px;
  }
}

@media (max-width: 768px) {
  .top-row {
    flex-direction: column;
    gap: 10px;
  }
}
</style>