import json
import logging
import random
import traceback
from datetime import datetime
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action, api_view, permission_classes, authentication_classes
from django.views.decorators.csrf import csrf_exempt
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.http import JsonResponse
from django.db.models import Q
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from utils.db_utils import db_agent_client, get_db_agent_client
from utils.response import api_response, error_response
from utils.error_handler import format_error, notify_error, is_fatal_error, ErrorCategory, ErrorSeverity
from .models import CompareModel, CompareTask, CompareDiff, DataConnection, SourceOnlyRecord, TargetOnlyRecord
from .serializers import CompareModelSerializer, CompareTaskSerializer, CompareDiffSerializer, DataConnectionSerializer
from .tasks import schedule_task_status_check
from .services import DiffPersistenceService, DiffRetrievalService, TaskCompletionService
from .agent_adapter import create_task_with_agent_support, get_task_status_with_agent_support
from .agent_adapter import create_task_with_agent_support, get_task_status_with_agent_support

logger = logging.getLogger(__name__)

# 获取Channel Layer
channel_layer = get_channel_layer()

# 导入统一的任务更新函数
from utils.websocket_manager import send_task_update as websocket_send_task_update

def send_task_update(task):
    """
    通过WebSocket发送任务更新通知

    Args:
        task: 任务实例
    """
    # 使用统一的任务更新函数
    return websocket_send_task_update(task)

def api_response(code=0, message='操作成功', data=None, **kwargs):
    """统一的API响应格式
    Args:
        code: 响应码,0表示成功,非0表示失败
        message: 响应消息
        data: 响应数据
        **kwargs: 其他参数,如total等
    Returns:
        Response: DRF响应对象
    """
    resp = {'code': code, 'message': message, 'data': data}
    if kwargs:
        resp.update(kwargs)

    return Response(resp)

def error_response(code=400, message='操作失败', data=None):
    """统一的错误响应格式
    Args:
        code: 错误码
        message: 错误消息
        data: 错误数据
    Returns:
        Response: DRF响应对象
    """
    return api_response(code=code, message=message, data=data)

class CompareModelViewSet(viewsets.ModelViewSet):
    """比对模型视图集"""
    queryset = CompareModel.objects.all()
    serializer_class = CompareModelSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['name', 'description']
    ordering_fields = ['id', 'name', 'create_time', 'update_time']
    ordering = ['-create_time']

    def get_queryset(self):
        queryset = super().get_queryset()
        name = self.request.query_params.get('name')
        if name:
            queryset = queryset.filter(name__icontains=name)
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return api_response(
                message='获取比对模型列表成功',
                data=serializer.data,
                total=queryset.count()
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message='获取比对模型列表成功',
            data=serializer.data,
            total=queryset.count()
        )

    def retrieve(self, request, *args, **kwargs):
        """获取比对模型详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return api_response(
            message='获取比对模型成功',
            data=serializer.data
        )

    def create(self, request, *args, **kwargs):
        """创建比对模型"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return api_response(
                message='创建比对模型成功',
                data=serializer.data,
                headers=headers
            )
        except Exception as e:
            logger.error(f"创建比对模型失败: {str(e)}")
            return error_response(
                code=400,
                message=f"创建失败: {str(e)}"
            )

    def update(self, request, *args, **kwargs):
        """更新比对模型"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        try:
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return api_response(
                message='更新比对模型成功',
                data=serializer.data
            )
        except Exception as e:
            logger.error(f"更新比对模型失败: {str(e)}")
            return error_response(
                code=400,
                message=f"更新失败: {str(e)}"
            )

    def destroy(self, request, *args, **kwargs):
        """删除比对模型"""
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return api_response(
                message='删除比对模型成功'
            )
        except Exception as e:
            logger.error(f"删除比对模型失败: {str(e)}")
            return error_response(
                code=400,
                message=f"删除失败: {str(e)}"
            )


class CompareTaskViewSet(viewsets.ModelViewSet):
    """比对任务视图集"""
    queryset = CompareTask.objects.all()
    serializer_class = CompareTaskSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['model', 'status']
    ordering_fields = ['id', 'start_time', 'create_time']
    ordering = ['-create_time']

    # 临时禁用认证以便测试
    authentication_classes = []
    permission_classes = []

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return api_response(
                message='获取比对任务列表成功',
                data=serializer.data,
                total=queryset.count()
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message='获取比对任务列表成功',
            data=serializer.data,
            total=queryset.count()
        )

    def retrieve(self, request, *args, **kwargs):
        """获取任务详情 - 支持Agent状态同步"""
        instance = self.get_object()

        # 尝试从Agent同步状态
        from .agent_adapter import get_agent_adapter
        agent_adapter = get_agent_adapter()
        if agent_adapter.is_agent_enabled():
            agent_adapter.sync_task_status(instance)
            # 重新获取实例以获取最新状态
            instance.refresh_from_db()

        serializer = self.get_serializer(instance)
        return api_response(
            message='获取任务详情成功',
            data=serializer.data
        )

    @action(detail=False, methods=['post'])
    def precheck(self, request):
        """预检查比对任务配置"""
        model_id = request.data.get('modelId')
        if not model_id:
            return error_response(
                code=400,
                message='缺少模型ID',
                data={'messages': ['缺少模型ID']}
            )

        try:
            model = CompareModel.objects.get(id=model_id)
        except CompareModel.DoesNotExist:
            return error_response(
                code=404,
                message='模型不存在',
                data={'messages': ['模型不存在']}
            )

        # 验证源连接和目标连接
        messages = []
        source_ok = target_ok = False

        try:
            if model.source_connection:
                source_conn = DataConnection.objects.get(id=model.source_connection)
                # 调用数据库工具验证连接
                source_result = db_agent_client.test_connection({
                    'type': source_conn.type,
                    'host': source_conn.host,
                    'port': source_conn.port,
                    'username': source_conn.username,
                    'password': source_conn.password,
                    'database': source_conn.database,
                    'parameters': source_conn.parameters
                })
                source_ok = source_result.get('success', False)
                if not source_ok:
                    messages.append(f"源连接测试失败: {source_result.get('message', '未知错误')}")
                else:
                    messages.append("源连接测试成功")
            else:
                messages.append("未配置源连接")
        except Exception as e:
            logger.error(f"源连接测试异常: {str(e)}")
            messages.append(f"源连接测试异常: {str(e)}")

        try:
            if model.target_connection:
                target_conn = DataConnection.objects.get(id=model.target_connection)
                # 调用数据库工具验证连接
                target_result = db_agent_client.test_connection({
                    'type': target_conn.type,
                    'host': target_conn.host,
                    'port': target_conn.port,
                    'username': target_conn.username,
                    'password': target_conn.password,
                    'database': target_conn.database,
                    'parameters': target_conn.parameters
                })
                target_ok = target_result.get('success', False)
                if not target_ok:
                    messages.append(f"目标连接测试失败: {target_result.get('message', '未知错误')}")
                else:
                    messages.append("目标连接测试成功")
            else:
                messages.append("未配置目标连接")
        except Exception as e:
            logger.error(f"目标连接测试异常: {str(e)}")
            messages.append(f"目标连接测试异常: {str(e)}")

        # 验证模型中的表配置
        table_count = model.tables.count()
        if table_count == 0:
            messages.append("模型没有配置任何表")
        else:
            messages.append(f"模型包含 {table_count} 个表配置")

        success = source_ok and target_ok and table_count > 0

        if success:
            return api_response(
                message='预检查完成',
                data={'messages': messages}
            )
        else:
            return error_response(
                code=400,
                message='预检查失败',
                data={'messages': messages}
            )

    def _start_task(self, instance, request_data=None):
        """启动比对任务的辅助方法，处理start和launch的共同逻辑

        Args:
            instance: 任务实例
            request_data: 请求数据，用于获取limit和options

        Returns:
            Response或error_response: 成功或失败的响应
        """
        request_data = request_data or {}

        # 检查任务状态
        if instance.status not in ['waiting', 'stopped']:
            return error_response(
                code=400,
                message=f'当前任务状态为 {instance.get_status_display()}，无法启动'
            )

        # 获取比对模型
        model = instance.model
        if not model:
            return error_response(
                code=400,
                message='比对模型不存在'
            )

        # 获取表配置
        tables = model.tables.all()
        if not tables:
            return error_response(
                code=400,
                message='比对模型未配置数据表'
            )

        # 获取数据库连接信息
        source_conn_id = model.source_connection
        target_conn_id = model.target_connection

        if not source_conn_id or not target_conn_id:
            return error_response(
                code=400,
                message='未配置数据库连接'
            )

        # 获取数据库连接详情
        try:
            source_conn = DataConnection.objects.get(id=source_conn_id)
            target_conn = DataConnection.objects.get(id=target_conn_id)
        except DataConnection.DoesNotExist:
            return error_response(
                code=400,
                message='数据库连接不存在'
            )

        # 构建任务名称和设置
        task_name = request_data.get('name') or f"{model.name}-{instance.id}"
        limit = int(request_data.get('limit', 1000))
        options = request_data.get('options', {})

        # 更新任务名称
        if not instance.name:
            instance.name = task_name
            instance.save()

        # 发送比对请求 - 支持Agent标准
        from .agent_adapter import get_agent_adapter

        agent_adapter = get_agent_adapter()
        if agent_adapter.is_agent_enabled():
            # 使用Agent标准
            agent_task_id = agent_adapter.create_task_via_agent({
                'model': model.id,
                'name': task_name
            })

            if agent_task_id:
                # Agent任务创建成功
                result = {
                    'success': True,
                    'data': {
                        'task_id': agent_task_id,
                        'status': 'pending',
                        'message': 'Agent任务创建成功'
                    }
                }
            else:
                # Agent失败，回退到原有方式
                db_agent_client = get_db_agent_client()
                result = db_agent_client.create_compare_task(
                    task_name=task_name,
                    tables=tables,
                    source_conn=source_conn,
                    target_conn=target_conn,
                    limit=limit,
                    options=options
                )
        else:
            # 使用原有方式
            db_agent_client = get_db_agent_client()
            result = db_agent_client.create_compare_task(
                task_name=task_name,
                tables=tables,
                source_conn=source_conn,
                target_conn=target_conn,
                limit=limit,
                options=options
            )

        if not result.get('success', False):
            # 更新任务状态为失败
            instance.status = 'failed'
            instance.result = {
                'success': False,
                'message': result.get('message', '未知错误')
            }
            instance.save()
            logger.error(f"启动比对任务失败: {result}")
            return error_response(code=500, message=result.get('message', '启动任务失败'))

        # 成功创建任务，更新任务信息
        task_data = result.get('data', {})
        instance.external_id = task_data.get('task_id')  # 保存DB-Agent返回的任务ID
        instance.status = 'running'
        instance.start_time = timezone.now()
        instance.result = {
            'task_id': task_data.get('task_id'),
            'status': task_data.get('status'),
            'message': task_data.get('message'),
            'links': task_data.get('links', {})
        }
        instance.save()

        # 将任务加入定时查询队列
        schedule_task_status_check(instance.id, instance.external_id)

        return None  # 表示成功处理，由调用方决定返回内容

    @action(methods=['post'], detail=True)
    def start(self, request, *args, **kwargs):
        """启动比对任务"""
        # 获取任务实例
        try:
            instance = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 调用共享方法启动任务
        result = self._start_task(instance, request.data)
        if result:  # 如果返回了错误响应
            return result

        return Response({'success': True, 'message': '任务已启动', 'task_id': instance.external_id})

    def destroy(self, request, *args, **kwargs):
        """删除比对任务"""
        try:
            instance = self.get_object()
            task_name = instance.name or f"任务-{instance.id}"

            # 检查任务状态，如果正在运行则先停止
            if instance.status in ['running', 'waiting', 'paused']:
                # 如果有外部任务ID，尝试停止Agent端的任务
                if instance.external_id:
                    try:
                        result = db_agent_client.control_task(instance.external_id, 'stop')
                        if result.get("success", False):
                            logger.info(f"成功停止Agent端任务: {instance.external_id}")
                        else:
                            logger.warning(f"停止Agent端任务失败: {result.get('message', '未知错误')}")
                    except Exception as e:
                        logger.warning(f"停止Agent端任务时发生异常: {str(e)}")

                # 更新本地任务状态
                instance.status = 'stopped'
                instance.end_time = timezone.now()
                instance.save()

            # 删除任务
            self.perform_destroy(instance)

            return api_response(
                message=f'任务 "{task_name}" 删除成功'
            )

        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在，可能已被删除'
            )
        except Exception as e:
            # 检查是否是DoesNotExist错误
            if 'No CompareTask matches the given query' in str(e):
                return error_response(
                    code=404,
                    message='任务不存在，可能已被删除'
                )
            logger.error(f"删除任务失败: {str(e)}")
            return error_response(
                code=500,
                message=f"删除任务失败: {str(e)}"
            )

    @action(detail=True, methods=['post'])
    def control(self, request, pk=None):
        """控制比对任务执行（暂停、恢复、停止）"""
        action = request.data.get('action')
        if action not in ['pause', 'resume', 'stop']:
            return error_response(
                code=400,
                message='不支持的操作，仅支持: pause, resume, stop'
            )

        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 如果有外部任务ID，则调用DB-Agent控制任务
        if task.external_id and task.status in ['waiting', 'running', 'paused']:
            try:
                # 调用DB-Agent控制任务
                result = db_agent_client.control_task(task.external_id, action)

                if result.get("success", False):
                    # 根据操作类型更新任务状态
                    if action == 'pause':
                        task.status = 'paused'
                        task.message = '任务已暂停'
                    elif action == 'resume':
                        task.status = 'running'
                        task.message = '任务已恢复执行'
                    elif action == 'stop':
                        task.status = 'stopped'
                        task.message = '任务已停止'
                        if task.start_time:
                            task.end_time = timezone.now()

                    task.save()
                    logger.info(f"DB-Agent任务控制成功: {task.external_id}, 操作: {action}")

                    # 发送WebSocket通知
                    send_task_update(task)
                else:
                    logger.error(f"DB-Agent任务控制失败: {result}")
                    return error_response(
                        code=500,
                        message=f"任务控制失败: {result.get('message', '未知错误')}"
                    )

            except Exception as e:
                logger.exception(f"调用DB-Agent控制任务异常: {str(e)}")
                return error_response(
                    code=500,
                    message=f"任务控制异常: {str(e)}"
                )
        else:
            # 本地任务状态控制逻辑（原有代码）
            if action == 'pause':
                if task.status != 'running':
                    return error_response(
                        code=400,
                        message='只有运行中的任务可以暂停'
                    )
                task.status = 'paused'
                task.message = '任务已暂停'

            elif action == 'resume':
                if task.status != 'paused':
                    return error_response(
                        code=400,
                        message='只有已暂停的任务可以恢复'
                    )
                task.status = 'running'
                task.message = '任务已恢复执行'

            elif action == 'stop':
                if task.status not in ['waiting', 'running', 'paused']:
                    return error_response(
                        code=400,
                        message='只有等待中、运行中或已暂停的任务可以停止'
                    )
                task.status = 'stopped'
                task.message = '任务已停止'
                if task.start_time:
                    task.end_time = timezone.now()

            task.save()

            # 发送WebSocket通知
            send_task_update(task)

        serializer = self.get_serializer(task)
        return api_response(
            message=f'任务已{"暂停" if action == "pause" else "恢复" if action == "resume" else "停止"}',
            data=serializer.data
        )

    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """获取任务状态"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 检查任务是否已完成（终态）
        terminal_states = ['success', 'failed', 'stopped', 'completed', 'error', 'cancelled']
        if task.status in terminal_states:
            # 已完成的任务直接返回数据库中的状态，不再查询Agent
            logger.debug(f"任务已完成，直接返回数据库状态: task_id={task.id}, status={task.status}")
            serializer = self.get_serializer(task)
            return api_response(
                message='任务状态获取成功',
                data=serializer.data
            )

        # 如果任务有外部ID并且状态是运行中，则从DB-Agent获取最新状态
        if task.external_id and task.status in ['waiting', 'running', 'paused']:
            try:
                # 查询DB-Agent任务状态
                agent_status = db_agent_client.get_task_status(task.external_id)

                if agent_status.get("success", False):
                    # 映射状态
                    agent_task_status = agent_status.get("status", "").lower()
                    if agent_task_status in ["running", "pending"]:
                        task_status = "running"
                    elif agent_task_status == "completed":
                        task_status = "success"
                    elif agent_task_status in ["failed", "error"]:
                        task_status = "failed"
                    else:
                        task_status = task.status

                    # 更新任务状态
                    task.status = task_status
                    task.progress = agent_status.get("progress", task.progress)

                    # 更新统计数据
                    stats = agent_status.get("stats", {})
                    task.total_records = stats.get("source_count", task.total_records)
                    task.processed_records = stats.get("processed_count", task.processed_records)
                    task.matched_records = stats.get("match_count", task.matched_records)
                    task.different_records = stats.get("diff_count", task.different_records)

                    # 如果任务已完成，设置结束时间
                    if task_status in ["success", "failed"]:
                        task.end_time = timezone.now()
                        task.message = "任务已完成" if task_status == "success" else "任务执行失败"

                    task.save()

                    # 如果任务成功完成，自动持久化差异记录
                    if task_status == "success":
                        try:
                            completion_result = TaskCompletionService.handle_task_completion(task)
                            if completion_result['success']:
                                logger.info(f"任务{task.id}完成处理成功: {completion_result['message']}")
                            else:
                                logger.error(f"任务{task.id}完成处理失败: {completion_result['message']}")
                        except Exception as e:
                            logger.exception(f"处理任务{task.id}完成时发生异常: {str(e)}")

                    # 发送WebSocket通知
                    send_task_update(task)

            except Exception as e:
                logger.exception(f"查询DB-Agent任务状态异常: {str(e)}")
                # 出错时不更新任务状态，仅记录日志

        serializer = self.get_serializer(task)

        # 获取任务的表级进度信息
        table_progress = []

        # 从DB-Agent获取表级进度信息
        if task.external_id and task.status in ['running', 'paused', 'success']:
            try:
                # 查询DB-Agent表级进度
                table_results = db_agent_client.get_task_table_progress(task.external_id)

                if table_results.get("success", False) and "tables" in table_results:
                    table_progress = table_results.get("tables", [])

                    # 检查是否所有表都已完成（进度100%）
                    if table_progress and task.status == 'running':
                        total_progress_entry = next((p for p in table_progress if p.get('tableName') == '总计'), None)
                        if total_progress_entry and total_progress_entry.get('progress', 0) >= 100:
                            # 任务实际已完成，更新状态
                            logger.info(f"检测到任务实际已完成: task_id={task.id}, 更新状态为success")
                            task.status = 'success'
                            task.progress = 100
                            task.end_time = timezone.now()
                            task.message = "任务已完成"
                            task.save()

                            # 发送WebSocket通知
                            send_task_update(task)

            except Exception as e:
                logger.exception(f"查询DB-Agent表级进度异常: {str(e)}")
                # 如果获取失败，使用现有表生成模拟数据（仅作为后备方案）
                if task.model:
                    tables = task.model.tables.all()
                    for table in tables:
                        total = 2000 + random.randint(0, 3000)
                        progress_pct = min(100, task.progress + random.randint(-10, 10))
                        if progress_pct < 0:
                            progress_pct = 0
                        processed = int(total * progress_pct / 100)

                        match_rate = 0.85
                        diff_rate = 0.1
                        error_rate = 0.05

                        table_progress.append({
                            'tableName': table.table_id,
                            'totalRecords': total,
                            'processed': processed,
                            'matched': int(processed * match_rate),
                            'different': int(processed * diff_rate),
                            'errors': int(processed * error_rate),
                            'progress': progress_pct
                        })

        # 计算预估剩余时间
        remaining_time = 0
        if task.status == 'running' and task.start_time and task.progress > 0:
            elapsed = (timezone.now() - task.start_time).total_seconds()
            if task.progress < 100:
                remaining_time = int((elapsed / task.progress) * (100 - task.progress))

        return api_response(
            data=serializer.data,
            details={
                'tableProgress': table_progress,
                'remainingTime': remaining_time,
                'currentSpeed': random.randint(50, 500)  # 这个可以保留随机值或从DB-Agent获取
            }
        )

    @action(detail=True, methods=['get'])
    def result(self, request, pk=None):
        """获取任务结果"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 检查任务是否已完成
        if task.status not in ['success', 'failed']:
            return error_response(
                code=400,
                message=f'任务尚未完成，当前状态: {task.status}'
            )

        # 如果任务有外部ID，从DB-Agent获取详细结果
        result_data = {}
        if task.external_id:
            try:
                # 查询DB-Agent任务结果
                agent_result = db_agent_client.get_task_result(task.external_id)

                if agent_result.get("success", False):
                    result_data = agent_result.get("data", {})
                else:
                    logger.warning(f"从DB-Agent获取任务结果失败: {agent_result.get('message', '未知错误')}")

            except Exception as e:
                logger.exception(f"查询DB-Agent任务结果异常: {str(e)}")

        # 如果没有从DB-Agent获取到结果，使用任务本身的结果
        if not result_data and task.result:
            result_data = task.result

        # 从Agent结果中提取统计信息
        agent_summary = result_data.get('summary', {})

        # 获取实际的差异记录数（从数据库查询）
        actual_diff_count = CompareDiff.objects.filter(task=task).count()

        # 数据逻辑修正：确保数据一致性
        total_records = agent_summary.get('source_count', task.total_records or 0)
        diff_count = agent_summary.get('diff_count', actual_diff_count)
        error_count = agent_summary.get('error_count', task.error_records or 0)

        # 如果Agent没有提供匹配数，则根据逻辑计算
        if agent_summary.get('matched_count') is not None:
            matched_count = agent_summary.get('matched_count')
        else:
            # 逻辑计算：总数 - 差异数 - 错误数 = 匹配数
            matched_count = max(0, total_records - diff_count - error_count)

        # 更新任务数据库记录（如果数据不一致）
        if (task.matched_records != matched_count or
            task.different_records != diff_count or
            task.error_records != error_count):
            task.matched_records = matched_count
            task.different_records = diff_count
            task.error_records = error_count
            task.save(update_fields=['matched_records', 'different_records', 'error_records'])
            logger.info(f"更新任务{task.id}的统计数据: 匹配={matched_count}, 差异={diff_count}, 错误={error_count}")

        # 构造响应数据 - 统一字段格式，去除冗余
        response_data = {
            'task_id': str(task.id),
            'external_id': task.external_id,
            'status': task.status,
            'summary': {
                # 使用统一的字段命名
                'total_records': total_records,
                'matched_records': matched_count,
                'different_records': diff_count,
                'error_records': error_count,
                'processed_records': agent_summary.get('processed_count', task.processed_records or 0),

                # 保留前端兼容字段（但使用一致的数据）
                'totalRecords': total_records,
                'matchedRecords': matched_count,
                'differentRecords': diff_count,
                'errorRecords': error_count,

                # 简化字段名（使用一致的数据）
                'total': total_records,
                'matched': matched_count,
                'different': diff_count,
                'errors': error_count
            },
            'details': result_data.get('details', []),
            'execution_time': None,
            'completed_at': task.end_time.isoformat() if task.end_time else None,
            'message': task.message or '任务已完成'
        }

        # 添加差异类型统计（如果Agent提供了）
        if 'diff_type_counts' in result_data:
            response_data['diffTypeCounts'] = result_data['diff_type_counts']
        elif 'diffTypeCounts' in result_data:
            response_data['diffTypeCounts'] = result_data['diffTypeCounts']

        # 计算执行时间
        if task.start_time and task.end_time:
            execution_time = (task.end_time - task.start_time).total_seconds()
            response_data['execution_time'] = int(execution_time)

        return api_response(
            message='获取任务结果成功',
            data=response_data
        )

    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """获取任务的分页差异结果 - 使用新的服务架构"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 检查任务是否已完成
        if task.status not in ['success', 'failed']:
            return error_response(
                code=400,
                message=f'任务尚未完成，当前状态: {task.status}'
            )

        # 获取分页参数
        page = int(request.GET.get('page', 1))
        size = int(request.GET.get('size', 10))

        # 获取筛选参数
        filters = {
            'diff_type': request.GET.get('diffType'),
            'severity': request.GET.get('severity'),
            'keyword': request.GET.get('keyword')
        }

        # 使用新的差异检索服务
        result = DiffRetrievalService.get_task_diffs(task, page, size, filters)

        if not result['success']:
            return error_response(
                code=500,
                message=f'获取差异结果失败: {result["message"]}'
            )

        # 构造响应数据，包含数据源信息
        response_data = {
            'results': result['results'],
            'total': result['total'],
            'page': result['page'],
            'size': result['size'],
            'pages': result['pages'],
            'source': result['source']  # 标识数据来源
        }

        # 添加警告信息（如果有）
        if 'warning' in result:
            response_data['warning'] = result['warning']

        # 添加持久化信息（如果有）
        if 'persistence_info' in result:
            response_data['persistence_info'] = result['persistence_info']

        return api_response(
            message='获取差异结果成功',
            data=response_data
        )

    @action(detail=True, methods=['post'])
    def persist_diffs(self, request, pk=None):
        """手动持久化任务的差异记录"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        # 检查任务是否已完成
        if task.status not in ['success', 'failed']:
            return error_response(
                code=400,
                message=f'只有已完成的任务才能持久化差异记录，当前状态: {task.status}'
            )

        # 获取强制刷新参数
        force_refresh = request.data.get('force_refresh', False)

        # 执行持久化
        result = DiffPersistenceService.persist_task_diffs(task, force_refresh=force_refresh)

        if result['success']:
            return api_response(
                message=result['message'],
                data={
                    'task_id': task.id,
                    'stats': result['stats'],
                    'source': result['source']
                }
            )
        else:
            return error_response(
                code=500,
                message=result['message'],
                data={'stats': result.get('stats', {})}
            )

    def _get_diff_type_display(self, diff_type):
        """获取差异类型的显示名称"""
        diff_type_map = {
            'target_missing': '目标缺失',
            'source_missing': '源缺失',
            'value_different': '值不同',
            'type_different': '类型不同',
            'structure_different': '结构不同'
        }
        return diff_type_map.get(diff_type, diff_type)

    @action(detail=True, methods=['post'])
    def stop(self, request, pk=None):
        """停止比对任务"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        if task.status not in ['waiting', 'running']:
            return error_response(
                code=400,
                message='只有等待中或运行中的任务可以停止'
            )

        # 更新任务状态
        task.status = 'stopped'
        if task.start_time:
            task.end_time = timezone.now()
        task.save()

        # 发送WebSocket通知
        send_task_update(task)

        serializer = self.get_serializer(task)
        return api_response(
            message='任务已停止',
            data=serializer.data
        )

    @action(detail=False, methods=['post'])
    def launch(self, request):
        """创建并立即启动比对任务"""
        # 1. 创建任务
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return error_response(
                code=400,
                message='任务创建失败',
                data=serializer.errors
            )

        # 保存任务实例
        try:
            instance = serializer.save()
        except Exception as e:
            logger.error(f"创建比对任务异常: {str(e)}")
            return error_response(
                code=500,
                message=f'任务创建异常: {str(e)}'
            )

        # 调用共享方法启动任务
        result = self._start_task(instance, request.data)
        if result:  # 如果有错误发生
            instance.delete()  # 删除刚创建的任务
            return result

        # 返回任务信息 - 直接从数据库重新查询最新的实例，避免使用已保存过的实例
        refreshed_instance = CompareTask.objects.get(pk=instance.pk)
        serializer = self.get_serializer(refreshed_instance)
        return api_response(
            message='任务已创建并启动',
            data=serializer.data
        )

    @action(detail=True, methods=['post'])
    def error(self, request, pk=None):
        """接收DB-Agent错误报告

        允许Agent向服务器报告任务执行过程中遇到的错误，
        服务器将更新任务状态并保存错误信息。

        请求体格式:
        {
            "task_id": "外部任务ID",
            "error_type": "错误类型",
            "error_message": "错误消息",
            "traceback": "错误堆栈跟踪",
            "context": {
                "key1": "value1",
                ...
            }
        }
        """
        try:
            # 先获取任务实例
            task = self.get_object()

            # 验证请求中的任务ID与外部任务ID是否匹配
            if task.external_id and task.external_id != request.data.get('task_id'):
                logger.warning(f"错误报告任务ID不匹配: 期望={task.external_id}, 实际={request.data.get('task_id')}")
                return error_response(
                    code=400,
                    message='任务ID不匹配'
                )

            # 提取错误信息
            error_type = request.data.get('error_type', 'Unknown')
            error_message = request.data.get('error_message', '未知错误')
            traceback = request.data.get('traceback', '')
            context = request.data.get('context', {})

            # 更新任务状态为失败
            task.status = 'failed'
            task.end_time = timezone.now()
            task.message = f"执行失败: {error_message}"

            # 保存错误详情到任务结果中
            if not task.result:
                task.result = {}

            task.result.update({
                'error': {
                    'type': error_type,
                    'message': error_message,
                    'traceback': traceback,
                    'context': context,
                    'reported_at': timezone.now().isoformat()
                }
            })

            task.save()

            # 记录错误日志
            logger.error(f"Agent报告任务错误 - ID: {pk}, 外部ID: {task.external_id}, 类型: {error_type}, 消息: {error_message}")

            # 可选: 发送通知给管理员或其他系统

            return Response({'success': True, 'message': '错误报告已接收'})

        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )
        except Exception as e:
            logger.exception(f"处理错误报告时发生异常: {str(e)}")
            return error_response(
                code=500,
                message=f'处理错误报告失败: {str(e)}'
            )

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消比对任务"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        if task.status not in ['waiting', 'running', 'paused']:
            return error_response(
                code=400,
                message='只有等待中、运行中或已暂停的任务可以取消'
            )

        # 如果有外部任务ID，则调用DB-Agent取消任务
        if task.external_id:
            try:
                # 调用DB-Agent取消任务
                db_agent_client = get_db_agent_client()
                result = db_agent_client.cancel_task(task.external_id)

                if not result.get("success", False):
                    logger.error(f"DB-Agent取消任务失败: {result}")
                    # 即使DB-Agent取消失败，我们也将任务标记为已取消
                    logger.warning(f"尽管DB-Agent取消失败，仍将任务标记为已取消: {task.id}")
            except Exception as e:
                logger.exception(f"调用DB-Agent取消任务异常: {str(e)}")
                # 即使发生异常，我们也将任务标记为已取消
                logger.warning(f"尽管调用DB-Agent异常，仍将任务标记为已取消: {task.id}")

        # 更新任务状态
        task.status = 'stopped'  # 使用stopped状态表示取消
        task.message = '任务已取消'
        if task.start_time:
            task.end_time = timezone.now()
        task.save()

        # 发送WebSocket通知
        send_task_update(task)

        # 停止轮询
        from .tasks import _running_timers
        if str(task.id) in _running_timers:
            logger.info(f"取消任务时停止轮询: task_id={task.id}")
            del _running_timers[str(task.id)]

        serializer = self.get_serializer(task)
        return api_response(
            message='任务已取消',
            data=serializer.data
        )

    @action(detail=True, methods=['post'])
    def stop_polling(self, request, pk=None):
        """停止任务状态轮询"""
        try:
            task = self.get_object()
        except CompareTask.DoesNotExist:
            return error_response(
                code=404,
                message='任务不存在'
            )

        from .tasks import _running_timers

        # 清理该任务的定时器
        if str(task.id) in _running_timers:
            # 记录日志
            logger.info(f"收到前端停止轮询请求: task_id={task.id}")

            # 从定时器字典中删除
            del _running_timers[str(task.id)]

        return api_response(
            message='已停止轮询',
            data={'success': True}
        )

    @action(detail=True, methods=['get'])
    async def differences(self, request, pk=None):
        """
        查询任务差异记录 - 使用增强的文件查询服务

        支持的查询参数：
        - page: 页码 (默认: 1)
        - size: 每页大小 (默认: 20)
        - diff_type: 差异类型 (source_only, target_only, differences)
        - keyword: 关键字搜索
        - field_name: 字段名过滤
        - sort_by: 排序字段 (默认: key)
        - sort_order: 排序顺序 (asc, desc)
        """
        try:
            task = self.get_object()

            # 解析查询参数
            page = int(request.GET.get('page', 1))
            size = int(request.GET.get('size', 20))
            diff_type = request.GET.get('diff_type')
            keyword = request.GET.get('keyword')
            field_name = request.GET.get('field_name')

            # 使用统一的差异检索服务
            from .services import DiffRetrievalService

            # 执行查询
            result = DiffRetrievalService.get_task_differences(
                task.external_id or str(task.id),
                page=page,
                size=size,
                diff_type=diff_type,
                keyword=keyword,
                field_name=field_name
            )

            if result['success']:
                return api_response(
                    message='查询差异记录成功',
                    data={
                        'items': result['data'],
                        'pagination': {
                            'page': result['page'],
                            'size': result['size'],
                            'total': result['total'],
                            'pages': result['pages'],
                            'has_next': result.get('has_next', False),
                            'has_prev': result.get('has_prev', False)
                        },
                        'query_info': {
                            'query_time_ms': result.get('query_time_ms', 0),
                            'cache_hit': result.get('cache_hit', False),
                            'data_source': result.get('source', 'database')
                        }
                    }
                )
            else:
                return error_response(
                    code=500,
                    message=f"查询差异记录失败: {result.get('error', '未知错误')}"
                )

        except Exception as e:
            logger.error(f"查询任务差异失败 - 任务ID: {pk}, 错误: {str(e)}")
            return error_response(
                code=500,
                message=f"查询差异记录失败: {str(e)}"
            )

    @action(detail=True, methods=['get'])
    def download_file(self, request, pk=None):
        """
        下载差异文件

        支持的文件类型：
        - source_only: 仅源表存在的记录
        - target_only: 仅目标表存在的记录
        - differences: 字段值差异记录
        - summary: 比对摘要

        支持的格式：
        - jsonl: 原始JSONL格式 (默认)
        - csv: CSV格式
        - excel: Excel格式
        """
        try:
            task = self.get_object()
            file_type = request.GET.get('file_type', 'differences')
            format_type = request.GET.get('format', 'jsonl')

            # 验证文件类型
            valid_file_types = ['source_only', 'target_only', 'differences', 'summary']
            if file_type not in valid_file_types:
                return error_response(
                    code=400,
                    message=f"无效的文件类型，支持的类型: {', '.join(valid_file_types)}"
                )

            # 验证格式类型
            valid_formats = ['jsonl', 'csv', 'excel']
            if format_type not in valid_formats:
                return error_response(
                    code=400,
                    message=f"无效的格式类型，支持的格式: {', '.join(valid_formats)}"
                )

            # 获取任务ID
            task_id = task.external_id or str(task.id)

            # 构建文件路径
            from pathlib import Path
            from django.conf import settings

            comparison_results_path = Path(getattr(settings, 'COMPARISON_RESULTS_PATH', 'comparison_results'))
            workspace_path = comparison_results_path / task_id
            data_dir = workspace_path / 'data'

            if file_type == 'summary':
                file_path = data_dir / 'summary.json'
            else:
                file_path = data_dir / f"{file_type}.jsonl"

            if not file_path.exists():
                return error_response(
                    code=404,
                    message=f"文件不存在: {file_type}"
                )

            # 根据格式类型处理文件
            if format_type == 'jsonl':
                # 直接返回JSONL文件
                from django.http import FileResponse
                response = FileResponse(
                    open(file_path, 'rb'),
                    content_type='application/x-jsonlines'
                )
                response['Content-Disposition'] = f'attachment; filename="{task_id}_{file_type}.jsonl"'
                return response

            elif format_type == 'csv':
                # 转换为CSV格式
                import csv
                import tempfile
                import json

                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
                    csv_writer = None

                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    record = json.loads(line)

                                    # 初始化CSV写入器
                                    if csv_writer is None:
                                        fieldnames = list(record.keys())
                                        csv_writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
                                        csv_writer.writeheader()

                                    # 扁平化嵌套字典
                                    flat_record = {}
                                    for key, value in record.items():
                                        if isinstance(value, dict):
                                            flat_record[key] = json.dumps(value, ensure_ascii=False)
                                        elif isinstance(value, list):
                                            flat_record[key] = json.dumps(value, ensure_ascii=False)
                                        else:
                                            flat_record[key] = value

                                    csv_writer.writerow(flat_record)

                                except json.JSONDecodeError:
                                    continue

                from django.http import FileResponse
                response = FileResponse(
                    open(temp_file.name, 'rb'),
                    content_type='text/csv'
                )
                response['Content-Disposition'] = f'attachment; filename="{task_id}_{file_type}.csv"'
                return response

            elif format_type == 'excel':
                # 转换为Excel格式
                import pandas as pd
                import tempfile
                import json

                records = []
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                record = json.loads(line)

                                # 扁平化嵌套字典
                                flat_record = {}
                                for key, value in record.items():
                                    if isinstance(value, dict):
                                        flat_record[key] = json.dumps(value, ensure_ascii=False)
                                    elif isinstance(value, list):
                                        flat_record[key] = json.dumps(value, ensure_ascii=False)
                                    else:
                                        flat_record[key] = value

                                records.append(flat_record)

                            except json.JSONDecodeError:
                                continue

                if records:
                    df = pd.DataFrame(records)

                    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                        df.to_excel(temp_file.name, index=False, engine='openpyxl')

                        from django.http import FileResponse
                        response = FileResponse(
                            open(temp_file.name, 'rb'),
                            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )
                        response['Content-Disposition'] = f'attachment; filename="{task_id}_{file_type}.xlsx"'
                        return response
                else:
                    return error_response(
                        code=404,
                        message="文件中没有有效的数据记录"
                    )

        except Exception as e:
            logger.error(f"下载文件失败 - 任务ID: {pk}, 错误: {str(e)}")
            return error_response(
                code=500,
                message=f"下载文件失败: {str(e)}"
            )

    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """
        获取任务统计信息

        返回任务的详细统计数据，包括：
        - 总记录数
        - 各类型差异数量
        - 字段统计信息
        - 趋势数据
        """
        try:
            task = self.get_object()
            task_id = task.external_id or str(task.id)

            # 获取统计信息（从任务记录中获取）
            stats = {
                'total_records': 0,
                'source_only_count': 0,
                'target_only_count': 0,
                'differences_count': 0,
                'matched_count': 0,
                'field_stats': {},
                'trend_data': []
            }

            # 从任务记录中获取统计数据
            if hasattr(task, 'total_records') and task.total_records:
                stats['total_records'] = task.total_records
            if hasattr(task, 'different_records') and task.different_records:
                stats['differences_count'] = task.different_records
            if hasattr(task, 'matched_records') and task.matched_records:
                stats['matched_count'] = task.matched_records

            return api_response(
                message='获取统计信息成功',
                data=stats
            )

        except Exception as e:
            logger.error(f"获取任务统计失败 - 任务ID: {pk}, 错误: {str(e)}")
            return error_response(
                code=500,
                message=f"获取统计信息失败: {str(e)}"
            )

    @action(detail=True, methods=['get'])
    def fields(self, request, pk=None):
        """
        获取任务可用字段列表

        返回任务中所有可用的字段名称和类型信息
        """
        try:
            task = self.get_object()
            task_id = task.external_id or str(task.id)

            # 从数据库中获取字段信息（简化版）
            fields = set()
            field_types = {}

            # 从任务的差异记录中获取字段信息
            try:
                from .models import CompareDiff
                sample_diffs = CompareDiff.objects.filter(task=task)[:50]

                for diff in sample_diffs:
                    if diff.context and isinstance(diff.context, dict):
                        # 从源记录中提取字段
                        source_record = diff.context.get('source_record', {})
                        if isinstance(source_record, dict):
                            for field, value in source_record.items():
                                fields.add(field)
                                if field not in field_types:
                                    field_types[field] = type(value).__name__

                        # 从目标记录中提取字段
                        target_record = diff.context.get('target_record', {})
                        if isinstance(target_record, dict):
                            for field, value in target_record.items():
                                fields.add(field)
                                if field not in field_types:
                                    field_types[field] = type(value).__name__
            except Exception as e:
                logger.warning(f"从数据库获取字段信息失败: {str(e)}")
                # 提供默认字段
                fields = {'id', 'name', 'value', 'status'}
                field_types = {'id': 'int', 'name': 'str', 'value': 'str', 'status': 'str'}

            return api_response(
                message='获取字段列表成功',
                data={
                    'fields': sorted(list(fields)),
                    'field_types': field_types
                }
            )

        except Exception as e:
            logger.error(f"获取字段列表失败 - 任务ID: {pk}, 错误: {str(e)}")
            return error_response(
                code=500,
                message=f"获取字段列表失败: {str(e)}"
            )

    @action(detail=True, methods=['get'])
    def preview_file(self, request, pk=None):
        """
        预览差异文件内容

        支持的文件类型：
        - source_only: 仅源表存在的记录
        - target_only: 仅目标表存在的记录
        - differences: 字段值差异记录
        - summary: 比对摘要

        支持的查询参数：
        - file_type: 文件类型 (默认: differences)
        - page: 页码 (默认: 1)
        - size: 每页大小 (默认: 50)
        """
        try:
            task = self.get_object()
            file_type = request.GET.get('file_type', 'differences')
            page = int(request.GET.get('page', 1))
            size = int(request.GET.get('size', 50))

            # 验证文件类型
            valid_file_types = ['source_only', 'target_only', 'differences', 'summary']
            if file_type not in valid_file_types:
                return error_response(
                    code=400,
                    message=f"无效的文件类型，支持的类型: {', '.join(valid_file_types)}"
                )

            # 简化的预览实现（从数据库获取）
            task_id = task.external_id or str(task.id)

            # 从数据库获取差异记录进行预览
            try:
                from .models import CompareDiff

                # 根据文件类型筛选
                queryset = CompareDiff.objects.filter(task=task)
                if file_type != 'summary':
                    queryset = queryset.filter(diff_type=file_type)

                # 分页
                total = queryset.count()
                start = (page - 1) * size
                end = start + size
                items = queryset[start:end]

                # 序列化数据
                from .serializers import CompareDiffSerializer
                serializer = CompareDiffSerializer(items, many=True)

                pages = (total + size - 1) // size if total > 0 else 0

                return api_response(
                    message='预览文件成功',
                    data={
                        'items': serializer.data,
                        'pagination': {
                            'page': page,
                            'size': size,
                            'total': total,
                            'pages': pages
                        },
                        'file_info': {
                            'file_type': file_type,
                            'source': 'database'
                        }
                    }
                )
            except Exception as e:
                logger.warning(f"从数据库预览失败: {str(e)}")
                return api_response(
                    message='预览文件成功',
                    data={
                        'items': [],
                        'pagination': {
                            'page': page,
                            'size': size,
                            'total': 0,
                            'pages': 0
                        },
                        'file_info': {
                            'file_type': file_type,
                            'error': '暂无数据'
                        }
                    }
                )

        except Exception as e:
            logger.error(f"预览文件失败 - 任务ID: {pk}, 错误: {str(e)}")
            return error_response(
                code=500,
                message=f"预览文件失败: {str(e)}"
            )


class CompareDiffViewSet(viewsets.ReadOnlyModelViewSet):
    """比对差异视图集"""
    queryset = CompareDiff.objects.all()
    serializer_class = CompareDiffSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['task', 'table_name', 'diff_type', 'severity']
    search_fields = ['field_name', 'record_id', 'source_value', 'target_value']
    ordering_fields = ['id', 'create_time']
    ordering = ['-create_time']

    def get_queryset(self):
        queryset = super().get_queryset()

        # 根据任务ID筛选
        task_id = self.kwargs.get('task_pk')
        if task_id:
            queryset = queryset.filter(task_id=task_id)

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return api_response(
                message='获取比对差异列表成功',
                data=serializer.data,
                total=queryset.count()
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message='获取比对差异列表成功',
            data=serializer.data,
            total=queryset.count()
        )


class DataConnectionViewSet(viewsets.ModelViewSet):
    """数据库连接视图集"""
    queryset = DataConnection.objects.all()
    serializer_class = DataConnectionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status']
    search_fields = ['name', 'host', 'database']
    ordering_fields = ['id', 'name', 'create_time', 'update_time']
    ordering = ['-create_time']

    def list(self, request, *args, **kwargs):
        """获取数据库连接列表"""
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return api_response(
                message='获取连接列表成功',
                data=serializer.data,
                total=queryset.count()
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message='获取连接列表成功',
            data=serializer.data,
            total=queryset.count()
        )

    def create(self, request, *args, **kwargs):
        """创建数据库连接"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return api_response(
                message='创建成功',
                data=serializer.data,
                headers=headers
            )
        except Exception as e:
            return error_response(
                code=400,
                message=f"创建数据库连接失败: {str(e)}"
            )

    def update(self, request, *args, **kwargs):
        """更新数据库连接"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        try:
            # 检查是否包含密码字段
            if 'password' not in request.data or not request.data.get('password'):
                # 获取请求数据的可变副本
                mutable_data = request.data.copy()

                # 注：DRF请求对象不一定有_mutable属性，所以我们创建一个新的数据副本
                # 将原密码回填到请求数据中
                mutable_data['password'] = instance.password

                # 使用修改后的数据创建序列化器
                serializer = self.get_serializer(instance, data=mutable_data, partial=partial)
            else:
                serializer = self.get_serializer(instance, data=request.data, partial=partial)

            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return api_response(
                message='更新成功',
                data=serializer.data
            )
        except Exception as e:
            return error_response(
                code=400,
                message=f"更新数据库连接失败: {str(e)}"
            )

    @action(detail=False, methods=['post'])
    def test(self, request):
        """
        测试数据库连接 (通过 DB-Agent 服务)。
        将请求转发给配置好的 DB-Agent 服务来执行实际的连接测试。
        """
        try:
            # 从请求中获取数据库连接配置
            connection_config = request.data.copy()

            # 检查是否提供了ID且需要获取完整凭证
            if connection_config.get('id') and (not connection_config.get('password') or connection_config.get('get_credentials')):
                try:
                    # 根据ID查询完整的数据库连接配置
                    connection = DataConnection.objects.get(id=connection_config.get('id'))
                    # 更新配置，确保使用数据库中存储的密码
                    connection_config.update({
                        "type": connection.type,
                        "host": connection.host,
                        "port": connection.port,
                        "username": connection.username,
                        "password": connection.password,
                        "database": connection.database
                    })

                    # 添加自定义参数
                    if connection.parameters:
                        connection_config.update(connection.parameters)
                except DataConnection.DoesNotExist:
                    logger.warning(f"未找到ID={connection_config.get('id')}的数据库连接")
                except Exception as e:
                    logger.error(f"获取完整凭证时出错: {str(e)}")

            # 去除不必要的字段，避免干扰测试
            for field in ['get_credentials', 'id', 'savePassword']:
                if field in connection_config:
                    connection_config.pop(field)

            # 对不同类型的数据库连接进行特殊处理
            db_type = connection_config.get('type', '').lower()

            # PostgreSQL特殊处理
            if db_type == 'postgresql':
                # 对于PostgreSQL，移除parameters字段
                if 'parameters' in connection_config:
                    connection_config.pop('parameters')

            # MySQL特殊处理
            elif db_type == 'mysql':
                # 处理特殊参数
                mysql_params = {}
                for param in ['charset', 'autocommit', 'ssl']:
                    if param in connection_config.get('parameters', {}):
                        mysql_params[param] = connection_config['parameters'][param]
                # 添加MySQL特有参数
                connection_config.update(mysql_params)

            # Oracle特殊处理
            elif db_type == 'oracle':
                # 处理特殊参数
                oracle_params = {}
                for param in ['service_name', 'sid', 'mode', 'encoding']:
                    if param in connection_config.get('parameters', {}):
                        oracle_params[param] = connection_config['parameters'][param]
                # 添加Oracle特有参数
                connection_config.update(oracle_params)

            # 使用DB-Agent客户端发送测试请求
            response_data = db_agent_client.test_connection(connection_config)

            # 记录测试结果
            if response_data.get('success', False):
                logger.info(f"连接测试成功: {response_data.get('message', '')}")
                return api_response(
                    message=response_data.get('message', '连接测试成功'),
                    data=response_data
                )
            else:
                logger.warning(f"连接测试失败: {response_data.get('message', '')}")
                return error_response(
                    code=400,
                    message=response_data.get('message', '连接测试失败'),
                    data=response_data
                )
        except Exception as e:
            # 记录异常
            logger.exception(f"处理连接测试请求时发生异常: {str(e)}")
            return error_response(
                code=500,
                message=f"处理连接测试请求时发生异常: {str(e)}"
            )

    @action(detail=False, methods=['get'])
    def debug(self, request):
        """接口调试端点，返回一些测试数据和系统状态"""
        # 检查数据库连接
        connections_count = DataConnection.objects.count()

        # 创建一个示例连接记录返回
        sample_data = {
            "id": 999,
            "name": "测试连接",
            "type": "mysql",
            "type_display": "MySQL",
            "host": "localhost",
            "port": 3306,
            "username": "test_user",
            "database": "test_db",
            "status": "active",
            "status_display": "正常",
            "createTime": "2023-01-01 00:00:00",
            "updateTime": "2023-01-01 00:00:00"
        }

        # 返回调试信息
        return api_response(
            message="调试接口正常",
            data={
                "database_connections": connections_count,
                "sample_data": sample_data,
                "api_url": request.build_absolute_uri(),
                "timestamp": timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )

    @action(detail=False, methods=['post'])
    def create_sample(self, request):
        """创建样例数据库连接记录，方便前端测试"""
        try:
            # 检查是否已经存在同名连接
            name = "示例连接-MySQL"
            existing = DataConnection.objects.filter(name=name).first()
            if existing:
                logger.info(f"样例连接已存在: {name}")
                serializer = self.get_serializer(existing)
                return api_response(
                    message="样例连接已存在",
                    data=serializer.data
                )

            # 创建样例连接
            sample_connection = DataConnection(
                name=name,
                type="mysql",
                host="localhost",
                port=3306,
                username="root",
                password="password",
                database="test_db",
                status="active"
            )
            sample_connection.save()

            # 创建第二个样例
            name2 = "示例连接-PostgreSQL"
            if not DataConnection.objects.filter(name=name2).exists():
                sample_connection2 = DataConnection(
                    name=name2,
                    type="postgresql",
                    host="localhost",
                    port=5432,
                    username="postgres",
                    password="password",
                    database="test_db",
                    status="active"
                )
                sample_connection2.save()

            # 返回创建的连接数据
            logger.info(f"成功创建样例连接: {name}")
            serializer = self.get_serializer(sample_connection)
            return api_response(
                message="样例连接创建成功",
                data=serializer.data
            )

        except Exception as e:
            logger.error(f"创建样例连接失败: {str(e)}")
            return error_response(
                code=500,
                message=f"创建样例连接失败: {str(e)}"
            )

    def destroy(self, request, *args, **kwargs):
        """删除数据库连接"""
        try:
            instance = self.get_object()
            name = instance.name
            self.perform_destroy(instance)
            logger.info(f"数据库连接删除成功: {name}")
            return api_response(
                message='删除成功'
            )
        except Exception as e:
            logger.error(f"删除数据库连接失败: {str(e)}")
            return error_response(
                code=400,
                message=f"删除数据库连接失败: {str(e)}"
            )


# 错误报告API - 兼容DB-Agent的错误报告接口

@api_view(['POST'])
@permission_classes([])  # 允许DB-Agent无需认证访问
@authentication_classes([])  # 跳过认证
def task_error_report(request, task_id):
    """
    接收DB-Agent发送的任务错误报告

    此接口兼容DB-Agent的错误报告格式，将错误信息保存到任务记录中
    """
    try:
        logger.info(f"收到DB-Agent任务错误报告: task_id={task_id}")

        # 尝试查找对应的任务
        try:
            # 先尝试通过external_id查找
            task = CompareTask.objects.filter(external_id=task_id).first()

            # 如果找不到，尝试通过id查找
            if not task:
                task = CompareTask.objects.filter(id=task_id).first()

            if not task:
                logger.warning(f"未找到对应的任务记录: {task_id}")
                return api_response(message="未找到对应的任务记录", success=False, status=404)
        except Exception as e:
            logger.error(f"查找任务记录失败: {str(e)}")
            # 使用错误处理工具记录错误
            error_report = format_error(e, context={"task_id": task_id, "api": "task_error_report"})
            logger.error(f"错误详情: {error_report}")
            return api_response(message=f"查找任务记录失败: {str(e)}", success=False, status=500)

        # 解析请求数据
        try:
            error_data = request.data
            logger.info(f"错误报告数据: {error_data}")

            # 提取错误信息
            error_message = error_data.get('message', '未知错误')
            error_type = error_data.get('type', 'UnknownError')
            error_details = error_data.get('details', {})
            error_category = error_data.get('category', 'unknown')
            error_severity = error_data.get('severity', 'error')
            is_fatal = error_data.get('is_fatal', False)

            # 更新任务状态
            task.status = 'failed'
            task.message = f"任务失败: {error_message}"

            # 如果任务没有结果字段，创建一个
            if not task.result:
                task.result = {}

            # 添加错误信息到结果中
            task.result['error'] = {
                'type': error_type,
                'message': error_message,
                'details': error_details,
                'category': error_category,
                'severity': error_severity,
                'is_fatal': is_fatal,
                'reported_at': timezone.now().isoformat()
            }

            # 如果任务有开始时间但没有结束时间，设置结束时间
            if task.start_time and not task.end_time:
                task.end_time = timezone.now()

            # 保存任务
            task.save()

            # 发送WebSocket通知
            send_task_update(task)

            # 使用错误通知工具发送通知
            notify_error(task.result['error'], task.id)

            logger.info(f"已更新任务状态为失败: task_id={task.id}, external_id={task.external_id}")

            return api_response(message="错误报告已处理", success=True)
        except Exception as e:
            logger.error(f"处理错误报告数据失败: {str(e)}")
            # 使用错误处理工具记录错误
            error_report = format_error(e, context={"task_id": task_id, "api": "task_error_report"})
            logger.error(f"错误详情: {error_report}")
            return api_response(message=f"处理错误报告数据失败: {str(e)}", success=False, status=500)
    except Exception as e:
        logger.exception(f"处理任务错误报告时发生异常: {str(e)}")
        # 使用错误处理工具记录错误
        error_report = format_error(e, context={"task_id": task_id, "api": "task_error_report"})
        logger.error(f"错误详情: {error_report}")
        return api_response(message=f"处理任务错误报告时发生异常: {str(e)}", success=False, status=500)


@api_view(['POST'])
@permission_classes([])  # 允许DB-Agent无需认证访问
@authentication_classes([])  # 跳过认证
def task_status_update(request, task_id):
    """
    接收DB-Agent发送的任务状态更新通知

    此接口接收任务状态变更通知，更新任务记录并通过WebSocket推送给前端
    """
    try:
        logger.info(f"收到DB-Agent任务状态更新通知: task_id={task_id}")

        # 尝试查找对应的任务
        try:
            # 先尝试通过external_id查找
            task = CompareTask.objects.filter(external_id=task_id).first()

            # 如果找不到，尝试通过id查找
            if not task:
                task = CompareTask.objects.filter(id=task_id).first()

            if not task:
                logger.warning(f"未找到对应的任务记录: {task_id}")
                return api_response(message="未找到对应的任务记录", success=False, status=404)
        except Exception as e:
            logger.error(f"查找任务记录失败: {str(e)}")
            # 使用错误处理工具记录错误
            error_report = format_error(e, context={"task_id": task_id, "api": "task_status_update"})
            logger.error(f"错误详情: {error_report}")
            return api_response(message=f"查找任务记录失败: {str(e)}", success=False, status=500)

        # 解析请求数据
        try:
            status_data = request.data
            logger.info(f"状态更新数据: {status_data}")

            # 提取状态信息 - 支持多种数据结构
            raw_status = status_data.get('status', '').lower().strip()

            # 如果顶层没有状态，尝试从data字段中获取
            if not raw_status and 'data' in status_data and status_data['data']:
                data_field = status_data['data']
                raw_status = data_field.get('status', '').lower().strip()
                logger.debug(f"从data字段获取状态: {raw_status}")

            # 提取进度信息
            progress = status_data.get('progress', task.progress)
            if progress == task.progress and 'data' in status_data and status_data['data']:
                data_field = status_data['data']
                progress = data_field.get('progress', progress)
                logger.debug(f"从data字段获取进度: {progress}")

            # 提取统计信息
            stats = status_data.get('stats', {})
            if not stats and 'data' in status_data and status_data['data']:
                data_field = status_data['data']
                stats = data_field.get('stats', {})
                logger.debug(f"从data字段获取统计信息: {bool(stats)}")

            logger.info(f"状态提取结果: raw_status={raw_status}, progress={progress}, stats_count={len(stats)}")

            # 状态映射：将Agent的completed状态映射为前端的success状态
            status = raw_status
            if raw_status == 'completed':
                status = 'success'
                logger.info(f"将Agent状态completed映射为前端状态success: task_id={task_id}")
            elif not raw_status and progress >= 100:
                # 如果状态为空但进度100%，设置为success
                status = 'success'
                logger.info(f"状态为空但进度100%，设置状态为success: task_id={task_id}")
            elif not raw_status:
                # 如果状态为空且进度未完成，保持当前状态或设置为running
                status = task.status if task.status else 'running'
                logger.warning(f"状态为空，保持当前状态: task_id={task_id}, status={status}")

            # 生成消息，确保状态不为空
            if status_data.get('message'):
                message = status_data.get('message')
            else:
                message = f'任务状态已更新为 {status}' if status else '任务状态更新中'

            # 更新任务状态
            old_status = task.status
            task.status = status
            task.progress = progress
            task.message = message

            # 更新统计数据
            if stats:
                task.total_records = stats.get('source_count', task.total_records)
                task.processed_records = stats.get('processed_count', task.processed_records)
                task.matched_records = stats.get('match_count', task.matched_records)
                task.different_records = stats.get('diff_count', task.different_records)

            # 检查任务是否完成
            terminal_states = ['success', 'failed', 'stopped', 'completed', 'error', 'cancelled']
            if status in terminal_states and old_status not in terminal_states:
                # 任务刚完成，设置结束时间并确保数据持久化
                task.end_time = timezone.now()
                logger.info(f"任务完成，设置结束时间: task_id={task_id}, status={status}")

                # 如果有结果数据，保存到result字段
                result_data = status_data.get('result')
                if result_data:
                    if not hasattr(task, 'result') or task.result is None:
                        task.result = {}
                    task.result.update(result_data)
                    logger.info(f"保存任务结果数据: task_id={task_id}")

            # 如果任务已完成，确保不会再被轮询
            if status in terminal_states:
                # 清理轮询资源
                from .tasks import cleanup_task_resources
                cleanup_task_resources(task_id, task.external_id)

            # 如果任务已完成，设置结束时间
            if status in ['completed', 'success', 'failed', 'error', 'stopped', 'canceled']:
                if task.start_time and not task.end_time:
                    task.end_time = timezone.now()

            # 保存任务
            task.save()

            # 发送WebSocket通知
            send_task_update(task)

            # 如果任务刚完成，发送任务完成通知
            if status in terminal_states and old_status not in terminal_states:
                try:
                    # 使用同步方式发送任务完成通知
                    from utils.websocket_manager import websocket_manager

                    # 直接使用WebSocket管理器发送通知
                    websocket_manager.broadcast_task_update(task.external_id or str(task.id), {
                        'type': 'task_completed',
                        'task_id': task.external_id or str(task.id),
                        'status': status,
                        'message': '任务已完成',
                        'timestamp': timezone.now().isoformat()
                    })

                    logger.info(f"已发送任务完成通知: task_id={task.id}, status={status}")
                except Exception as e:
                    logger.error(f"发送任务完成通知失败: {str(e)}")

            logger.info(f"已更新任务状态: task_id={task.id}, external_id={task.external_id}, status={status}")

            return api_response(message="任务状态更新已处理", success=True)
        except Exception as e:
            logger.error(f"处理状态更新数据失败: {str(e)}")
            # 使用错误处理工具记录错误
            error_report = format_error(e, context={"task_id": task_id, "api": "task_status_update"})
            logger.error(f"错误详情: {error_report}")
            return api_response(message=f"处理状态更新数据失败: {str(e)}", success=False, status=500)
    except Exception as e:
        logger.exception(f"处理任务状态更新时发生异常: {str(e)}")
        # 使用错误处理工具记录错误
        error_report = format_error(e, context={"task_id": task_id, "api": "task_status_update"})
        logger.error(f"错误详情: {error_report}")
        return api_response(message=f"处理任务状态更新时发生异常: {str(e)}", success=False, status=500)


@api_view(['POST'])
@permission_classes([])  # 允许DB-Agent无需认证访问
@authentication_classes([])  # 跳过认证
def system_error_report(request):
    """
    接收DB-Agent发送的系统错误报告

    此接口兼容DB-Agent的系统错误报告格式，记录系统级错误
    """
    try:
        logger.info("收到DB-Agent系统错误报告")

        # 解析请求数据
        try:
            error_data = request.data
            logger.error(f"系统错误报告: {error_data}")

            # 提取错误信息
            error_message = error_data.get('message', '未知系统错误')
            error_type = error_data.get('type', 'SystemError')
            error_details = error_data.get('details', {})
            related_task = error_data.get('task_id')

            # 记录错误日志
            logger.error(f"DB-Agent系统错误: [{error_type}] {error_message}")
            if error_details:
                logger.error(f"错误详情: {error_details}")
            if related_task:
                logger.error(f"相关任务ID: {related_task}")

                # 如果有关联任务，尝试更新任务状态
                try:
                    task = CompareTask.objects.filter(external_id=related_task).first()
                    if task:
                        task.status = 'failed'
                        task.message = f"系统错误导致任务失败: {error_message}"

                        # 如果任务没有结果字段，创建一个
                        if not task.result:
                            task.result = {}

                        # 添加错误信息到结果中
                        task.result['error'] = {
                            'type': 'SystemError',
                            'message': error_message,
                            'details': error_details,
                            'reported_at': timezone.now().isoformat()
                        }

                        # 如果任务有开始时间但没有结束时间，设置结束时间
                        if task.start_time and not task.end_time:
                            task.end_time = timezone.now()

                        # 保存任务
                        task.save()

                        # 发送WebSocket通知
                        send_task_update(task)

                        logger.info(f"已更新相关任务状态为失败: task_id={task.id}, external_id={task.external_id}")
                except Exception as e:
                    logger.error(f"更新相关任务状态失败: {str(e)}")

            return api_response(message="系统错误报告已处理", success=True)
        except Exception as e:
            logger.error(f"处理系统错误报告数据失败: {str(e)}")
            return api_response(message=f"处理系统错误报告数据失败: {str(e)}", success=False, status=500)
    except Exception as e:
        logger.exception(f"处理系统错误报告时发生异常: {str(e)}")
        return api_response(message=f"处理系统错误报告时发生异常: {str(e)}", success=False, status=500)


@api_view(['GET'])
def task_cleanup_status(request):
    """
    获取任务清理状态

    返回任务清理服务的状态信息，包括运行状态、统计信息等。
    """
    try:
        from utils.task_cleanup_service import task_cleanup_service
        from utils.message_queue import message_queue
        from utils.websocket_manager import websocket_manager

        # 获取清理服务状态
        cleanup_stats = task_cleanup_service.get_stats()

        # 获取消息队列状态
        queue_stats = message_queue.get_queue_stats()

        # 获取WebSocket连接状态
        connection_stats = websocket_manager.get_connection_stats()

        # 获取任务详情
        task_details = []
        with message_queue.lock:
            sync_tasks = list(message_queue.last_sync_time.keys())
            queue_tasks = list(message_queue.queues.keys())
            all_tasks = set(sync_tasks + queue_tasks)

        for task_id in sorted(all_tasks):
            exists = message_queue.task_exists(task_id)
            has_connections = message_queue.has_active_connections(task_id)
            connections = websocket_manager.get_task_connections(task_id)

            task_details.append({
                'task_id': task_id,
                'exists': exists,
                'has_connections': has_connections,
                'connection_count': len(connections),
                'status': 'normal' if exists and has_connections else 'inactive'
            })

        return api_response(
            message='获取任务清理状态成功',
            data={
                'cleanup_service': cleanup_stats,
                'message_queue': queue_stats,
                'websocket_connections': connection_stats,
                'task_details': task_details,
                'summary': {
                    'total_tasks': len(all_tasks),
                    'active_tasks': len([t for t in task_details if t['status'] == 'normal']),
                    'inactive_tasks': len([t for t in task_details if t['status'] == 'inactive'])
                }
            }
        )

    except Exception as e:
        logger.error(f"获取任务清理状态失败: {str(e)}")
        return error_response(
            code=500,
            message=f"获取任务清理状态失败: {str(e)}"
        )


@api_view(['POST'])
def manual_task_cleanup(request):
    """
    手动执行任务清理

    支持清理指定任务或所有不活跃任务。
    """
    try:
        from utils.message_queue import message_queue

        task_id = request.data.get('task_id')
        force = request.data.get('force', False)
        clean_all = request.data.get('all', False)

        cleaned_tasks = []

        if task_id:
            # 清理指定任务
            if not message_queue.task_exists(task_id):
                message_queue.cleanup_task(task_id)
                cleaned_tasks.append({
                    'task_id': task_id,
                    'reason': 'task_not_exists'
                })
            elif force or not message_queue.has_active_connections(task_id):
                message_queue.cleanup_task(task_id)
                cleaned_tasks.append({
                    'task_id': task_id,
                    'reason': 'forced' if force else 'no_active_connections'
                })
            else:
                return error_response(
                    code=400,
                    message=f'任务 {task_id} 仍有活跃连接，使用 force=true 强制清理'
                )

        elif clean_all:
            # 清理所有不活跃任务
            with message_queue.lock:
                sync_tasks = list(message_queue.last_sync_time.keys())
                queue_tasks = list(message_queue.queues.keys())
                all_tasks = set(sync_tasks + queue_tasks)

            for tid in all_tasks:
                try:
                    if not message_queue.task_exists(tid):
                        message_queue.cleanup_task(tid)
                        cleaned_tasks.append({
                            'task_id': tid,
                            'reason': 'task_not_exists'
                        })
                    elif force or not message_queue.has_active_connections(tid):
                        message_queue.cleanup_task(tid)
                        cleaned_tasks.append({
                            'task_id': tid,
                            'reason': 'forced' if force else 'no_active_connections'
                        })
                except Exception as e:
                    logger.error(f"清理任务 {tid} 时发生错误: {str(e)}")
        else:
            return error_response(
                code=400,
                message='请指定 task_id 或设置 all=true'
            )

        return api_response(
            message=f'手动清理完成，共清理 {len(cleaned_tasks)} 个任务',
            data={
                'cleaned_tasks': cleaned_tasks,
                'count': len(cleaned_tasks)
            }
        )

    except Exception as e:
        logger.error(f"手动任务清理失败: {str(e)}")
        return error_response(
            code=500,
            message=f"手动任务清理失败: {str(e)}"
        )


# ===== 新增的比对结果API接口 =====

@csrf_exempt
@api_view(['GET'])
@permission_classes([])
@authentication_classes([])
def get_comparison_result(request, task_id):
    """获取指定任务的比对结果摘要"""
    try:
        # 查找任务
        task = CompareTask.objects.filter(external_id=task_id).first()
        if not task:
            task = CompareTask.objects.filter(id=task_id).first()

        if not task:
            return api_response(
                success=False,
                message='任务不存在',
                code=404
            )

        # 检查任务是否已完成
        if task.status not in ['success', 'failed']:
            return api_response(
                success=False,
                message=f'任务尚未完成，当前状态: {task.status}',
                code=400
            )

        # 构建摘要数据
        summary_data = {
            'taskId': task.external_id or str(task.id),
            'name': task.name or f'任务-{task.id}',
            'status': task.status,
            'summary': {
                'totalRecords': task.total_records,
                'matchedRecords': task.matched_records,
                'diffRecords': task.different_records,
                'sourceOnlyRecords': task.source_only_records,
                'targetOnlyRecords': task.target_only_records,
                'errorRecords': task.error_records,
                'executionTime': task.execution_time,
            },
            'startTime': task.start_time.isoformat() if task.start_time else None,
            'endTime': task.end_time.isoformat() if task.end_time else None,
        }

        return api_response(
            message='获取比对结果成功',
            data=summary_data
        )

    except Exception as e:
        logger.error(f"获取比对结果失败: {str(e)}")
        return api_response(
            success=False,
            message=f'获取比对结果失败: {str(e)}',
            code=500
        )


@csrf_exempt
@api_view(['GET'])
@permission_classes([])
@authentication_classes([])
def get_differences(request, task_id):
    """分页获取差异记录列表"""
    try:
        # 查找任务
        task = CompareTask.objects.filter(external_id=task_id).first()
        if not task:
            task = CompareTask.objects.filter(id=task_id).first()

        if not task:
            return api_response(
                success=False,
                message='任务不存在',
                code=404
            )

        # 获取分页参数（支持size和page_size两种格式）
        page = int(request.GET.get('page', 1))
        size = int(request.GET.get('size', request.GET.get('page_size', 20)))

        # 获取差异记录
        differences = CompareDiff.objects.filter(task=task).order_by('-create_time')

        # 分页处理
        from django.core.paginator import Paginator
        paginator = Paginator(differences, size)
        page_obj = paginator.get_page(page)

        # 构建返回数据
        results = []
        for diff in page_obj:
            results.append({
                'id': diff.id,
                'tableName': diff.table_name,
                'fieldName': diff.field_name,
                'recordId': diff.record_id,
                'diffType': diff.diff_type,
                'severity': diff.severity,
                'sourceValue': diff.source_value,
                'targetValue': diff.target_value,
                'context': diff.context,
                'createTime': diff.create_time.isoformat(),
            })

        return api_response(
            message='获取差异记录成功',
            data={
                'results': results,
                'total': paginator.count,
                'page': page,
                'size': size,
            }
        )

    except Exception as e:
        logger.error(f"获取差异记录失败: {str(e)}")
        return api_response(
            success=False,
            message=f'获取差异记录失败: {str(e)}',
            code=500
        )


@csrf_exempt
@api_view(['GET'])
@permission_classes([])
@authentication_classes([])
def get_source_only_records(request, task_id):
    """分页获取源独有记录"""
    try:
        # 查找任务
        task = CompareTask.objects.filter(external_id=task_id).first()
        if not task:
            task = CompareTask.objects.filter(id=task_id).first()

        if not task:
            return api_response(
                success=False,
                message='任务不存在',
                code=404
            )

        # 获取分页参数（支持size和page_size两种格式）
        page = int(request.GET.get('page', 1))
        size = int(request.GET.get('size', request.GET.get('page_size', 20)))

        # 获取源独有记录
        source_only = SourceOnlyRecord.objects.filter(task=task).order_by('-create_time')

        # 分页处理
        from django.core.paginator import Paginator
        paginator = Paginator(source_only, size)
        page_obj = paginator.get_page(page)

        # 构建返回数据
        results = []
        for record in page_obj:
            results.append({
                'id': record.id,
                'tableName': record.table_name,
                'recordId': record.record_id,
                'data': record.data,
                'reason': record.reason,
                'createTime': record.create_time.isoformat(),
            })

        return api_response(
            message='获取源独有记录成功',
            data={
                'results': results,
                'total': paginator.count,
                'page': page,
                'size': size,
            }
        )

    except Exception as e:
        logger.error(f"获取源独有记录失败: {str(e)}")
        return api_response(
            success=False,
            message=f'获取源独有记录失败: {str(e)}',
            code=500
        )


@csrf_exempt
@api_view(['GET'])
@permission_classes([])
@authentication_classes([])
def get_target_only_records(request, task_id):
    """分页获取目标独有记录"""
    try:
        # 查找任务
        task = CompareTask.objects.filter(external_id=task_id).first()
        if not task:
            task = CompareTask.objects.filter(id=task_id).first()

        if not task:
            return api_response(
                success=False,
                message='任务不存在',
                code=404
            )

        # 获取分页参数（支持size和page_size两种格式）
        page = int(request.GET.get('page', 1))
        size = int(request.GET.get('size', request.GET.get('page_size', 20)))

        # 获取目标独有记录
        target_only = TargetOnlyRecord.objects.filter(task=task).order_by('-create_time')

        # 分页处理
        from django.core.paginator import Paginator
        paginator = Paginator(target_only, size)
        page_obj = paginator.get_page(page)

        # 构建返回数据
        results = []
        for record in page_obj:
            results.append({
                'id': record.id,
                'tableName': record.table_name,
                'recordId': record.record_id,
                'data': record.data,
                'reason': record.reason,
                'createTime': record.create_time.isoformat(),
            })

        return api_response(
            message='获取目标独有记录成功',
            data={
                'results': results,
                'total': paginator.count,
                'page': page,
                'size': size,
            }
        )

    except Exception as e:
        logger.error(f"获取目标独有记录失败: {str(e)}")
        return api_response(
            success=False,
            message=f'获取目标独有记录失败: {str(e)}',
            code=500
        )


@csrf_exempt
@api_view(['POST'])
@permission_classes([])
@authentication_classes([])
def sync_comparison_results(request):
    """接收Agent端同步的比对结果数据"""
    try:
        task_id = request.data.get('task_id')
        results_data = request.data.get('results', {})

        if not task_id:
            return api_response(
                success=False,
                message='缺少任务ID',
                code=400
            )

        # 查找任务
        task = CompareTask.objects.filter(external_id=task_id).first()
        if not task:
            return api_response(
                success=False,
                message='任务不存在',
                code=404
            )

        # 使用事务确保数据一致性
        from django.db import transaction
        with transaction.atomic():
            # 更新任务摘要信息
            summary = results_data.get('summary', {})
            task.total_records = summary.get('total_records', 0)
            task.matched_records = summary.get('matched_records', 0)
            task.different_records = summary.get('diff_records', 0)
            task.source_only_records = summary.get('source_only_records', 0)
            task.target_only_records = summary.get('target_only_records', 0)
            task.execution_time = summary.get('execution_time', 0.0)
            task.status = 'success'
            task.end_time = timezone.now()
            task.save()

            # 同步差异记录
            differences = results_data.get('differences', [])
            for diff_data in differences:
                CompareDiff.objects.create(
                    task=task,
                    table_name=diff_data.get('table_name', ''),
                    field_name=diff_data.get('field_name', ''),
                    record_id=diff_data.get('id', ''),
                    diff_type='value',
                    severity='medium',
                    source_value=diff_data.get('source_data', {}),
                    target_value=diff_data.get('target_data', {}),
                    context=diff_data.get('diff_fields', [])
                )

            # 同步源独有记录
            source_only = results_data.get('source_only', [])
            for record_data in source_only:
                SourceOnlyRecord.objects.create(
                    task=task,
                    table_name=record_data.get('table_name', ''),
                    record_id=record_data.get('id', ''),
                    data=record_data.get('data', {}),
                    reason=record_data.get('reason', '')
                )

            # 同步目标独有记录
            target_only = results_data.get('target_only', [])
            for record_data in target_only:
                TargetOnlyRecord.objects.create(
                    task=task,
                    table_name=record_data.get('table_name', ''),
                    record_id=record_data.get('id', ''),
                    data=record_data.get('data', {}),
                    reason=record_data.get('reason', '')
                )

        logger.info(f"成功同步任务 {task_id} 的比对结果")
        return api_response(message='同步成功')

    except Exception as e:
        logger.error(f"同步比对结果失败: {str(e)}")
        return api_response(
            success=False,
            message=f'同步失败: {str(e)}',
            code=500
        )
