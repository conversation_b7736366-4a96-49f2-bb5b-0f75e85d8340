"""
WebSocket连接管理模块

提供WebSocket连接管理功能，包括连接跟踪、消息广播、连接状态监控等。
"""
import json
import logging
import threading
import time
from typing import Dict, Any, List, Set, Optional, Callable
from datetime import datetime, timedelta
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone

logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    WebSocket连接管理类

    管理WebSocket连接，提供连接跟踪、消息广播、连接状态监控等功能。
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(WebSocketManager, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化WebSocket连接管理"""
        if self._initialized:
            return

        # 获取Channel Layer
        self.channel_layer = get_channel_layer()

        # 连接组映射表
        self.group_connections = {}

        # 任务连接映射表
        self.task_connections = {}

        # 连接状态映射表
        self.connection_status = {}

        # 连接计数器
        self.connection_count = 0

        # 消息计数器
        self.message_count = 0

        # 最近活动时间
        self.last_activity = timezone.now()

        # 初始化完成标志
        self._initialized = True

        # 启动后台监控线程
        self._start_background_thread()

    def _start_background_thread(self):
        """启动后台监控线程"""
        def monitor_connections():
            # 等待消息处理器注册完成
            self._wait_for_handlers()

            while True:
                try:
                    # 更新连接状态
                    self._update_connection_status()

                    # 发送心跳消息
                    self._send_heartbeat()

                    # 每30秒检查一次
                    time.sleep(30)
                except Exception as e:
                    logger.error(f"WebSocket连接监控线程异常: {str(e)}")
                    time.sleep(60)  # 发生异常时等待一分钟后重试

        # 创建后台线程
        thread = threading.Thread(target=monitor_connections, daemon=True)
        thread.start()

    def _wait_for_handlers(self):
        """等待消息处理器注册完成"""
        max_wait_time = 60  # 最大等待60秒
        wait_interval = 0.5  # 每0.5秒检查一次
        waited_time = 0

        while waited_time < max_wait_time:
            try:
                from .message_queue import message_queue
                if "notification" in message_queue.handlers and "task_status" in message_queue.handlers:
                    logger.debug("消息处理器已注册，后台监控线程开始工作")
                    return
            except Exception:
                pass

            time.sleep(wait_interval)
            waited_time += wait_interval

        logger.warning(f"等待消息处理器注册超时({max_wait_time}秒)，后台监控线程将继续工作")

    def _update_connection_status(self):
        """更新连接状态"""
        # 更新最近活动时间
        self.last_activity = timezone.now()

        # 记录连接状态
        logger.debug(f"当前WebSocket连接数: {self.connection_count}")
        logger.debug(f"当前任务连接数: {len(self.task_connections)}")
        logger.debug(f"当前组连接数: {len(self.group_connections)}")

    def _send_heartbeat(self):
        """发送心跳消息"""
        try:
            # 向所有任务组发送心跳消息
            for task_id in self.task_connections:
                self.send_task_message(task_id, {
                    'type': 'heartbeat',
                    'timestamp': timezone.now().isoformat()
                })

            # 向通知组发送心跳消息
            self.send_notification({
                'type': 'heartbeat',
                'timestamp': timezone.now().isoformat()
            })

            logger.debug("已发送WebSocket心跳消息")
        except Exception as e:
            logger.error(f"发送WebSocket心跳消息失败: {str(e)}")

    def register_connection(self, channel_name: str, group_name: str, task_id: Optional[str] = None):
        """
        注册WebSocket连接

        Args:
            channel_name: 通道名称
            group_name: 组名称
            task_id: 任务ID
        """
        # 更新组连接映射表
        if group_name not in self.group_connections:
            self.group_connections[group_name] = set()
        self.group_connections[group_name].add(channel_name)

        # 更新任务连接映射表
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = set()
            self.task_connections[task_id].add(channel_name)

        # 更新连接状态
        self.connection_status[channel_name] = {
            'connected_at': timezone.now().isoformat(),
            'last_activity': timezone.now().isoformat(),
            'group_name': group_name,
            'task_id': task_id
        }

        # 更新连接计数器
        self.connection_count += 1

        logger.info(f"WebSocket连接已注册: channel={channel_name}, group={group_name}, task_id={task_id}")

    def unregister_connection(self, channel_name: str):
        """
        注销WebSocket连接

        Args:
            channel_name: 通道名称
        """
        # 获取连接状态
        connection_info = self.connection_status.get(channel_name)
        if not connection_info:
            logger.warning(f"尝试注销未注册的WebSocket连接: {channel_name}")
            return

        # 从组连接映射表中移除
        group_name = connection_info.get('group_name')
        if group_name and group_name in self.group_connections:
            self.group_connections[group_name].discard(channel_name)
            # 如果组为空，移除组
            if not self.group_connections[group_name]:
                del self.group_connections[group_name]

        # 从任务连接映射表中移除
        task_id = connection_info.get('task_id')
        if task_id and task_id in self.task_connections:
            self.task_connections[task_id].discard(channel_name)
            # 如果任务没有连接，清理任务相关资源
            if not self.task_connections[task_id]:
                del self.task_connections[task_id]
                # 通知消息队列清理该任务
                self._cleanup_task_resources(task_id)

        # 从连接状态映射表中移除
        if channel_name in self.connection_status:
            del self.connection_status[channel_name]

        # 更新连接计数器
        self.connection_count -= 1

        logger.info(f"WebSocket连接已注销: channel={channel_name}, group={group_name}, task_id={task_id}")

    def _cleanup_task_resources(self, task_id: str):
        """
        清理任务相关资源

        Args:
            task_id: 任务ID
        """
        try:
            # 导入消息队列
            from .message_queue import message_queue

            # 清理消息队列中的任务
            message_queue.cleanup_task(task_id)

            logger.info(f"已清理任务 {task_id} 的相关资源")
        except Exception as e:
            logger.error(f"清理任务 {task_id} 资源时发生错误: {str(e)}")

    def update_connection_activity(self, channel_name: str):
        """
        更新连接活动时间

        Args:
            channel_name: 通道名称
        """
        if channel_name in self.connection_status:
            self.connection_status[channel_name]['last_activity'] = timezone.now().isoformat()

    def send_task_message(self, task_id: str, message: Dict[str, Any]):
        """
        发送任务消息

        Args:
            task_id: 任务ID
            message: 消息内容
        """
        try:
            # 导入消息队列
            from .message_queue import message_queue

            # 检查消息处理器是否已注册
            if "task_status" in message_queue.handlers:
                # 添加到消息队列
                message_queue.add_message(task_id, message, message_type="task_status")
            else:
                # 如果处理器未注册，记录调试信息但不报错
                logger.debug(f"任务状态处理器未注册，跳过队列添加: task_id={task_id}, message_type={message.get('type')}")

            # 尝试直接发送
            self._direct_send_task_message(task_id, message)

            # 更新消息计数器
            self.message_count += 1

            # 更新最近活动时间
            self.last_activity = timezone.now()

            logger.debug(f"已发送任务消息: task_id={task_id}, message_type={message.get('type')}")
            return True
        except Exception as e:
            logger.error(f"发送任务消息失败: {str(e)}")
            return False

    def _direct_send_task_message(self, task_id: str, message: Dict[str, Any]):
        """
        直接发送任务消息（不经过队列）

        Args:
            task_id: 任务ID
            message: 消息内容

        Returns:
            bool: 是否成功发送
        """
        try:
            # 构建任务组名称
            task_group_name = f'task_{task_id}'

            # 发送消息到任务组
            async_to_sync(self.channel_layer.group_send)(
                task_group_name,
                {
                    'type': 'task_status',
                    'content': message
                }
            )
            return True
        except Exception as e:
            logger.error(f"直接发送任务消息失败: {str(e)}")
            return False

    def send_notification(self, message: Dict[str, Any]):
        """
        发送通知消息

        Args:
            message: 消息内容
        """
        try:
            # 导入消息队列
            from .message_queue import message_queue

            # 检查消息处理器是否已注册
            if "notification" in message_queue.handlers:
                # 添加到消息队列（使用特殊任务ID表示全局通知）
                message_queue.add_message("global", message, message_type="notification")
            else:
                # 如果处理器未注册，记录调试信息但不报错
                logger.debug(f"通知处理器未注册，跳过队列添加: message_type={message.get('type')}")

            # 尝试直接发送
            self._direct_send_notification(message)

            # 更新消息计数器
            self.message_count += 1

            # 更新最近活动时间
            self.last_activity = timezone.now()

            logger.debug(f"已发送通知消息: message_type={message.get('type')}")
            return True
        except Exception as e:
            logger.error(f"发送通知消息失败: {str(e)}")
            return False

    def _direct_send_notification(self, message: Dict[str, Any]):
        """
        直接发送通知消息（不经过队列）

        Args:
            message: 消息内容

        Returns:
            bool: 是否成功发送
        """
        try:
            # 发送消息到通知组
            async_to_sync(self.channel_layer.group_send)(
                'task_notifications',
                {
                    'type': 'task_notification',
                    'content': message
                }
            )
            return True
        except Exception as e:
            logger.error(f"直接发送通知消息失败: {str(e)}")
            return False

    def broadcast_message(self, message: Dict[str, Any]):
        """
        广播消息到所有连接

        Args:
            message: 消息内容
        """
        try:
            # 发送消息到通知组
            self.send_notification(message)

            # 发送消息到所有任务组
            for task_id in self.task_connections:
                self.send_task_message(task_id, message)

            logger.debug(f"已广播消息: message_type={message.get('type')}")
            return True
        except Exception as e:
            logger.error(f"广播消息失败: {str(e)}")
            return False

    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息

        Returns:
            连接统计信息
        """
        return {
            'connection_count': self.connection_count,
            'task_connection_count': len(self.task_connections),
            'group_connection_count': len(self.group_connections),
            'message_count': self.message_count,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None
        }

    def get_task_connections(self, task_id: str) -> List[Dict[str, Any]]:
        """
        获取任务连接信息

        Args:
            task_id: 任务ID

        Returns:
            任务连接信息列表
        """
        if task_id not in self.task_connections:
            return []

        connections = []
        for channel_name in self.task_connections[task_id]:
            if channel_name in self.connection_status:
                connections.append(self.connection_status[channel_name])

        return connections

    def cleanup_task_connections(self, task_id: str):
        """
        清理指定任务的所有WebSocket连接

        Args:
            task_id: 任务ID
        """
        try:
            if task_id not in self.task_connections:
                logger.debug(f"任务 {task_id} 没有活跃的WebSocket连接")
                return

            # 获取任务的所有连接
            connections = self.task_connections[task_id].copy()
            logger.info(f"开始清理任务 {task_id} 的 {len(connections)} 个WebSocket连接")

            # 逐个关闭连接
            for channel_name in connections:
                try:
                    # 发送连接关闭通知
                    async_to_sync(self.channel_layer.send)(
                        channel_name,
                        {
                            'type': 'websocket.close',
                            'code': 1000,
                            'text': f'任务 {task_id} 已完成，关闭连接'
                        }
                    )

                    # 从连接映射中移除
                    self.unregister_connection(channel_name)
                    logger.debug(f"已关闭任务 {task_id} 的连接: {channel_name}")
                except Exception as e:
                    logger.warning(f"关闭连接 {channel_name} 失败: {str(e)}")

            # 清理任务连接记录
            if task_id in self.task_connections:
                del self.task_connections[task_id]

            # 清理任务相关资源
            self._cleanup_task_resources(task_id)

            logger.info(f"任务 {task_id} 的WebSocket连接清理完成")
        except Exception as e:
            logger.error(f"清理任务 {task_id} 的WebSocket连接时发生错误: {str(e)}")

# 定义发送任务更新的辅助函数
def send_task_update(task):
    """
    发送任务状态更新通知

    Args:
        task: 任务对象，必须包含id和status属性
    """
    try:
        # 导入序列化器
        from apps.database.serializers import CompareTaskSerializer

        # 序列化任务数据
        serializer = CompareTaskSerializer(task)
        task_data = serializer.data

        # 构建任务特定消息
        task_message = {
            'type': 'task_status',
            'content': {
                'type': 'task_status',
                'task': task_data
            }
        }

        # 构建通知消息
        notification_message = {
            'type': 'task_notification',
            'content': {
                'type': 'task_update',
                'task_id': str(task.id),
                'status': task.status,
                'progress': getattr(task, 'progress', 0),
                'message': getattr(task, 'message', ''),
                'timestamp': time.time()
            }
        }

        # 使用WebSocketManager发送任务消息
        websocket_manager.send_task_message(str(task.id), task_message['content'])

        # 同时发送通知消息
        websocket_manager.send_notification(notification_message['content'])

        logger.debug(f"已发送任务状态更新: task_id={task.id}, status={task.status}")
        return True
    except Exception as e:
        logger.error(f"发送任务状态更新失败: {str(e)}")
        return False

# 创建全局实例
try:
    websocket_manager = WebSocketManager()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"初始化WebSocket连接管理器失败: {str(e)}")

    # 创建一个简单的替代实例，避免导入错误
    class SimpleWebSocketManager:
        def register_connection(self, channel_name, group_name, task_id=None):
            logger.debug(f"注册WebSocket连接: {channel_name}, {group_name}, {task_id}")

        def unregister_connection(self, channel_name):
            logger.debug(f"注销WebSocket连接: {channel_name}")

        def update_connection_activity(self, channel_name):
            pass

        def send_task_message(self, task_id, message):
            logger.debug(f"发送任务消息: {task_id}, {message}")
            return True

        def send_notification(self, message):
            logger.debug(f"发送通知消息: {message}")
            return True

        def broadcast_message(self, message):
            logger.debug(f"广播消息: {message}")
            return True

        def get_connection_stats(self):
            return {"connection_count": 0}

        def get_task_connections(self, task_id):
            return []

        def cleanup_task_connections(self, task_id):
            logger.debug(f"清理任务WebSocket连接: {task_id}")

    websocket_manager = SimpleWebSocketManager()
