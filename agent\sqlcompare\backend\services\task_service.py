#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务服务层 - 纯委托模式，委托给SQLAlchemyComparisonService
"""

import os
import sys
import logging
from typing import List, Optional, Tuple, Dict, Any
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 直接使用主项目的服务和模型
from services.sqlalchemy_service import SQLAlchemyComparisonService
from models.pydantic_models import TaskCreateFromModel, TaskCreateDirect, TaskResponse
from models.sqlalchemy_models import TaskStatus

logger = logging.getLogger(__name__)


class TaskService:
    """任务服务类 - 纯委托模式"""

    def __init__(self, database_url: str = "sqlite:///./sqlcompare.db"):
        # 直接使用主项目的SQLAlchemy服务
        self.sqlalchemy_service = SQLAlchemyComparisonService(database_url)
        logger.info("任务服务初始化完成，委托给SQLAlchemyComparisonService")
        
    async def create_task_from_model(self, user_id: str, task_data: TaskCreateFromModel) -> str:
        """
        基于模型创建任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.create_task_from_model(user_id, task_data)
        except Exception as e:
            logger.error(f"基于模型创建任务失败: {e}")
            raise

    async def create_task_direct(self, user_id: str, task_data: TaskCreateDirect) -> str:
        """
        直接创建任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.create_task_direct(user_id, task_data)
        except Exception as e:
            logger.error(f"直接创建任务失败: {e}")
            raise

    async def get_tasks(self, user_id: str, status: Optional[str] = None, 
                       offset: int = 0, limit: int = 20) -> Tuple[List[Dict], int]:
        """
        获取任务列表 - 委托给SQLAlchemyComparisonService
        """
        try:
            tasks = self.sqlalchemy_service.get_tasks_by_user(user_id, status, limit, offset)
            total = len(tasks)  # 简化实现，实际应该获取总数
            return tasks, total
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return [], 0

    async def get_task(self, task_id: str) -> Optional[Dict]:
        """
        获取任务详情 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_task_summary(task_id)
        except Exception as e:
            logger.error(f"获取任务详情失败: {task_id}, {e}")
            return None

    async def get_task_progress(self, task_id: str) -> Optional[Dict]:
        """
        获取任务进度 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_task_progress(task_id)
        except Exception as e:
            logger.error(f"获取任务进度失败: {task_id}, {e}")
            return None

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.CANCELLED.value)
            return True
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, {e}")
            return False

    async def delete_task(self, task_id: str) -> bool:
        """
        删除任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.delete_task(task_id)
        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, {e}")
            return False

    def get_comparison_results(self, task_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取比对结果 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_comparison_results(task_id, limit, offset)
        except Exception as e:
            logger.error(f"获取比对结果失败: {task_id}, {e}")
            return []

    def get_task_statistics(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取任务统计 - 委托给SQLAlchemyComparisonService
        """
        try:
            # 这里可以扩展为获取用户特定的统计信息
            # 目前简化实现
            return {
                "total_tasks": 0,
                "pending_tasks": 0,
                "running_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "cancelled_tasks": 0
            }
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}
