<template>
  <div class="model-preview-container">
    <el-card class="model-info-card" shadow="hover">
      <template #header>
        <div class="model-info-header">
          <h3>
            <el-icon><Document /></el-icon>
            {{ model.name || '未命名模型' }}
          </h3>
          <el-tag type="success" effect="dark" size="small">{{ model.status === 'active' ? '启用中' : '未启用' }}</el-tag>
        </div>
      </template>

      <div class="model-info-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模型ID">{{ model.id || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(model.create_time) }}</el-descriptions-item>
          <el-descriptions-item label="表数量">{{ model.tables ? model.tables.length : 0 }}</el-descriptions-item>
          <el-descriptions-item label="最后修改">{{ formatTime(model.update_time) }}</el-descriptions-item>
        </el-descriptions>

        <div class="model-description" v-if="model.description">
          <p>{{ model.description }}</p>
        </div>
      </div>
    </el-card>

    <div class="model-details">
      <!-- 源数据库信息 -->
      <el-card class="db-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h4>
              <el-icon><DataBoard /></el-icon>
              源数据库
            </h4>
            <el-button
              type="primary"
              size="small"
              @click="testConnection('source')"
              :loading="sourceLoading"
            >
              测试连接
            </el-button>
          </div>
        </template>

        <el-descriptions :column="1" border size="small">
          <el-descriptions-item label="数据库类型">
            <el-tag v-if="getDbTypeColor(model.source_db?.type)" :type="getDbTypeColor(model.source_db?.type)">{{ getDbTypeName(model.source_db?.type) }}</el-tag>
            <el-tag v-else>{{ getDbTypeName(model.source_db?.type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务器">
            {{ model.source_db?.host }}:{{ model.source_db?.port }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库名称">
            {{ model.source_db?.database }}
          </el-descriptions-item>
          <el-descriptions-item label="连接用户">
            {{ model.source_db?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="连接参数">
            {{ model.source_db?.parameters || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 目标数据库信息 -->
      <el-card class="db-info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h4>
              <el-icon><DataBoard /></el-icon>
              目标数据库
            </h4>
            <el-button
              type="primary"
              size="small"
              @click="testConnection('target')"
              :loading="targetLoading"
            >
              测试连接
            </el-button>
          </div>
        </template>

        <el-descriptions :column="1" border size="small">
          <el-descriptions-item label="数据库类型">
            <el-tag v-if="getDbTypeColor(model.target_db?.type)" :type="getDbTypeColor(model.target_db?.type)">{{ getDbTypeName(model.target_db?.type) }}</el-tag>
            <el-tag v-else>{{ getDbTypeName(model.target_db?.type) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务器">
            {{ model.target_db?.host }}:{{ model.target_db?.port }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库名称">
            {{ model.target_db?.database }}
          </el-descriptions-item>
          <el-descriptions-item label="连接用户">
            {{ model.target_db?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="连接参数">
            {{ model.target_db?.parameters || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 映射表格信息 -->
    <el-card class="table-mappings-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h4>
            <el-icon><Grid /></el-icon>
            映射对比表
          </h4>
          <div class="header-actions">
            <el-input
              v-model="tableSearch"
              placeholder="搜索表名..."
              prefix-icon="Search"
              clearable
              style="width: 200px; margin-right: 10px;"
            />
            <el-switch
              v-model="showAllFields"
              active-text="显示全部字段"
              inactive-text="显示差异"
              style="margin-left: 10px;"
            />
          </div>
        </div>
      </template>

      <el-table
        :data="filteredTables"
        style="width: 100%"
        border
        stripe
        size="small"
        :max-height="400"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="source_table" label="源表" min-width="150">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.source_schema ? `${scope.row.source_schema}.${scope.row.source_table}` : scope.row.source_table"
              placement="top"
            >
              <span>{{ scope.row.source_table }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="target_table" label="目标表" min-width="150">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.target_schema ? `${scope.row.target_schema}.${scope.row.target_table}` : scope.row.target_table"
              placement="top"
            >
              <span>{{ scope.row.target_table }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="primary_keys" label="主键" min-width="150">
          <template #default="scope">
            <el-tag v-for="key in scope.row.primary_keys" :key="key" size="small" style="margin-right: 5px;">
              {{ key }}
            </el-tag>
            <el-tag v-if="!scope.row.primary_keys || scope.row.primary_keys.length === 0" type="danger" size="small">
              无主键
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="字段数量" width="100">
          <template #default="scope">
            {{ getFieldCount(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" link @click="viewFieldMappings(scope.row)">
              查看字段
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" v-if="model.tables && model.tables.length > 10">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredTables.length"
        />
      </div>
    </el-card>

    <!-- 字段映射对话框 -->
    <el-dialog
      v-model="fieldsDialogVisible"
      :title="`表映射详情: ${currentTable?.source_table || ''} → ${currentTable?.target_table || ''}`"
      width="80%"
      destroy-on-close
    >
      <div class="fields-filter" v-if="currentTable && currentTable.field_mappings">
        <el-input
          v-model="fieldSearch"
          placeholder="搜索字段名..."
          prefix-icon="Search"
          clearable
          style="width: 200px; margin-right: 10px;"
        />
        <el-radio-group v-model="fieldFilter" size="small">
          <el-radio-button value="all">全部</el-radio-button>
          <el-radio-button value="different">只看差异</el-radio-button>
          <el-radio-button value="missing">只看缺失</el-radio-button>
        </el-radio-group>
      </div>

      <el-table
        v-if="currentTable && currentTable.field_mappings"
        :data="filteredFields"
        style="width: 100%"
        border
        stripe
        size="small"
        :max-height="500"
      >
        <el-table-column prop="source_field" label="源字段" min-width="150" />
        <el-table-column prop="target_field" label="目标字段" min-width="150" />
        <el-table-column prop="source_type" label="源类型" min-width="120" />
        <el-table-column prop="target_type" label="目标类型" min-width="120" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.is_primary_key" type="success" effect="dark" size="small">主键</el-tag>
            <el-tag v-else-if="scope.row.source_type !== scope.row.target_type" type="warning" size="small">类型不同</el-tag>
            <el-tag v-else-if="scope.row.source_field !== scope.row.target_field" type="info" size="small">名称不同</el-tag>
            <el-tag v-else type="success" size="small">匹配</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="200">
          <template #default="scope">
            {{ scope.row.description || '无描述' }}
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!currentTable || !currentTable.field_mappings || currentTable.field_mappings.length === 0" class="no-data">
        <el-empty description="暂无字段映射数据" />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Document, DataBoard, Grid, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { testConnection as testDbConnection } from '@/api/database/connection'
import { parseSqlToTableMapping } from '@/utils/sql-utils'

// 属性定义
const props = defineProps({
  model: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 状态变量
const sourceLoading = ref(false);
const targetLoading = ref(false);
const tableSearch = ref('');
const fieldSearch = ref('');
const showAllFields = ref(true);
const fieldFilter = ref('all');
const currentPage = ref(1);
const pageSize = ref(10);
const fieldsDialogVisible = ref(false);
const currentTable = ref(null);

// 处理表格映射和字段映射
const processTables = computed(() => {
  if (!props.model || !props.model.tables) {
    return [];
  }

  return props.model.tables.map((table, index) => {
    // 使用SQL解析函数提取表映射信息
    const mappingInfo = parseSqlToTableMapping(table.sql_1, table.sql_2);

    return {
      id: index + 1,
      table_id: table.table_id || `table_${index + 1}`,
      remark: table.remark || `表配置${index + 1}`,
      // 应用解析得到的表名和主键信息
      source_schema: mappingInfo.source_schema,
      source_table: mappingInfo.source_table,
      target_schema: mappingInfo.target_schema,
      target_table: mappingInfo.target_table,
      primary_keys: mappingInfo.primary_keys,
      // 应用解析得到的字段映射
      field_mappings: mappingInfo.field_mappings,
      // 原始SQL保留
      sql_1: table.sql_1,
      sql_2: table.sql_2
    };
  });
});

// 过滤表格
const filteredTables = computed(() => {
  let tables = processTables.value;

  if (tableSearch.value) {
    const searchLower = tableSearch.value.toLowerCase();
    tables = tables.filter(table =>
      (table.source_table && table.source_table.toLowerCase().includes(searchLower)) ||
      (table.target_table && table.target_table.toLowerCase().includes(searchLower)) ||
      (table.remark && table.remark.toLowerCase().includes(searchLower))
    );
  }

  return tables;
});

// 分页表格
const paginatedTables = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTables.value.slice(start, end);
});

// 字段过滤
const filteredFields = computed(() => {
  if (!currentTable.value || !currentTable.value.field_mappings) {
    return [];
  }

  let fields = currentTable.value.field_mappings;

  // 应用字段搜索
  if (fieldSearch.value) {
    const searchLower = fieldSearch.value.toLowerCase();
    fields = fields.filter(field =>
      (field.source_field && field.source_field.toLowerCase().includes(searchLower)) ||
      (field.target_field && field.target_field.toLowerCase().includes(searchLower))
    );
  }

  // 应用字段筛选
  if (fieldFilter.value === 'different') {
    fields = fields.filter(field => field.source_field !== field.target_field || field.source_type !== field.target_type);
  } else if (fieldFilter.value === 'missing') {
    fields = fields.filter(field => !field.source_field || !field.target_field);
  }

  return fields;
});

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return '未知';

  try {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return '格式错误';
  }
};

// 获取数据库类型名称
const getDbTypeName = (type) => {
  const dbTypeMap = {
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'oracle': 'Oracle',
    'sqlserver': 'SQL Server',
    'db2': 'DB2',
    'sqlite': 'SQLite',
    'mariadb': 'MariaDB',
    'hive': 'Hive',
    'clickhouse': 'ClickHouse',
    'kingbase': 'KingBase',
    'dameng': 'DaMeng',
    'mongodb': 'MongoDB'
  };

  return dbTypeMap[type] || type || '未知';
};

// 获取数据库类型标签颜色
const getDbTypeColor = (type) => {
  const colorMap = {
    'mysql': 'success',
    'postgresql': 'primary',
    'oracle': 'danger',
    'sqlserver': 'warning',
    'db2': 'info',
    'sqlite': '',
    'mariadb': 'success',
    'hive': 'warning',
    'clickhouse': 'danger',
    'kingbase': 'success',
    'dameng': 'primary',
    'mongodb': 'info'
  };

  return colorMap[type] || '';
};

// 获取字段数量
const getFieldCount = (table) => {
  if (!table) return 0;

  if (table.field_mappings && Array.isArray(table.field_mappings)) {
    return table.field_mappings.length;
  }

  return 0;
};

// 查看字段映射
const viewFieldMappings = (table) => {
  currentTable.value = table;
  fieldsDialogVisible.value = true;
};

// 测试数据库连接
const testConnection = async (type) => {
  if (!props.model) return;

  const db = type === 'source' ? props.model.source_db : props.model.target_db;

  if (!db || !db.host || !db.port || !db.database) {
    ElMessage.warning('数据库连接信息不完整');
    return;
  }

  try {
    if (type === 'source') {
      sourceLoading.value = true;
    } else {
      targetLoading.value = true;
    }

    const response = await testDbConnection(db);

    if (response && typeof response === 'object') {
      if (response.success) {
        ElMessage.success(response.message || '连接测试成功');
      } else {
        ElMessage.error(response.message || '连接测试失败');
      }
    } else {
      ElMessage.warning('收到无效响应，连接测试结果未知');
    }
  } catch (error) {
    ElMessage.error(`连接测试失败: ${error.message || '未知错误'}`);
  } finally {
    if (type === 'source') {
      sourceLoading.value = false;
    } else {
      targetLoading.value = false;
    }
  }
};

// 监听模型变化，重置分页
watch(() => props.model, () => {
  currentPage.value = 1;
  tableSearch.value = '';
}, { deep: true });

// 监听搜索变化，重置分页
watch(tableSearch, () => {
  currentPage.value = 1;
});
</script>

<style lang="scss" scoped>
.model-preview-container {
  margin-bottom: 30px; // 增加底部间距

  .model-info-card {
    margin-bottom: 20px;

    .model-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px; // 统一标题栏高度

      h3 {
        margin: 0;
        display: flex;
        align-items: center;
        font-size: 16px; // 统一标题字体大小

        .el-icon {
          margin-right: 8px;
        }
      }
    }

    .model-description {
      margin-top: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;

      p {
        margin: 0;
        color: #606266;
      }
    }
  }

  .model-details {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .db-info-card {
      flex: 1;
      min-width: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px; // 统一标题栏高度

    h4 {
      margin: 0;
      display: flex;
      align-items: center;
      font-size: 15px; // 统一标题字体大小

      .el-icon {
        margin-right: 8px;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .table-mappings-card {
    margin-bottom: 30px; // 增加底部间距
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .fields-filter {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .no-data {
    padding: 40px 0;
  }
}
</style>