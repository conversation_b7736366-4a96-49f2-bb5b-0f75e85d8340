<template>
  <div class="config-options-container">
    <el-form ref="formRef" :model="config" label-width="140px" :rules="rules">
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        <span class="divider-title">比对配置</span>
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="task_name">
            <el-input v-model="config.task_name" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大比对行数" prop="max_rows">
            <el-input-number
              v-model="config.max_rows"
              :min="0"
              :max="1000000"
              placeholder="为0表示不限制"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="比对模式" prop="compare_mode">
            <el-select v-model="config.compare_mode" style="width: 100%">
              <el-option label="全表比对" value="full" />
              <el-option label="主键比对" value="primary_key" />
              <el-option label="增量比对" value="incremental" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结果存储方式" prop="result_storage">
            <el-select v-model="config.result_storage" style="width: 100%">
              <el-option label="仅保存差异" value="diff_only" />
              <el-option label="保存全部" value="all" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 增量比对选项，仅当比对模式为增量比对时显示 -->
      <div v-if="config.compare_mode === 'incremental'">
        <el-divider content-position="left">增量比对选项</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="增量字段" prop="increment_field">
              <el-select v-model="config.increment_field" filterable allow-create style="width: 100%">
                <el-option
                  v-for="field in availableFields"
                  :key="field"
                  :label="field"
                  :value="field"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="增量开始值" prop="increment_start_value">
              <el-input v-model="config.increment_start_value" placeholder="起始值" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <el-divider content-position="left">
        <el-icon><Cpu /></el-icon>
        <span class="divider-title">执行选项</span>
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="并发度" prop="concurrency">
            <el-slider
              v-model="config.concurrency"
              :min="1"
              :max="10"
              :marks="{1: '1', 5: '5', 10: '10'}"
              show-input
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行超时(分钟)" prop="timeout">
            <el-input-number
              v-model="config.timeout"
              :min="1"
              :max="120"
              placeholder="最大120分钟"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="批处理大小" prop="batch_size">
            <el-select v-model="config.batch_size" style="width: 100%">
              <el-option label="小 (100)" value="100" />
              <el-option label="中 (1000)" value="1000" />
              <el-option label="大 (10000)" value="10000" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">
        <el-icon><BellFilled /></el-icon>
        <span class="divider-title">通知设置</span>
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="完成通知" prop="notification">
            <el-select v-model="config.notification" multiple collapse-tags style="width: 100%">
              <el-option label="任务完成通知" value="task_complete" />
              <el-option label="发现差异通知" value="diff_found" />
              <el-option label="错误通知" value="error" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="任务备注" prop="description">
            <el-input
              v-model="config.description"
              type="textarea"
              rows="3"
              placeholder="可选：添加任务说明或执行目的..."
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider />

      <el-form-item>
        <div class="form-actions">
          <el-button type="primary" @click="submitForm">开始执行</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="saveAsTemplate">存为模板</el-button>
          <el-button type="info" @click="loadTemplate">加载模板</el-button>
        </div>
      </el-form-item>
    </el-form>

    <!-- 模板选择对话框 -->
    <el-dialog v-model="templateDialogVisible" title="选择配置模板" width="500px">
      <el-table :data="templates" style="width: 100%" @row-click="selectTemplate">
        <el-table-column prop="name" label="模板名称" />
        <el-table-column prop="create_time" label="创建时间">
          <template #default="scope">
            {{ formatTime(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column width="80">
          <template #default="scope">
            <el-button type="primary" link @click.stop="selectTemplate(scope.row)">使用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 保存模板对话框 -->
    <el-dialog v-model="saveTemplateDialogVisible" title="保存配置模板" width="500px">
      <el-form :model="templateForm" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="templateForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="saveTemplateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSaveTemplate">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Setting, Cpu, BellFilled } from '@element-plus/icons-vue';
import { formatTime } from '@/utils/timeFormat';
// import { saveConfigTemplate, listConfigTemplates } from '@/api/database';

const props = defineProps({
  selectedModel: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['submit']);

// 表单引用
const formRef = ref(null);

// 配置选项
const config = reactive({
  task_name: '',
  max_rows: 10000,
  compare_mode: 'full',
  result_storage: 'diff_only',
  increment_field: '',
  increment_start_value: '',
  concurrency: 3,
  timeout: 30,
  batch_size: '1000',
  notification: ['task_complete', 'error'],
  description: ''
});

// 对话框状态
const templateDialogVisible = ref(false);
const saveTemplateDialogVisible = ref(false);

// 模板列表
const templates = ref([]);

// 模板表单
const templateForm = reactive({
  name: '',
  description: ''
});

// 可用字段列表（用于增量比对字段选择）
const availableFields = ref([]);

// 表单验证规则
const rules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  increment_field: [
    { required: true, message: '请选择增量字段', trigger: 'change' }
  ],
  increment_start_value: [
    { required: true, message: '请输入增量起始值', trigger: 'blur' }
  ]
};

// 监听所选模型变化，设置默认任务名称
watch(() => props.selectedModel, (newModel) => {
  if (newModel) {
    config.task_name = `${newModel.name}_比对_${new Date().toISOString().slice(0, 10)}`;

    // 获取可用字段列表（用于增量比对）
    extractAvailableFields(newModel);
  }
}, { immediate: true });

// 从模型中提取可用字段列表
const extractAvailableFields = (model) => {
  if (!model || !model.tables || !model.tables.length) {
    availableFields.value = [];
    return;
  }

  // 收集所有表中的字段
  const fields = new Set();

  model.tables.forEach(table => {
    // 如果有明确的字段映射
    if (table.field_mappings && Array.isArray(table.field_mappings)) {
      table.field_mappings.forEach(mapping => {
        fields.add(mapping.source_field);
      });
    }

    // 如果有源字段列表
    if (table.source_fields && Array.isArray(table.source_fields)) {
      table.source_fields.forEach(field => {
        fields.add(field);
      });
    }

    // 如果有主键
    if (table.primary_keys && Array.isArray(table.primary_keys)) {
      table.primary_keys.forEach(key => {
        fields.add(key);
      });
    }
  });

  // 转换为数组并按字母顺序排序
  availableFields.value = Array.from(fields).sort();
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 如果是增量比对模式，确保增量字段和起始值已设置
        if (config.compare_mode === 'incremental') {
          if (!config.increment_field) {
            ElMessage.warning('增量比对模式需要选择增量字段');
            return;
          }
          if (!config.increment_start_value) {
            ElMessage.warning('增量比对模式需要设置起始值');
            return;
          }
        }

        // 将配置和所选模型一起发送给父组件
        emit('submit', {
          ...config,
          model_id: props.selectedModel?.id,
          batch_size: parseInt(config.batch_size, 10)
        });
      } catch (error) {
        console.error('提交配置出错', error);
        ElMessage.error('提交失败：' + (error.message || '未知错误'));
      }
    } else {
      ElMessage.warning('请完成必填项');
    }
  });
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;

  formRef.value.resetFields();

  // 重置为默认值
  Object.assign(config, {
    task_name: props.selectedModel ? `${props.selectedModel.name}_比对_${new Date().toISOString().slice(0, 10)}` : '',
    max_rows: 10000,
    compare_mode: 'full',
    result_storage: 'diff_only',
    increment_field: '',
    increment_start_value: '',
    concurrency: 3,
    timeout: 30,
    batch_size: '1000',
    notification: ['task_complete', 'error'],
    description: ''
  });
};

// 打开加载模板对话框
const loadTemplate = async () => {
  try {
    // TODO: 实现模板加载功能
    ElMessage.info('模板功能暂未实现');
    // 加载模板列表
    // const result = await listConfigTemplates();
    // templates.value = result.data || [];

    // if (templates.value.length === 0) {
    //   ElMessage.info('暂无保存的配置模板');
    //   return;
    // }

    // templateDialogVisible.value = true;
  } catch (error) {
    console.error('加载模板失败', error);
    ElMessage.error('加载模板失败：' + (error.message || '未知错误'));
  }
};

// 选择模板
const selectTemplate = (template) => {
  try {
    // 将模板配置应用到当前配置
    const templateConfig = JSON.parse(template.config || '{}');

    // 合并配置，保留当前任务名称和所选模型ID
    Object.assign(config, {
      ...templateConfig,
      task_name: `${props.selectedModel ? props.selectedModel.name : '任务'}_${new Date().toISOString().slice(0, 10)}`
    });

    templateDialogVisible.value = false;
    ElMessage.success('已应用模板：' + template.name);
  } catch (error) {
    console.error('应用模板失败', error);
    ElMessage.error('应用模板失败：' + (error.message || '未知错误'));
  }
};

// 保存为模板
const saveAsTemplate = () => {
  // 重置表单
  templateForm.name = '';
  templateForm.description = '';

  // 显示对话框
  saveTemplateDialogVisible.value = true;
};

// 确认保存模板
const confirmSaveTemplate = async () => {
  if (!templateForm.name) {
    ElMessage.warning('请输入模板名称');
    return;
  }

  try {
    // TODO: 实现模板保存功能
    ElMessage.info('模板功能暂未实现');
    saveTemplateDialogVisible.value = false;

    // const templateData = {
    //   name: templateForm.name,
    //   description: templateForm.description,
    //   config: JSON.stringify(config)
    // };

    // const result = await saveConfigTemplate(templateData);

    // if (result.code === 0) {
    //   ElMessage.success('模板保存成功');
    //   saveTemplateDialogVisible.value = false;
    // } else {
    //   ElMessage.error(result.message || '保存失败');
    // }
  } catch (error) {
    console.error('保存模板失败', error);
    ElMessage.error('保存模板失败：' + (error.message || '未知错误'));
  }
};

// 对外暴露的方法
defineExpose({
  resetForm,
  getConfig: () => config
});
</script>

<style lang="scss" scoped>
.config-options-container {
  margin-bottom: 30px;

  .el-form {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .divider-title {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
  }

  .el-divider {
    margin: 20px 0;
  }

  // 版本信息
  .version-info {
    text-align: center;
    margin-top: 20px;
    font-size: 12px;
    color: #909399;
  }
}
</style>