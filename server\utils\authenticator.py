from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from datetime import timedelta
from django.conf import settings
from utils.response import CustomResponse


class CustomTokenRefreshView(TokenRefreshView):
    """自定义刷新令牌视图，返回自定义格式的响应，适配pure前端需求"""

    def post(self, request, *args, **kwargs):
        # 调用父类的 post 方法来获取默认的响应
        current_time = timezone.now()

        # 检查响应状态是否为 200 (OK)
        try:
            # 验证请求数据
            if not request.data or 'refreshToken' not in request.data:
                return CustomResponse(
                    success=False,
                    msg="缺少refreshToken参数",
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 构造符合SimpleJWT期望的请求数据格式
            refresh_data = {'refresh': request.data.get('refreshToken')}

            # 临时修改request.data以符合父类期望的格式
            original_data = request.data
            request._mutable = True
            request.data.clear()
            request.data.update(refresh_data)

            # 获取原始数据
            response = super().post(request, *args, **kwargs)

            # 恢复原始请求数据
            request.data.clear()
            request.data.update(original_data)

            # 检查响应状态
            if response.status_code != 200:
                return CustomResponse(
                    success=False,
                    msg="刷新令牌无效或已过期",
                    status=status.HTTP_401_UNAUTHORIZED
                )

            original_response_data = response.data
            access_token = original_response_data.get('access')
            refresh_token = original_response_data.get('refresh')

            # 计算访问令牌的到期时间
            access_token_lifetime = settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME']
            expiration_time = current_time + access_token_lifetime + timedelta(hours=8)
            expiration_time_str = expiration_time.strftime('%Y/%m/%d %H:%M:%S')

            data = {
                'accessToken': access_token,
                'refreshToken': refresh_token,
                'expires': expiration_time_str
            }
            return CustomResponse(data=data, msg="token刷新成功")

        except Exception as e:
            # 处理错误响应
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Token刷新失败: {str(e)}")

            return CustomResponse(
                success=False,
                msg="登录信息已失效，请重新登录",
                status=status.HTTP_401_UNAUTHORIZED
            )
