# -*- coding: utf-8 -*-
"""
标准API响应模块

提供统一的API响应格式，包括成功响应和错误响应。
"""
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse


class CustomResponse(Response):
    """自定义响应类，用于REST框架"""
    def __init__(self, success=True, data=None, msg=None, status=status.HTTP_200_OK, template_name=None, headers=None, exception=False, content_type=None, page=1, limit=1, total=1):
        if not data:
            total = 0
        std_data = {"success": success, "data": data, "msg": msg, "page": page, "limit": limit, "total": total}
        super().__init__(std_data, status, template_name, headers, exception, content_type)


def api_response(data=None, message=None, success=True, status=200, **kwargs):
    """
    标准API响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        success: 是否成功
        status: HTTP状态码
        **kwargs: 其他参数
        
    Returns:
        JsonResponse对象
    """
    response_data = {
        "success": success,
        "message": message,
        "data": data
    }
    
    # 添加其他参数
    if kwargs:
        response_data.update(kwargs)
        
    return JsonResponse(response_data, status=status)


def error_response(code=400, message="操作失败", data=None, **kwargs):
    """
    错误响应格式
    
    Args:
        code: 错误码
        message: 错误消息
        data: 错误数据
        **kwargs: 其他参数
        
    Returns:
        JsonResponse对象
    """
    response_data = {
        "success": False,
        "code": code,
        "message": message,
        "data": data
    }
    
    # 添加其他参数
    if kwargs:
        response_data.update(kwargs)
        
    return JsonResponse(response_data, status=code)
