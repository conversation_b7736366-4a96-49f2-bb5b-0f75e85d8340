# 组件结构
web/src/components/MonacoEditor/
├── src/
│   ├── monaco-editor.vue   # 主组件文件
│   ├── useMonaco.ts       # 组合式 API 实现
│   ├── types.ts          # 类型定义
│   └── sql-language.ts   # SQL 语言支持
└── index.ts             # 导出文件

# seMonaco.ts 核心实现
1. 基础架构
- 使用 Vue3 Composition API 实现 Monaco Editor 的核心逻辑
- 通过 useMonacoEditor hook 封装所有编辑器相关功能
- 主要包含:初始化、格式化、内容同步、生命周期管理等功能

2. 状态管理
- loading: 使用 ref 管理编辑器加载状态
- editor: 保存编辑器实例引用
- 通过 props/emit 与父组件通信

3. 核心功能实现
- initMonaco: 异步初始化编辑器
  - 设置默认配置(主题、语言等)
  - 使用缓存创建编辑器实例
  - 绑定内容变更事件
  - 通知父组件挂载完成
  
- formatCode: 代码格式化
  - 调用编辑器内置格式化功能
  
- 内容同步机制
  - 监听 modelValue 变化自动更新编辑器内容
  - 编辑器内容变化时同步更新父组件

4. 生命周期管理
- 组件卸载时解除编辑器实例引用
- 实例缓存由独立模块管理

5. 性能优化
- 使用编辑器实例缓存
- 异步初始化
- 按需更新内容

# monaco-cache.ts 缓存管理
1. 核心数据结构
- editorInstances: Map<string, Editor> 存储编辑器实例
- usageTimestamp: Map<string, number> 记录使用时间
- MAX_CACHE_SIZE = 10 控制最大缓存数量

2. 主要功能
getOrCreateEditor:
- 复用已有实例
  - 更新编辑器选项
  - 处理容器变更
- 创建新实例
  - 异步加载Monaco
  - 创建编辑器
  - 缓存实例

3. 缓存管理策略
- LRU(最近最少使用)淘汰算法
  - 记录每个实例最后使用时间
  - 缓存满时清理最久未用实例
- 生命周期管理
  - 单个实例清理(clearEditor)
  - 全量清理(clearAllEditors)
  - 页面卸载时自动清理

4. 错误处理
- Monaco加载异常处理
- 编辑器创建失败处理
- DOM容器无效处理

5. 性能优化
- 懒加载Monaco实例
- 复用编辑器实例
- 及时释放资源

# monaco-loader.ts 加载器实现
1. 核心功能
- Monaco实例管理
  - 单例模式确保全局唯一实例
  - 懒加载机制
  - 加载状态追踪
  
2. 加载流程
preloadMonaco():
- 检查缓存实例
- 动态导入Monaco
- 注册SQL语言支持
- 配置编辑器主题
- Worker文件预加载

3. 性能优化
- 源映射禁用
  - 拦截.map文件加载
  - 减少不必要的网络请求
- 预加载策略
  - 应用启动后延迟预加载
  - Worker文件预加载
  - 使用link prefetch提前获取

4. 主题管理
- 主题注册与切换
- 系统主题自适应
- 主题变更事件监听

5. 错误处理
- 加载失败重试机制
- 非阻塞式错误处理
- 详细的日志记录

# monaco-theme.ts 主题配置分析
1. 主题定义
- 亮色主题(sqlLightTheme)
  - 基于vs基础主题
  - 自定义语法高亮规则
  - 编辑器UI颜色配置
  
- 暗色主题(sqlDarkTheme) 
  - 基于vs-dark基础主题
  - 深色模式语法高亮
  - 暗色UI配置

2. 主题注册机制
registerMonacoThemes():
- 统一注册入口
- 支持动态主题切换
- 主题命名规范

3. 主题选择策略
getCurrentTheme():
- 系统主题检测
- 用户偏好记忆
- 默认主题回退

4. 性能考虑
- 主题定义静态化
- 按需加载
- 缓存优化

# Monaco Loader核心实现分析

1. 实例管理
- 单例模式确保全局唯一实例
- 懒加载策略
- 加载状态追踪
- 实例缓存机制

2. 加载流程
preloadMonaco():
- 检查缓存实例
- 动态导入Monaco
- 注册SQL语言支持
- 配置编辑器主题
- 预加载worker文件

3. 性能优化
- 禁用源映射减少请求
- 预加载策略
- 实例缓存复用
- 按需加载组件

4. 主题管理
- 主题注册机制
- 自适应系统主题
- 主题切换事件监听
- 深色模式支持

5. 错误处理
- 加载重试机制
- 详细日志记录
- 优雅降级策略
- 异常状态恢复

# Web Worker配置分析

1. Worker配置架构
- 基于环境变量的worker URL配置
- 按语言类型分发不同worker
- 统一的worker加载入口
- 动态worker路径管理

2. Worker类型映射
{
  json: '/monaco-editor/min/vs/language/json/json.worker.js',
  css: '/monaco-editor/min/vs/language/css/css.worker.js',
  html: '/monaco-editor/min/vs/language/html/html.worker.js',
  typescript: '/monaco-editor/min/vs/language/typescript/ts.worker.js',
  default: '/monaco-editor/min/vs/editor/editor.worker.js'
}

3. 加载优化策略
- 预加载关键worker
- 按需加载其他worker
- 缓存已加载worker
- 错误重试机制

4. 性能考虑
- Worker文件大小优化
- 加载时机控制
- 缓存策略
- 并发限制