"""
Django桥接适配器 - 让SQLAlchemy Agent使用Django的统一模型
解决模型冲突，实现数据一致性
"""
import os
import sys
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class DjangoBridgeAdapter:
    """Django桥接适配器 - 连接SQLAlchemy Agent和Django统一模型"""
    
    def __init__(self, django_settings_module: str = 'arkdrf.settings'):
        self.django_settings_module = django_settings_module
        self._django_initialized = False
        self._models = None
        
    def _init_django(self):
        """初始化Django环境"""
        if self._django_initialized:
            return True
            
        try:
            # 添加Django项目路径
            project_root = Path(__file__).parent.parent.parent.parent / 'server'
            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))
            
            # 设置Django环境
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', self.django_settings_module)
            
            import django
            from django.conf import settings
            
            if not settings.configured:
                django.setup()
            
            self._django_initialized = True
            logger.info("Django环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Django环境初始化失败: {e}")
            return False
    
    def _get_django_models(self):
        """获取Django桥接模型"""
        if not self._init_django():
            return None
            
        if self._models is not None:
            return self._models
            
        try:
            from apps.database.models_bridge import (
                ComparisonConnection,
                ComparisonModel,
                ComparisonTableRule,
                ComparisonTask
            )
            
            self._models = {
                'connection': ComparisonConnection,
                'model': ComparisonModel,
                'table_rule': ComparisonTableRule,
                'task': ComparisonTask
            }
            
            logger.info("Django桥接模型加载成功")
            return self._models
            
        except ImportError as e:
            logger.error(f"导入Django桥接模型失败: {e}")
            return None
    
    def get_connection_config(self, connection_id: int) -> Optional[Dict[str, Any]]:
        """获取数据库连接配置"""
        models = self._get_django_models()
        if not models:
            return None
            
        try:
            connection = models['connection'].objects.get(id=connection_id)
            return {
                'TYPE': connection.type.upper(),
                'IP': connection.host,
                'PORT': str(connection.port),
                'USER_NAME': connection.username,
                'PASSWORD': connection.password,
                'SCHEMA': connection.database,
                'PARAMS': connection.params or {}
            }
        except Exception as e:
            logger.error(f"获取连接配置失败 (ID: {connection_id}): {e}")
            return None
    
    def get_comparison_model_config(self, model_id: int) -> Optional[Dict[str, Any]]:
        """获取比对模型配置"""
        models = self._get_django_models()
        if not models:
            return None
            
        try:
            model = models['model'].objects.select_related(
                'source_connid', 'target_connid'
            ).get(id=model_id)
            
            return {
                'DB1': {
                    'TYPE': model.source_connid.type.upper(),
                    'IP': model.source_connid.host,
                    'PORT': str(model.source_connid.port),
                    'USER_NAME': model.source_connid.username,
                    'PASSWORD': model.source_connid.password,
                    'SCHEMA': model.source_connid.database,
                },
                'DB2': {
                    'TYPE': model.target_connid.type.upper(),
                    'IP': model.target_connid.host,
                    'PORT': str(model.target_connid.port),
                    'USER_NAME': model.target_connid.username,
                    'PASSWORD': model.target_connid.password,
                    'SCHEMA': model.target_connid.database,
                },
                'COMMON': {
                    'CMP_TYPE': '2' if model.cmp_type == 'content' else '1',
                    'TITLE': model.name,
                    **(model.global_config if model.global_config else {})
                }
            }
        except Exception as e:
            logger.error(f"获取比对模型配置失败 (ID: {model_id}): {e}")
            return None
    
    def get_table_rules(self, model_id: int) -> List[Dict[str, Any]]:
        """获取表规则配置"""
        models = self._get_django_models()
        if not models:
            return []
            
        try:
            rules = models['table_rule'].objects.filter(
                model_id=model_id, 
                is_active=True
            ).order_by('table_id')
            
            return [
                {
                    'table_id': rule.table_id,
                    'table_name': rule.table_name,
                    'sql_1': rule.sql_1,
                    'sql_2': rule.sql_2,
                    'primary_keys': rule.primary_keys,
                    'ignore_fields': rule.ignore_fields,
                    'field_mappings': rule.field_mappings,
                    'remark': rule.remark
                }
                for rule in rules
            ]
        except Exception as e:
            logger.error(f"获取表规则失败 (model_id: {model_id}): {e}")
            return []
    
    def create_comparison_task(self, task_data: Dict[str, Any]) -> Optional[str]:
        """创建比对任务"""
        models = self._get_django_models()
        if not models:
            return None
            
        try:
            # 查找关联的模型和表规则
            model_obj = None
            table_rule_obj = None
            
            if 'model_id' in task_data:
                try:
                    model_obj = models['model'].objects.get(id=task_data['model_id'])
                except models['model'].DoesNotExist:
                    logger.warning(f"未找到模型 ID: {task_data['model_id']}")
            
            if 'table_rule_id' in task_data:
                try:
                    table_rule_obj = models['table_rule'].objects.get(id=task_data['table_rule_id'])
                except models['table_rule'].DoesNotExist:
                    logger.warning(f"未找到表规则 ID: {task_data['table_rule_id']}")
            
            # 创建任务
            task = models['task'].objects.create(
                task_id=task_data.get('task_id'),
                user_id=task_data.get('user_id', 'system'),
                model=model_obj,
                table_rule=table_rule_obj,
                task_name=task_data.get('task_name'),
                description=task_data.get('description'),
                status=task_data.get('status', 'pending')
            )
            
            logger.info(f"创建任务成功: {task.task_id}")
            return task.task_id
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None
    
    def update_task_status(self, task_id: str, status: str, **kwargs) -> bool:
        """更新任务状态"""
        models = self._get_django_models()
        if not models:
            return False
            
        try:
            task = models['task'].objects.get(task_id=task_id)
            
            # 更新状态
            task.status = status
            
            # 更新其他字段
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            
            task.save()
            logger.debug(f"任务状态更新成功: {task_id} -> {status}")
            return True
            
        except Exception as e:
            logger.error(f"任务状态更新失败: {e}")
            return False
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        models = self._get_django_models()
        if not models:
            return None
            
        try:
            task = models['task'].objects.select_related('model', 'table_rule').get(task_id=task_id)
            
            return {
                'task_id': task.task_id,
                'user_id': task.user_id,
                'model_id': task.model.id if task.model else None,
                'model_name': task.model.name if task.model else None,
                'table_rule_id': task.table_rule.id if task.table_rule else None,
                'task_name': task.task_name,
                'description': task.description,
                'status': task.status,
                'progress_pct': float(task.progress_pct) if task.progress_pct else 0.0,
                'current_step': task.current_step,
                'create_time': task.create_time.isoformat() if task.create_time else None,
                'start_time': task.start_time.isoformat() if task.start_time else None,
                'complete_time': task.complete_time.isoformat() if task.complete_time else None,
                'total_records': task.total_records,
                'processed_records': task.processed_records,
                'diff_records': task.diff_records,
                'source_only': task.source_only,
                'target_only': task.target_only,
                'matched_records': task.matched_records,
                'exec_time': float(task.exec_time) if task.exec_time else 0.0,
                'error_msg': task.error_msg,
                'error_details': task.error_details
            }
            
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None
    
    def list_connections(self) -> List[Dict[str, Any]]:
        """列出所有数据库连接"""
        models = self._get_django_models()
        if not models:
            return []
            
        try:
            connections = models['connection'].objects.filter(status='active')
            return [
                {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database,
                    'status': conn.status
                }
                for conn in connections
            ]
        except Exception as e:
            logger.error(f"列出连接失败: {e}")
            return []
    
    def list_comparison_models(self) -> List[Dict[str, Any]]:
        """列出所有比对模型"""
        models = self._get_django_models()
        if not models:
            return []
            
        try:
            comparison_models = models['model'].objects.filter(status=True).select_related(
                'source_connid', 'target_connid'
            )
            
            return [
                {
                    'id': model.id,
                    'name': model.name,
                    'description': model.description,
                    'source_connection': model.source_connid.name,
                    'target_connection': model.target_connid.name,
                    'cmp_type': model.cmp_type,
                    'status': model.status
                }
                for model in comparison_models
            ]
        except Exception as e:
            logger.error(f"列出比对模型失败: {e}")
            return []
    
    def test_connection(self) -> bool:
        """测试Django连接"""
        return self._init_django()


# 全局适配器实例
django_bridge = DjangoBridgeAdapter()


def get_django_bridge() -> DjangoBridgeAdapter:
    """获取Django桥接适配器实例"""
    return django_bridge
