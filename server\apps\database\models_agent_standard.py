"""
Django模型 - 完全基于SQLCompare Agent的SQLAlchemy模型标准
以Agent为核心，Django只作为数据读取层，不修改Agent的表结构

表结构与agent/sqlcompare/models/sqlalchemy_models.py完全一致
"""
from django.db import models
from django.utils import timezone


class TaskStatusChoices(models.TextChoices):
    """任务状态枚举 - 与Agent SQLAlchemy完全一致"""
    PENDING = "pending", "等待中"
    RUNNING = "running", "运行中"
    COMPLETED = "completed", "已完成"
    FAILED = "failed", "失败"
    CANCELLED = "cancelled", "已取消"


class User(models.Model):
    """用户表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'users'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
    
    user_id = models.CharField(max_length=50, primary_key=True)
    username = models.Char<PERSON><PERSON>(max_length=100)
    email = models.CharField(max_length=255, unique=True)
    password_hash = models.CharField(max_length=255)
    role = models.CharField(max_length=20, default='user')
    is_active = models.BooleanField(default=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()


class ComparisonConnection(models.Model):
    """数据库连接表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'comparison_connections'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
        indexes = [
            models.Index(fields=['name'], name='idx_connections_name'),
            models.Index(fields=['type'], name='idx_connections_type'),
            models.Index(fields=['status'], name='idx_connections_status'),
        ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=20)
    host = models.CharField(max_length=255)
    port = models.IntegerField()
    username = models.CharField(max_length=100)
    password = models.CharField(max_length=255)
    database = models.CharField(max_length=100)
    params = models.JSONField(null=True, blank=True)
    status = models.CharField(max_length=20, default='active')
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    def __str__(self):
        return self.name

    def to_dict(self):
        """转换为Agent兼容格式"""
        return {
            'TYPE': self.type.upper(),
            'IP': self.host,
            'PORT': str(self.port),
            'USER_NAME': self.username,
            'PASSWORD': self.password,
            'SCHEMA': self.database
        }


class ComparisonModel(models.Model):
    """比对模型表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'comparison_models'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
        constraints = [
            models.UniqueConstraint(fields=['name'], name='uq_model_name')
        ]
        indexes = [
            models.Index(fields=['name'], name='idx_model_name'),
            models.Index(fields=['status'], name='idx_model_status'),
            models.Index(fields=['source_connid'], name='idx_model_source_conn'),
            models.Index(fields=['target_connid'], name='idx_model_target_conn'),
        ]
    
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(null=True, blank=True)
    source_connid = models.ForeignKey(
        ComparisonConnection,
        on_delete=models.RESTRICT,
        related_name='source_models',
        db_column='source_connid'  # 与Agent字段名一致
    )
    target_connid = models.ForeignKey(
        ComparisonConnection,
        on_delete=models.RESTRICT,
        related_name='target_models',
        db_column='target_connid'  # 与Agent字段名一致
    )
    cmp_type = models.CharField(max_length=20, default='content')
    global_config = models.JSONField(null=True, blank=True)
    status = models.BooleanField(default=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    def __str__(self):
        return self.name

    def to_config_format(self):
        """转换为Agent兼容格式"""
        return {
            'DB1': self.source_connid.to_dict(),
            'DB2': self.target_connid.to_dict(),
            'COMMON': {
                'CMP_TYPE': '2' if self.cmp_type == 'content' else '1',
                'TITLE': self.name,
                **(self.global_config if self.global_config else {})
            }
        }


class ComparisonTableRule(models.Model):
    """数据库比对规则表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'comparison_table_rules'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
        constraints = [
            models.UniqueConstraint(fields=['model_id', 'table_id'], name='uq_model_table')
        ]
        indexes = [
            models.Index(fields=['model_id'], name='idx_rule_model_id'),
            models.Index(fields=['table_id'], name='idx_rule_table_id'),
            models.Index(fields=['model_id', 'table_id'], name='idx_rule_model_table'),
            models.Index(fields=['is_active'], name='idx_rule_active'),
        ]
    
    id = models.AutoField(primary_key=True)
    model_id = models.ForeignKey(
        ComparisonModel,
        on_delete=models.CASCADE,
        related_name='table_rules',
        db_column='model_id'  # 与Agent字段名一致
    )
    table_id = models.CharField(max_length=50)
    table_name = models.CharField(max_length=255)
    sql_1 = models.TextField()
    sql_2 = models.TextField()
    remark = models.CharField(max_length=500, null=True, blank=True)
    
    # 扩展配置 - 与Agent完全一致
    primary_keys = models.JSONField(null=True, blank=True)
    ignore_fields = models.JSONField(null=True, blank=True)
    field_mappings = models.JSONField(null=True, blank=True)
    
    # 状态管理
    is_active = models.BooleanField(default=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    def __str__(self):
        return f"{self.model_id.name}-{self.table_id}"

    def to_dict(self):
        """转换为Agent兼容格式"""
        return {
            'table_id': self.table_id,
            'table_name': self.table_name,
            'sql_1': self.sql_1,
            'sql_2': self.sql_2,
            'primary_keys': self.primary_keys,
            'ignore_fields': self.ignore_fields,
            'field_mappings': self.field_mappings
        }


class ComparisonTask(models.Model):
    """比对任务表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'comparison_tasks'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
        constraints = [
            models.UniqueConstraint(fields=['task_id', 'table_rule_id'], name='uq_task_table_rule')
        ]
        indexes = [
            models.Index(fields=['task_id'], name='idx_tasks_task_id'),
            models.Index(fields=['user_id', 'status'], name='idx_tasks_user_status'),
            models.Index(fields=['model_id'], name='idx_tasks_model_id'),
            models.Index(fields=['table_rule_id'], name='idx_tasks_table_rule'),
            models.Index(fields=['status'], name='idx_tasks_status'),
            models.Index(fields=['task_id', 'status'], name='idx_tasks_task_status'),
            models.Index(fields=['task_id', 'table_rule_id'], name='idx_tasks_task_table'),
            models.Index(fields=['model_id', 'status'], name='idx_tasks_model_status'),
            models.Index(fields=['create_time'], name='idx_tasks_create_time'),
        ]
    
    id = models.AutoField(primary_key=True)
    task_id = models.CharField(max_length=50)
    user_id = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='tasks',
        db_column='user_id',
        to_field='user_id'  # 关联到User的user_id字段
    )
    model_id = models.ForeignKey(
        ComparisonModel,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='tasks',
        db_column='model_id'  # 与Agent字段名一致
    )
    table_rule_id = models.ForeignKey(
        ComparisonTableRule,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='task_executions',
        db_column='table_rule_id'  # 与Agent字段名一致
    )
    
    # 任务基本信息
    task_name = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    
    # 执行状态和进度信息
    status = models.CharField(
        max_length=20,
        choices=TaskStatusChoices.choices,
        default=TaskStatusChoices.PENDING
    )
    progress_pct = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)
    current_step = models.CharField(max_length=100, null=True, blank=True)
    
    # 时间戳信息
    create_time = models.DateTimeField()
    start_time = models.DateTimeField(null=True, blank=True)
    complete_time = models.DateTimeField(null=True, blank=True)
    update_time = models.DateTimeField()
    
    # 统计信息
    total_records = models.IntegerField(default=0)
    processed_records = models.IntegerField(default=0)
    diff_records = models.IntegerField(default=0)
    source_only = models.IntegerField(default=0)
    target_only = models.IntegerField(default=0)
    exec_time = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    
    # 错误信息
    error_msg = models.TextField(null=True, blank=True)
    error_details = models.JSONField(null=True, blank=True)
    
    # 数据清理机制支持
    retention_days = models.IntegerField(default=30)
    auto_cleanup = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.task_name or self.task_id}"

    @property
    def matched_records(self):
        """计算匹配记录数"""
        return max(0, (self.total_records or 0) - (self.diff_records or 0) - 
                  (self.source_only or 0) - (self.target_only or 0))

    @property
    def duration(self):
        """任务持续时间"""
        if not self.start_time:
            return None
        
        end = self.complete_time or timezone.now()
        if end < self.start_time:
            return None
            
        seconds = (end - self.start_time).total_seconds()
        
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        
        if hours > 0:
            return f"{int(hours)}小时{int(minutes)}分钟"
        elif minutes > 0:
            return f"{int(minutes)}分钟{int(seconds)}秒"
        else:
            return f"{int(seconds)}秒"


class ComparisonResult(models.Model):
    """比对结果详情表 - 与Agent SQLAlchemy完全一致"""
    
    class Meta:
        db_table = 'comparison_results'  # 与Agent表名一致
        managed = False     # Django不管理此表，由Agent管理
        indexes = [
            models.Index(fields=['task_id'], name='idx_results_task_id'),
            models.Index(fields=['status'], name='idx_results_status'),
            models.Index(fields=['partition_key'], name='idx_results_partition'),
        ]
    
    id = models.AutoField(primary_key=True)
    
    # 关联信息
    task_id = models.ForeignKey(
        ComparisonTask,
        on_delete=models.CASCADE,
        related_name='results',
        db_column='task_id',
        to_field='task_id'  # 关联到ComparisonTask的task_id字段
    )
    table_name = models.CharField(max_length=100)
    record_key = models.CharField(max_length=500)
    
    # 差异信息
    status = models.CharField(max_length=2)
    field_name = models.CharField(max_length=100, null=True, blank=True)
    source_value = models.TextField(null=True, blank=True)
    target_value = models.TextField(null=True, blank=True)
    
    # 扩展信息
    diff_type = models.CharField(max_length=2, null=True, blank=True)
    partition_key = models.CharField(max_length=50, null=True, blank=True)
    
    # 时间戳（Agent自动管理）
    created_at = models.DateTimeField()

    def __str__(self):
        return f"{self.task_id}-{self.table_name}-{self.record_key}"
