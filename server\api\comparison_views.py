"""
比对结果API视图
提供前端查询比对结果的接口
"""

import json
import logging
from django.http import JsonResponse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views import View
from ..models.comparison import (
    ComparisonTask,
    ComparisonSummary,
    ComparisonDifference,
    ComparisonSourceOnly,
    ComparisonTargetOnly,
    ComparisonMetadata
)
from ..services.comparison_sync import comparison_sync_service

logger = logging.getLogger(__name__)


def api_response(success=True, data=None, message='', code=200):
    """统一API响应格式"""
    return JsonResponse({
        'success': success,
        'code': code,
        'message': message,
        'timestamp': timezone.now().isoformat(),
        'data': data
    }, status=code)


@require_http_methods(["GET"])
def get_comparison_result(request, task_id):
    """获取比对结果"""
    try:    
        # 获取任务
        task = ComparisonTask.objects.get(task_id=task_id)
        
        # 获取摘要
        try:
            summary = ComparisonSummary.objects.get(task=task)
            summary_data = {
                'totalRecords': summary.total_records,
                'matchedRecords': summary.matched_records,
                'diffRecords': summary.diff_records,
                'sourceOnlyRecords': summary.source_only_records,
                'targetOnlyRecords': summary.target_only_records,
                'executionTime': summary.execution_time,
            }
        except ComparisonSummary.DoesNotExist:
            summary_data = {
                'totalRecords': 0,
                'matchedRecords': 0,
                'diffRecords': 0,
                'sourceOnlyRecords': 0,
                'targetOnlyRecords': 0,
                'executionTime': 0,
            }
        
        # 获取元数据
        try:
            metadata = ComparisonMetadata.objects.get(task=task)
            metadata_data = {
                'sourceType': metadata.source_type,
                'targetType': metadata.target_type,
                'algorithm': metadata.algorithm,
                'createdAt': task.created_at.isoformat(),
                'completedAt': task.completed_at.isoformat() if task.completed_at else None,
            }
        except ComparisonMetadata.DoesNotExist:
            metadata_data = {
                'sourceType': task.source_config.get('type', ''),
                'targetType': task.target_config.get('type', ''),
                'algorithm': task.compare_config.get('algorithm', ''),
                'createdAt': task.created_at.isoformat(),
                'completedAt': task.completed_at.isoformat() if task.completed_at else None,
            }
        
        # 构建响应
        result = {
            'taskId': task.task_id,
            'status': task.status,
            'summary': summary_data,
            'metadata': metadata_data
        }
        
        return api_response(data=result)
        
    except ComparisonTask.DoesNotExist:
        return api_response(
            success=False,
            message='比对任务不存在',
            code=404
        )
    except Exception as e:
        logger.error(f"获取比对结果失败: {str(e)}")
        return api_response(
            success=False,
            message='服务器内部错误',
            code=500
        )


@require_http_methods(["GET"])
def get_comparison_differences(request, task_id):
    """分页获取差异记录"""
    try:
        task = ComparisonTask.objects.get(task_id=task_id)
        
        # 分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 50))
        
        # 查询差异记录
        differences = ComparisonDifference.objects.filter(task=task).order_by('id')
        paginator = Paginator(differences, page_size)
        page_obj = paginator.get_page(page)
        
        # 格式化结果
        results = []
        for diff in page_obj:
            results.append({
                'id': diff.record_id,
                'sourceData': diff.source_data,
                'targetData': diff.target_data,
                'diffFields': diff.diff_fields,
                'diffType': diff.diff_type,
            })
        
        return api_response(data={
            'results': results,
            'pagination': {
                'page': page,
                'pageSize': page_size,
                'total': paginator.count,
                'totalPages': paginator.num_pages,
            }
        })
        
    except ComparisonTask.DoesNotExist:
        return api_response(
            success=False,
            message='比对任务不存在',
            code=404
        )
    except Exception as e:
        logger.error(f"获取差异记录失败: {str(e)}")
        return api_response(
            success=False,
            message='服务器内部错误',
            code=500
        )


@require_http_methods(["GET"])
def get_source_only_records(request, task_id):
    """分页获取源独有记录"""
    try:
        task = ComparisonTask.objects.get(task_id=task_id)
        
        # 分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 50))
        
        # 查询源独有记录
        records = ComparisonSourceOnly.objects.filter(task=task).order_by('id')
        paginator = Paginator(records, page_size)
        page_obj = paginator.get_page(page)
        
        # 格式化结果
        results = []
        for record in page_obj:
            results.append({
                'id': record.record_id,
                'data': record.data,
                'reason': record.reason,
            })
        
        return api_response(data={
            'results': results,
            'pagination': {
                'page': page,
                'pageSize': page_size,
                'total': paginator.count,
                'totalPages': paginator.num_pages,
            }
        })
        
    except ComparisonTask.DoesNotExist:
        return api_response(
            success=False,
            message='比对任务不存在',
            code=404
        )
    except Exception as e:
        logger.error(f"获取源独有记录失败: {str(e)}")
        return api_response(
            success=False,
            message='服务器内部错误',
            code=500
        )


@require_http_methods(["GET"])
def get_target_only_records(request, task_id):
    """分页获取目标独有记录"""
    try:
        task = ComparisonTask.objects.get(task_id=task_id)
        
        # 分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 50))
        
        # 查询目标独有记录
        records = ComparisonTargetOnly.objects.filter(task=task).order_by('id')
        paginator = Paginator(records, page_size)
        page_obj = paginator.get_page(page)
        
        # 格式化结果
        results = []
        for record in page_obj:
            results.append({
                'id': record.record_id,
                'data': record.data,
                'reason': record.reason,
            })
        
        return api_response(data={
            'results': results,
            'pagination': {
                'page': page,
                'pageSize': page_size,
                'total': paginator.count,
                'totalPages': paginator.num_pages,
            }
        })
        
    except ComparisonTask.DoesNotExist:
        return api_response(
            success=False,
            message='比对任务不存在',
            code=404
        )
    except Exception as e:
        logger.error(f"获取目标独有记录失败: {str(e)}")
        return api_response(
            success=False,
            message='服务器内部错误',
            code=500
        )


@require_http_methods(["GET"])
def get_comparison_status(request, task_id):
    """获取比对任务状态"""
    status_data = comparison_sync_service.get_task_status(task_id)
    
    if status_data:
        return api_response(data=status_data)
    else:
        return api_response(
            success=False,
            message='任务不存在',
            code=404
        )


@csrf_exempt
@require_http_methods(["POST"])
def sync_comparison_results(request):
    """接收Agent同步的比对结果"""
    try:
        data = json.loads(request.body)
        task_id = data.get('task_id')
        results_data = data.get('results')
        
        if not task_id or not results_data:
            return api_response(
                success=False,
                message='缺少必要参数',
                code=400
            )
        
        # 同步结果
        success = comparison_sync_service.sync_comparison_results(task_id, results_data)
        
        if success:
            return api_response(message='同步成功')
        else:
            return api_response(
                success=False,
                message='同步失败',
                code=500
            )
            
    except json.JSONDecodeError:
        return api_response(
            success=False,
            message='无效的JSON数据',
            code=400
        )
    except Exception as e:
        logger.error(f"同步比对结果失败: {str(e)}")
        return api_response(
            success=False,
            message='服务器内部错误',
            code=500
        )
