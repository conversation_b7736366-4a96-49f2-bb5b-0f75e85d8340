#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源配置页面组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
import configparser
from pathlib import Path
from .base_page import BasePage


class DatasourceConfigPage(BasePage):
    """数据源配置页面"""
    
    def __init__(self, parent, app_instance=None):
        # 初始化配置变量
        self._init_config_vars()
        
        super().__init__(parent, app_instance)
        
        # 加载配置文件
        self._load_config_file()
    
    def _init_config_vars(self):
        """初始化配置变量"""
        # 源数据库配置变量
        self.source_vars = {
            'db_type': tk.StringVar(value="MySQL"),
            'host': tk.StringVar(value="localhost"),
            'port': tk.StringVar(value="3306"),
            'database': tk.StringVar(value=""),
            'username': tk.StringVar(value=""),
            'password': tk.StringVar(value=""),
            'charset': tk.StringVar(value="utf8mb4")
        }
        
        # 目标数据库配置变量
        self.target_vars = {
            'db_type': tk.StringVar(value="MySQL"),
            'host': tk.StringVar(value="localhost"),
            'port': tk.StringVar(value="3306"),
            'database': tk.StringVar(value=""),
            'username': tk.StringVar(value=""),
            'password': tk.StringVar(value=""),
            'charset': tk.StringVar(value="utf8mb4")
        }
    
    def _create_ui(self):
        """创建数据源配置界面"""
        self.frame = ttk.Frame(self.parent)
        
        # 页面标题
        title_label = ttk.Label(self.frame, text="数据源配置", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(20, 30))
        
        # 创建主容器
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=40, pady=20)
        
        # 源数据库连接配置
        self._create_source_connection_section(main_container)
        
        # 目标数据库连接配置
        self._create_target_connection_section(main_container)
        
        # 操作按钮
        self._create_action_buttons(main_container)
    
    def _create_source_connection_section(self, parent):
        """创建源数据库连接配置区域"""
        source_frame = ttk.LabelFrame(parent, text="源数据库连接", padding=15)
        source_frame.pack(fill=tk.X, pady=(0, 20))
        
        self._create_connection_fields(source_frame, "source")
    
    def _create_target_connection_section(self, parent):
        """创建目标数据库连接配置区域"""
        target_frame = ttk.LabelFrame(parent, text="目标数据库连接", padding=15)
        target_frame.pack(fill=tk.X, pady=(0, 20))
        
        self._create_connection_fields(target_frame, "target")
    
    def _create_connection_fields(self, parent, prefix):
        """创建连接字段"""
        # 选择对应的配置变量集合
        vars_dict = self.source_vars if prefix == "source" else self.target_vars
        
        fields = [
            ("数据库类型", "db_type", ["MySQL", "PostgreSQL", "Oracle", "SQL Server", "DB2"]),
            ("主机地址", "host", None),
            ("端口", "port", None),
            ("数据库名", "database", None),
            ("用户名", "username", None),
            ("密码", "password", None),
            ("字符编码", "charset", ["utf8mb4", "utf8", "latin1", "gbk"])
        ]
        
        for i, field_info in enumerate(fields):
            if len(field_info) == 3:
                label, field, options = field_info
            else:
                label, field = field_info[:2]
                options = None
            
            # 创建标签
            ttk.Label(parent, text=f"{label}:").grid(
                row=i, column=0, sticky=tk.W, pady=8, padx=(0, 10))
            
            # 创建输入控件
            if options:  # 下拉框
                widget = ttk.Combobox(parent, width=27, textvariable=vars_dict[field],
                                     values=options, state="readonly")
            elif field == "password":  # 密码框
                widget = ttk.Entry(parent, width=30, show="*", textvariable=vars_dict[field])
            else:  # 普通文本框
                widget = ttk.Entry(parent, width=30, textvariable=vars_dict[field])
            
            widget.grid(row=i, column=1, sticky=tk.W, pady=8, padx=(0, 20))
            
            # 添加测试连接按钮（仅在最后一行）
            if i == len(fields) - 1:
                test_btn = ttk.Button(parent, text="测试连接", 
                                     command=lambda p=prefix: self._test_connection(p))
                test_btn.grid(row=i, column=2, sticky=tk.W, pady=8, padx=(10, 0))
    
    def _create_action_buttons(self, parent):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(btn_frame, text="保存配置", 
                  command=self._save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="重新加载", 
                  command=self._reload_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="重置", 
                  command=self._reset_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="测试所有连接", 
                  command=self._test_all_connections).pack(side=tk.RIGHT)
    
    def _test_connection(self, prefix):
        """测试数据库连接"""
        vars_dict = self.source_vars if prefix == "source" else self.target_vars
        
        # 获取连接参数
        db_type = vars_dict['db_type'].get()
        host = vars_dict['host'].get()
        port = vars_dict['port'].get()
        database = vars_dict['database'].get()
        username = vars_dict['username'].get()
        password = vars_dict['password'].get()
        
        if not all([host, port, database, username]):
            messagebox.showerror("错误", "请填写完整的连接信息")
            return
        
        self._log_message(f"测试{prefix}数据库连接...")
        self._update_status(f"测试{prefix}连接中...")
        
        # 模拟连接测试
        try:
            # 这里应该实现真实的数据库连接测试
            self._log_message(f"{prefix}数据库连接测试成功")
            self._update_status("连接测试成功")
            messagebox.showinfo("成功", f"{prefix}数据库连接测试成功")
        except Exception as e:
            self._log_message(f"{prefix}数据库连接测试失败: {e}", "ERROR")
            self._update_status("连接测试失败")
            messagebox.showerror("失败", f"{prefix}数据库连接测试失败: {e}")
    
    def _test_all_connections(self):
        """测试所有连接"""
        self._test_connection("source")
        self._test_connection("target")
    
    def _save_config(self):
        """保存配置"""
        try:
            if self._save_config_file():
                self._log_message("数据源配置保存成功")
                self._update_status("配置已保存")
                messagebox.showinfo("成功", "数据源配置已保存到配置文件")
            else:
                messagebox.showerror("失败", "保存配置文件失败")
        except Exception as e:
            self._log_message(f"保存配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _reload_config(self):
        """重新加载配置"""
        try:
            self._load_config_file()
            self._log_message("数据源配置重新加载成功")
            self._update_status("配置已重新加载")
            messagebox.showinfo("成功", "数据源配置已重新加载")
        except Exception as e:
            self._log_message(f"重新加载配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"重新加载配置失败: {e}")
    
    def _reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有数据源配置吗？"):
            # 重置源数据库配置
            self.source_vars['db_type'].set("MySQL")
            self.source_vars['host'].set("localhost")
            self.source_vars['port'].set("3306")
            self.source_vars['database'].set("")
            self.source_vars['username'].set("")
            self.source_vars['password'].set("")
            self.source_vars['charset'].set("utf8mb4")
            
            # 重置目标数据库配置
            self.target_vars['db_type'].set("MySQL")
            self.target_vars['host'].set("localhost")
            self.target_vars['port'].set("3306")
            self.target_vars['database'].set("")
            self.target_vars['username'].set("")
            self.target_vars['password'].set("")
            self.target_vars['charset'].set("utf8mb4")
            
            self._log_message("数据源配置已重置")
            self._update_status("配置已重置")
            messagebox.showinfo("成功", "数据源配置已重置")
    
    def get_data(self):
        """获取页面数据"""
        return {
            "source": {key: var.get() for key, var in self.source_vars.items()},
            "target": {key: var.get() for key, var in self.target_vars.items()}
        }
    
    def set_data(self, data):
        """设置页面数据"""
        if "source" in data:
            for key, value in data["source"].items():
                if key in self.source_vars:
                    self.source_vars[key].set(value)
        
        if "target" in data:
            for key, value in data["target"].items():
                if key in self.target_vars:
                    self.target_vars[key].set(value)
    
    def validate(self):
        """验证页面数据"""
        # 验证源数据库配置
        for field in ['host', 'port', 'database', 'username']:
            if not self.source_vars[field].get():
                return False, f"源数据库{field}不能为空"
        
        # 验证目标数据库配置
        for field in ['host', 'port', 'database', 'username']:
            if not self.target_vars[field].get():
                return False, f"目标数据库{field}不能为空"
        
        return True, ""

    def _load_config_file(self):
        """加载配置文件"""
        try:
            # 查找配置文件 - 使用当前版本的配置
            current_dir = Path(__file__).parent  # components目录
            gui_dir = current_dir.parent  # gui目录
            sqlcompare_dir = gui_dir.parent  # sqlcompare目录

            config_paths = [
                # 当前版本配置路径 - 绝对路径最可靠
                Path("F:/replay/agent/sqlcompare/config/Config.ini"),
                # 从sqlcompare目录到config目录
                sqlcompare_dir / "config" / "Config.ini",
                # 从gui目录到config目录
                gui_dir / ".." / "config" / "Config.ini",
                # 备用路径
                Path("../config/Config.ini"),
                Path("config/Config.ini"),
                Path("Config.ini")
            ]

            config_path = None
            print(f"🔍 数据源配置页面查找配置文件...")
            print(f"   components目录: {current_dir}")
            print(f"   gui目录: {gui_dir}")
            print(f"   sqlcompare目录: {sqlcompare_dir}")

            for i, path in enumerate(config_paths):
                try:
                    abs_path = path.resolve()
                    exists = path.exists()
                    print(f"🔍 [{i+1}] 路径: {abs_path} - {'存在' if exists else '不存在'}")
                    if exists:
                        config_path = abs_path
                        print(f"✅ 找到配置文件: {abs_path}")
                        break
                except Exception as e:
                    print(f"❌ 路径解析失败 {path}: {e}")

            if not config_path:
                print("❌ 未找到配置文件，使用默认配置")
                self._log_message("未找到配置文件，使用默认配置", "WARNING")
                return

            # 读取配置文件
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')

            # 加载源数据库配置 (DB1)
            if 'DB1' in config:
                source_config = config['DB1']
                # 映射配置字段
                field_mapping = {
                    'TYPE': 'db_type',
                    'IP': 'host',
                    'PORT': 'port',
                    'SCHEMA': 'database',
                    'USER_NAME': 'username',
                    'PASSWORD': 'password'
                }

                for config_key, var_key in field_mapping.items():
                    if config_key in source_config and var_key in self.source_vars:
                        value = source_config[config_key]
                        # 特殊处理数据库类型
                        if var_key == 'db_type' and value.upper() == 'DB2':
                            value = 'DB2'
                        self.source_vars[var_key].set(value)

                self._log_message("已加载源数据库配置 (DB1)")

            # 加载目标数据库配置 (DB2)
            if 'DB2' in config:
                target_config = config['DB2']
                # 映射配置字段
                field_mapping = {
                    'TYPE': 'db_type',
                    'IP': 'host',
                    'PORT': 'port',
                    'SCHEMA': 'database',
                    'USER_NAME': 'username',
                    'PASSWORD': 'password'
                }

                for config_key, var_key in field_mapping.items():
                    if config_key in target_config and var_key in self.target_vars:
                        value = target_config[config_key]
                        # 特殊处理数据库类型
                        if var_key == 'db_type' and value.upper() == 'DB2':
                            value = 'DB2'
                        self.target_vars[var_key].set(value)

                self._log_message("已加载目标数据库配置 (DB2)")

            self._log_message(f"配置文件加载成功: {config_path}")

        except Exception as e:
            self._log_message(f"加载配置文件失败: {e}", "ERROR")

    def _save_config_file(self):
        """保存配置文件"""
        try:
            # 创建配置对象
            config = configparser.ConfigParser()

            # 读取现有配置（如果存在）
            config_paths = [
                Path("../config/Config.ini"),
                Path("config/Config.ini"),
                Path("Config.ini")
            ]

            config_path = None
            for path in config_paths:
                if path.exists():
                    config_path = path
                    config.read(config_path, encoding='utf-8')
                    break

            if not config_path:
                # 创建新的配置文件路径
                config_path = Path("../config/Config.ini")
                config_path.parent.mkdir(parents=True, exist_ok=True)

            # 更新源数据库配置
            if 'SOURCE_DB' not in config:
                config.add_section('SOURCE_DB')

            for key, var in self.source_vars.items():
                config.set('SOURCE_DB', key, var.get())

            # 更新目标数据库配置
            if 'TARGET_DB' not in config:
                config.add_section('TARGET_DB')

            for key, var in self.target_vars.items():
                config.set('TARGET_DB', key, var.get())

            # 保存配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                config.write(f)

            self._log_message(f"配置文件保存成功: {config_path}")
            return True

        except Exception as e:
            self._log_message(f"保存配置文件失败: {e}", "ERROR")
            return False
