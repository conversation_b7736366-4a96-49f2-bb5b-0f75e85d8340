<!--
错误详情组件
用于展示错误详情，包括错误类型、错误消息、错误类别、严重程度等
-->
<template>
  <div class="error-details">
    <div v-if="error" class="error-container">
      <div class="error-header">
        <el-alert
          :title="error.message || '发生错误'"
          :type="severityType"
          :description="errorDescription"
          show-icon
          :closable="false"
          class="mb-3"
        />
        
        <div class="error-meta">
          <div class="meta-item">
            <span class="label">错误类型:</span>
            <span class="value">{{ error.type || '未知' }}</span>
          </div>
          <div class="meta-item">
            <span class="label">错误类别:</span>
            <span class="value">
              <el-tag :type="getCategoryTagType(error.category)" size="small">
                {{ getCategoryDisplayName(error.category) }}
              </el-tag>
            </span>
          </div>
          <div class="meta-item">
            <span class="label">严重程度:</span>
            <span class="value">
              <el-tag :type="getSeverityTagType(error.severity)" size="small">
                {{ getSeverityDisplayName(error.severity) }}
              </el-tag>
            </span>
          </div>
          <div v-if="error.timestamp" class="meta-item">
            <span class="label">发生时间:</span>
            <span class="value">{{ formatTime(error.timestamp) }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="hasDetails" class="error-details mt-3">
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <div v-if="error.context && Object.keys(error.context).length > 0" class="context mb-3">
              <h4>错误上下文</h4>
              <pre class="context-content">{{ formatContext(error.context) }}</pre>
            </div>
            
            <div v-if="error.traceback" class="traceback">
              <h4>错误堆栈</h4>
              <pre class="traceback-content">{{ error.traceback }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <div v-if="showActions" class="error-actions mt-3">
        <slot name="actions">
          <el-button 
            v-if="showRetry"
            type="primary" 
            @click="$emit('retry')"
            :loading="loading"
          >
            重试
          </el-button>
          <el-button 
            type="info" 
            @click="copyError"
            :loading="copying"
          >
            复制错误信息
          </el-button>
          <el-button 
            @click="$emit('close')"
          >
            关闭
          </el-button>
        </slot>
      </div>
    </div>
    <div v-else class="no-error">
      <el-empty description="无错误信息" />
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ErrorCategory, 
  ErrorSeverity, 
  getCategoryDisplayName, 
  getSeverityDisplayName,
  getSeverityType
} from '@/utils/error/errorTypes'

export default {
  name: 'ErrorDetails',
  props: {
    error: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    showActions: {
      type: Boolean,
      default: true
    },
    showRetry: {
      type: Boolean,
      default: true
    }
  },
  emits: ['retry', 'close'],
  setup(props) {
    const copying = ref(false)
    
    // 计算是否有详细信息
    const hasDetails = computed(() => {
      return (props.error && (
        (props.error.context && Object.keys(props.error.context).length > 0) || 
        props.error.traceback
      ))
    })
    
    // 获取严重程度对应的UI类型
    const severityType = computed(() => {
      if (!props.error || !props.error.severity) return 'error'
      return getSeverityType(props.error.severity)
    })
    
    // 获取错误描述
    const errorDescription = computed(() => {
      if (!props.error) return ''
      
      let description = ''
      
      // 添加任务ID信息
      if (props.error.task_id) {
        description += `任务ID: ${props.error.task_id}\n`
      }
      
      // 添加错误类别和严重程度
      description += `类别: ${getCategoryDisplayName(props.error.category || ErrorCategory.UNKNOWN)}, `
      description += `严重程度: ${getSeverityDisplayName(props.error.severity || ErrorSeverity.ERROR)}`
      
      return description
    })
    
    // 格式化上下文
    const formatContext = (context) => {
      if (!context) return ''
      try {
        return JSON.stringify(context, null, 2)
      } catch (e) {
        return String(context)
      }
    }
    
    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      try {
        return new Date(timestamp).toLocaleString()
      } catch (e) {
        return timestamp
      }
    }
    
    // 获取错误类别对应的标签类型
    const getCategoryTagType = (category) => {
      const typeMap = {
        [ErrorCategory.DATABASE]: 'danger',
        [ErrorCategory.QUERY]: 'danger',
        [ErrorCategory.COMPARISON]: 'danger',
        [ErrorCategory.NETWORK]: 'warning',
        [ErrorCategory.CONNECTION]: 'warning',
        [ErrorCategory.TIMEOUT]: 'warning',
        [ErrorCategory.AUTHENTICATION]: 'danger',
        [ErrorCategory.AUTHORIZATION]: 'danger',
        [ErrorCategory.PERMISSION]: 'danger',
        [ErrorCategory.VALIDATION]: 'warning',
        [ErrorCategory.RESOURCE]: 'warning',
        [ErrorCategory.CONFIGURATION]: 'warning',
        [ErrorCategory.EXTERNAL]: 'danger',
        [ErrorCategory.INTERNAL]: 'danger',
        [ErrorCategory.SYSTEM]: 'danger',
        [ErrorCategory.UNKNOWN]: 'info'
      }
      return typeMap[category] || 'info'
    }
    
    // 获取严重程度对应的标签类型
    const getSeverityTagType = (severity) => {
      const typeMap = {
        [ErrorSeverity.INFO]: 'info',
        [ErrorSeverity.WARNING]: 'warning',
        [ErrorSeverity.ERROR]: 'danger',
        [ErrorSeverity.CRITICAL]: 'danger'
      }
      return typeMap[severity] || 'info'
    }
    
    // 复制错误信息
    const copyError = async () => {
      if (!props.error) return
      
      try {
        copying.value = true
        
        const errorText = `
错误类型: ${props.error.type || '未知'}
错误信息: ${props.error.message || '未知错误'}
错误类别: ${getCategoryDisplayName(props.error.category || ErrorCategory.UNKNOWN)}
严重程度: ${getSeverityDisplayName(props.error.severity || ErrorSeverity.ERROR)}
${props.error.timestamp ? `发生时间: ${formatTime(props.error.timestamp)}` : ''}
${props.error.task_id ? `任务ID: ${props.error.task_id}` : ''}
${props.error.context ? `上下文: ${formatContext(props.error.context)}` : ''}
${props.error.traceback ? `堆栈: ${props.error.traceback}` : ''}
        `.trim()
        
        await navigator.clipboard.writeText(errorText)
        ElMessage.success('错误信息已复制到剪贴板')
      } catch (e) {
        ElMessage.error('复制失败: ' + e.message)
      } finally {
        copying.value = false
      }
    }
    
    return {
      hasDetails,
      severityType,
      errorDescription,
      formatContext,
      formatTime,
      getCategoryDisplayName,
      getSeverityDisplayName,
      getCategoryTagType,
      getSeverityTagType,
      copyError,
      copying
    }
  }
}
</script>

<style scoped>
.error-container {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}

.error-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 8px;
  color: #606266;
}

.context-content,
.traceback-content {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
}

.error-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dark .error-container {
  background-color: #1e1e1e;
  border-color: #333;
}

.dark .context-content,
.dark .traceback-content {
  background-color: #2c2c2c;
  color: #e0e0e0;
}
</style>
