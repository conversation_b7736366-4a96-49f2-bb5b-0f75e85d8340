import os
import logging
from django.apps import AppConfig

logger = logging.getLogger(__name__)

class DatabaseConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.database'
    verbose_name = '数据库比对'

    def ready(self):
        """
        应用程序准备就绪时执行的操作
        """
        # 只在主进程中初始化服务（RUN_MAIN环境变量存在时表示是主进程）
        if os.environ.get('RUN_MAIN') != 'true':
            return

        if hasattr(self, '_initialized'):
            return
        self._initialized = True

        # 启动消息队列
        try:
            from utils.message_handlers import start_message_queue
            start_message_queue()
        except Exception as e:
            logger.error(f"启动消息队列失败: {str(e)}")

        # 启动任务清理服务
        try:
            from utils.task_cleanup_service import task_cleanup_service
            task_cleanup_service.start()
        except Exception as e:
            logger.error(f"启动任务清理服务失败: {str(e)}")
