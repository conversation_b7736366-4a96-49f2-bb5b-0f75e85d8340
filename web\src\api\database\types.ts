/**
 * 数据库API类型定义 - 简化版本
 * 专注于核心功能，移除过度复杂的类型定义
 */

// ===== 基础类型 =====

/**
 * API响应基础结构
 */
export interface BaseApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  timestamp: string;
  data?: T;
  total?: number; // 添加total字段，用于分页数据的总数
  // 扩展字段，用于兼容不同API响应格式
  details?: {
    tableProgress?: Array<{
      tableName: string;
      totalRecords: number;
      processed: number;
      matched: number;
      different: number;
      errors: number;
      progress: number;
    }>;
    remainingTime?: number;
    currentSpeed?: number;
    logs?: Array<{
      level: string;
      message: string;
      timestamp: string;
    }>;
    resources?: {
      cpu: number;
      memory: number;
      diskIO: number;
      networkIO: number;
    };
  };
  summary?: {
    source_count?: number;
    matched_count?: number;
    diff_count?: number;
    target_count?: number;
    execution_time?: number;
  };
}

/**
 * 比对响应接口
 */
export interface ComparisonApiResponse {
  success: boolean;
  code: number;
  message: string;
  timestamp: string;
  data?: any;
  summary?: {
    source_count?: number;
    matched_count?: number;
    diff_count?: number;
    target_count?: number;
    execution_time?: number;
  };
  execution_time?: number;
  details?: any[];
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 数据库连接配置
 */
export interface DatabaseConnection {
  id?: string;
  name: string;
  type: DatabaseType;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  schema?: string;
  parameters?: Record<string, any>;
}

/**
 * 数据库类型枚举
 */
export enum DatabaseType {
  DB2 = 'db2',
  ORACLE = 'oracle',
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  SQLSERVER = 'sqlserver',
  GAUSSDB = 'gaussdb'
}

// ===== 比较相关类型 =====

/**
 * 比较配置
 */
export interface CompareConfig {
  sourceTable: string;
  targetTable: string;
  compareColumns?: string[];
  keyColumns?: string[];
  whereCondition?: string;
  algorithm?: ComparisonAlgorithm;
  batchSize?: number;
  maxRecords?: number;
}

/**
 * 比较算法类型
 */
export enum ComparisonAlgorithm {
  PRIMARY_KEY = 'primary_key',
  HASH_BASED = 'hash_based',
  TWO_PHASE = 'two_phase'
}

/**
 * 任务状态
 */
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELED = 'canceled'
}

/**
 * 比较任务
 */
export interface ComparisonTask {
  taskId: string;
  name?: string;
  status: TaskStatus;
  sourceConnection: DatabaseConnection;
  targetConnection: DatabaseConnection;
  compareConfig: CompareConfig;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  progress?: number;
  error?: string;
}

/**
 * 比较结果摘要
 */
export interface ComparisonSummary {
  totalRecords: number;
  matchedRecords: number;
  diffRecords: number;
  sourceOnlyRecords: number;
  targetOnlyRecords: number;
  executionTime: number;
}

/**
 * 差异记录
 */
export interface DifferenceRecord {
  id: string;
  sourceData: Record<string, any>;
  targetData: Record<string, any>;
  diffFields: string[];
  diffType: 'value_diff' | 'type_diff' | 'format_diff';
}

/**
 * 源独有记录
 */
export interface SourceOnlyRecord {
  id: string;
  data: Record<string, any>;
}

/**
 * 目标独有记录
 */
export interface TargetOnlyRecord {
  id: string;
  data: Record<string, any>;
}

/**
 * 比较结果详情
 */
export interface ComparisonDetails {
  differences: DifferenceRecord[];
  sourceOnly: SourceOnlyRecord[];
  targetOnly: TargetOnlyRecord[];
}

/**
 * 完整的比较结果
 */
export interface ComparisonResult {
  taskId: string;
  status: TaskStatus;
  summary: ComparisonSummary;
  details?: ComparisonDetails;
  metadata: ComparisonMetadata;
}

/**
 * 比较元数据
 */
export interface ComparisonMetadata {
  sourceType: string;
  targetType: string;
  algorithm: string;
  compareConfig: CompareConfig;
  createdAt: string;
  completedAt?: string;
}

// ===== 数据库操作相关类型 =====

/**
 * 表信息
 */
export interface TableInfo {
  name: string;
  schema?: string;
  rowCount?: number;
  columns: ColumnInfo[];
  primaryKeys: string[];
}

/**
 * 列信息
 */
export interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: any;
  isPrimaryKey?: boolean;
}

/**
 * 连接测试结果
 */
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  serverVersion?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// ===== 错误相关类型 =====

/**
 * 错误代码
 */
export enum ErrorCode {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  QUERY_SYNTAX_ERROR = 'QUERY_SYNTAX_ERROR',
  TABLE_NOT_FOUND = 'TABLE_NOT_FOUND',
  TASK_NOT_FOUND = 'TASK_NOT_FOUND',
  COMPARISON_FAILED = 'COMPARISON_FAILED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT = 'RATE_LIMIT',
  BAD_GATEWAY = 'BAD_GATEWAY',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT = 'GATEWAY_TIMEOUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * API错误信息
 */
export interface ApiError {
  code: ErrorCode;
  message: string;
  details?: any;
  endpoint?: string;
}

/**
 * 数据库请求配置
 */
export interface DatabaseRequestConfig {
  timeout?: number;
  headers?: Record<string, string>;
}
