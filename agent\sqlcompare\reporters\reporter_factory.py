#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告器工厂模块
提供统一的报告器创建和管理功能，支持多种报告器类型和配置
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod

from reporters.base_reporter import BaseReporter
from core.models import DiffResult

logger = logging.getLogger(__name__)


class MultiReporter(BaseReporter):
    """
    多报告器组合类
    支持同时向多个报告器输出结果
    """

    def __init__(self, reporters: List[BaseReporter]):
        """
        初始化多报告器

        Args:
            reporters: 报告器列表
        """
        super().__init__()
        self.reporters = reporters
        self.failed_reporters = []
        logger.info(f"MultiReporter初始化，包含 {len(reporters)} 个报告器")

    def open(self):
        """打开所有报告器"""
        for i, reporter in enumerate(self.reporters):
            try:
                reporter.open()
                logger.debug(f"报告器 {i+1} 打开成功: {type(reporter).__name__}")
            except Exception as e:
                logger.error(f"报告器 {i+1} 打开失败: {type(reporter).__name__}, 错误: {e}")
                self.failed_reporters.append(reporter)

        # 移除失败的报告器
        for failed_reporter in self.failed_reporters:
            if failed_reporter in self.reporters:
                self.reporters.remove(failed_reporter)

        if not self.reporters:
            raise RuntimeError("所有报告器都打开失败")

        logger.info(f"MultiReporter打开完成，成功 {len(self.reporters)} 个，失败 {len(self.failed_reporters)} 个")

    def close(self):
        """关闭所有报告器"""
        for i, reporter in enumerate(self.reporters):
            try:
                reporter.close()
                logger.debug(f"报告器 {i+1} 关闭成功: {type(reporter).__name__}")
            except Exception as e:
                logger.error(f"报告器 {i+1} 关闭失败: {type(reporter).__name__}, 错误: {e}")

        logger.info(f"MultiReporter关闭完成")

    def report_diff(self, diff_result: DiffResult):
        """向所有报告器报告差异"""
        failed_count = 0
        for reporter in self.reporters:
            try:
                reporter.report_diff(diff_result)
            except Exception as e:
                logger.error(f"报告器 {type(reporter).__name__} 报告差异失败: {e}")
                failed_count += 1

        # 如果所有报告器都失败，抛出异常
        if failed_count == len(self.reporters):
            raise RuntimeError("所有报告器都报告差异失败")

    def get_statistics(self) -> Dict[str, Any]:
        """获取所有报告器的统计信息"""
        stats = {}
        for i, reporter in enumerate(self.reporters):
            try:
                if hasattr(reporter, 'get_performance_stats'):
                    reporter_stats = reporter.get_performance_stats()
                    stats[f"reporter_{i+1}_{type(reporter).__name__}"] = reporter_stats
            except Exception as e:
                logger.error(f"获取报告器 {type(reporter).__name__} 统计信息失败: {e}")

        return stats


class CSVReporter(BaseReporter):
    """
    CSV文件报告器
    将比对结果输出到CSV文件
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化CSV报告器

        Args:
            config: 配置字典，包含output_dir、filename等
        """
        super().__init__(config)
        self.output_dir = config.get('output_dir', './exports/')
        self.filename = config.get('filename', 'comparison_results.csv')
        self.task_id = config.get('task_id', 'unknown')
        self.comparison_table = config.get('comparison_table', 'unknown')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 构建完整文件路径
        self.file_path = os.path.join(self.output_dir, f"{self.task_id}_{self.comparison_table}_{self.filename}")
        
        self.csv_file = None
        self.csv_writer = None
        self.record_count = 0
        
        logger.info(f"CSVReporter初始化: {self.file_path}")

    def open(self):
        """打开CSV文件"""
        try:
            import csv
            self.csv_file = open(self.file_path, 'w', newline='', encoding='utf-8')
            self.csv_writer = csv.writer(self.csv_file)
            
            # 写入CSV头部
            headers = ['task_id', 'table_name', 'record_key', 'status', 'field_name', 'source_value', 'target_value', 'timestamp']
            self.csv_writer.writerow(headers)
            
            logger.info(f"CSV文件打开成功: {self.file_path}")
            
        except Exception as e:
            logger.error(f"CSV文件打开失败: {e}")
            raise

    def close(self):
        """关闭CSV文件"""
        try:
            if self.csv_file:
                self.csv_file.close()
                logger.info(f"CSV文件关闭，共写入 {self.record_count} 条记录: {self.file_path}")
        except Exception as e:
            logger.error(f"CSV文件关闭失败: {e}")

    def report_diff(self, diff_result: DiffResult):
        """报告差异到CSV文件"""
        try:
            from datetime import datetime
            
            timestamp = datetime.now().isoformat()
            
            # 处理字段级别的差异
            if hasattr(diff_result, 'field_diffs') and diff_result.field_diffs:
                for field_name, (value_a, value_b) in diff_result.field_diffs.items():
                    row = [
                        self.task_id,
                        self.comparison_table,
                        diff_result.key,
                        'FIELD_DIFF',
                        field_name,
                        str(value_a) if value_a is not None else '',
                        str(value_b) if value_b is not None else '',
                        timestamp
                    ]
                    self.csv_writer.writerow(row)
                    self.record_count += 1
            else:
                # 记录级别差异
                row = [
                    self.task_id,
                    self.comparison_table,
                    diff_result.key,
                    diff_result.status,
                    '',
                    str(getattr(diff_result, 'value_a', '')) if hasattr(diff_result, 'value_a') else '',
                    str(getattr(diff_result, 'value_b', '')) if hasattr(diff_result, 'value_b') else '',
                    timestamp
                ]
                self.csv_writer.writerow(row)
                self.record_count += 1
                
            # 定期刷新文件
            if self.record_count % 1000 == 0:
                self.csv_file.flush()
                
        except Exception as e:
            logger.error(f"CSV报告差异失败: {e}")
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取CSV报告器性能统计"""
        return {
            'type': 'csv',
            'file_path': self.file_path,
            'record_count': self.record_count,
            'task_id': self.task_id,
            'comparison_table': self.comparison_table
        }


class ReporterFactory:
    """
    报告器工厂类
    根据配置创建相应的报告器实例
    """

    @staticmethod
    def create_reporter(config: Dict[str, Any]) -> BaseReporter:
        """
        创建报告器实例

        Args:
            config: 报告器配置

        Returns:
            报告器实例

        Raises:
            ValueError: 不支持的报告器类型
            RuntimeError: 报告器创建失败
        """
        reporter_type = config.get('type', 'sqlite').lower()
        
        try:
            if reporter_type == 'sqlite':
                return ReporterFactory._create_sqlite_reporter(config)
            elif reporter_type == 'postgresql':
                return ReporterFactory._create_postgresql_reporter(config)
            elif reporter_type == 'csv':
                return ReporterFactory._create_csv_reporter(config)
            elif reporter_type == 'multiple':
                return ReporterFactory._create_multi_reporter(config)
            else:
                raise ValueError(f"不支持的报告器类型: {reporter_type}")
                
        except Exception as e:
            logger.error(f"创建报告器失败: {reporter_type}, 错误: {e}")
            raise RuntimeError(f"报告器创建失败: {e}")

    @staticmethod
    def _create_sqlite_reporter(config: Dict[str, Any]) -> BaseReporter:
        """创建SQLite报告器"""
        from reporters.sqlite_reporter import SqliteReporter
        
        sqlite_config = {
            'db_path': config.get('db_path', f"task_{config.get('task_id', 'unknown')}_results.db"),
            'task_id': config.get('task_id'),
            'table_name': config.get('table_name', 'comparison_results'),
            'comparison_table': config.get('comparison_table', 'unknown'),
            'batch_size': config.get('batch_size', 10000),
            'high_performance_mode': config.get('high_performance_mode', True),
            'append_mode': config.get('append_mode', False),
            'silent_mode': config.get('silent_mode', True)
        }
        
        logger.info(f"创建SQLite报告器: {sqlite_config['db_path']}")
        return SqliteReporter(sqlite_config)

    @staticmethod
    def _create_postgresql_reporter(config: Dict[str, Any]) -> BaseReporter:
        """创建PostgreSQL报告器"""
        from reporters.postgres_reporter import PostgresReporter
        
        # 检查必需的PostgreSQL配置
        required_fields = ['host', 'port', 'database', 'username', 'password']
        missing_fields = [field for field in required_fields if not config.get(field)]
        
        if missing_fields:
            raise ValueError(f"PostgreSQL配置缺失必需字段: {missing_fields}")
        
        postgres_config = {
            'host': config['host'],
            'port': config['port'],
            'database': config['database'],
            'username': config['username'],
            'password': config['password'],
            'schema': config.get('schema', 'public'),
            'task_id': config.get('task_id'),
            'table_name': config.get('table_name', 'comparison_results'),
            'comparison_table': config.get('comparison_table', 'unknown'),
            'batch_size': config.get('batch_size', 50000),
            'use_copy': config.get('use_copy', True),
            'high_performance_mode': config.get('high_performance_mode', True),
            'append_mode': config.get('append_mode', False),
            'silent_mode': config.get('silent_mode', True)
        }
        
        logger.info(f"创建PostgreSQL报告器: {config['host']}:{config['port']}/{config['database']}")
        return PostgresReporter(postgres_config)

    @staticmethod
    def _create_csv_reporter(config: Dict[str, Any]) -> BaseReporter:
        """创建CSV报告器"""
        csv_config = {
            'output_dir': config.get('output_dir', './exports/'),
            'filename': config.get('filename', 'comparison_results.csv'),
            'task_id': config.get('task_id'),
            'comparison_table': config.get('comparison_table', 'unknown')
        }
        
        logger.info(f"创建CSV报告器: {csv_config['output_dir']}")
        return CSVReporter(csv_config)

    @staticmethod
    def _create_multi_reporter(config: Dict[str, Any]) -> BaseReporter:
        """创建多报告器"""
        reporters_config = config.get('reporters', [])
        if not reporters_config:
            raise ValueError("多报告器配置中没有指定子报告器")
        
        reporters = []
        for i, reporter_config in enumerate(reporters_config):
            try:
                # 继承父级配置
                merged_config = {**config, **reporter_config}
                # 移除多报告器特有的配置
                merged_config.pop('reporters', None)
                
                reporter = ReporterFactory.create_reporter(merged_config)
                reporters.append(reporter)
                logger.info(f"多报告器子报告器 {i+1} 创建成功: {reporter_config.get('type')}")
            except Exception as e:
                logger.error(f"多报告器子报告器 {i+1} 创建失败: {e}")
                # 继续创建其他报告器，不因单个失败而终止
        
        if not reporters:
            raise RuntimeError("多报告器中没有成功创建的子报告器")
        
        logger.info(f"创建多报告器，包含 {len(reporters)} 个子报告器")
        return MultiReporter(reporters)

    @staticmethod
    def create_with_fallback(primary_config: Dict[str, Any], 
                           fallback_config: Optional[Dict[str, Any]] = None) -> BaseReporter:
        """
        创建报告器，支持降级机制

        Args:
            primary_config: 主要报告器配置
            fallback_config: 降级报告器配置

        Returns:
            报告器实例
        """
        try:
            # 尝试创建主要报告器
            return ReporterFactory.create_reporter(primary_config)
        except Exception as e:
            logger.warning(f"主要报告器创建失败: {e}")
            
            if fallback_config:
                try:
                    logger.info("尝试创建降级报告器...")
                    return ReporterFactory.create_reporter(fallback_config)
                except Exception as fallback_e:
                    logger.error(f"降级报告器创建也失败: {fallback_e}")
                    raise RuntimeError(f"主要和降级报告器都创建失败: {e}, {fallback_e}")
            else:
                # 使用默认的SQLite报告器作为最后的降级选项
                logger.info("使用默认SQLite报告器作为最后降级选项...")
                default_config = {
                    'type': 'sqlite',
                    'task_id': primary_config.get('task_id', 'fallback'),
                    'comparison_table': primary_config.get('comparison_table', 'unknown')
                }
                return ReporterFactory.create_reporter(default_config)
