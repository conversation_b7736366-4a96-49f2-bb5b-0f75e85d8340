/**
 * WebSocket API for database tasks
 *
 * 提供数据库任务的WebSocket连接和消息处理
 * 重构版本 - 统一WebSocket连接管理和优化重连机制
 */

import { createWebSocket, buildWebSocketUrl, getWebSocket } from '@/utils/websocket';
import { WEBSOCKET_ENDPOINTS } from './endpoints';
import type { CompareTask } from '@/types/database';

/**
 * WebSocket连接状态枚举
 */
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * WebSocket连接配置接口
 */
export interface WebSocketConfig {
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  debug?: boolean;
}

/**
 * WebSocket连接信息接口
 */
export interface WebSocketConnection {
  id: string;
  url: string;
  status: WebSocketStatus;
  ws: any;
  config: WebSocketConfig;
  lastConnected?: Date;
  reconnectAttempts: number;
  close: () => void;
  send: (data: any) => void;
  // 新增方法以保持与前端代码的兼容性
  isConnected: () => boolean;
  getState: () => any;
  getReadyState: () => number;
}

/**
 * 统一WebSocket连接管理器
 */
export class DatabaseWebSocketManager {
  private connections = new Map<string, WebSocketConnection>();
  private taskConnections = new Map<string, Set<string>>(); // 任务到连接ID的映射
  private defaultConfig: WebSocketConfig = {
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000,
    debug: false
  };

  /**
   * 创建任务状态WebSocket连接
   * 支持同一任务的多个并发连接
   */
  createTaskConnection(
    taskId: string,
    callbacks: {
      onTaskUpdate?: (task: CompareTask) => void;
      onTaskCompletion?: (task: CompareTask) => void;
      onConnected?: () => void;
      onDisconnected?: () => void;
      onError?: (error: any) => void;
      shouldStopReconnect?: () => boolean; // 新增：检查是否应该停止重连
    } = {},
    config?: Partial<WebSocketConfig>
  ): WebSocketConnection {
    // 生成唯一连接ID，支持同任务多连接
    const connectionId = `task_${taskId}_${this.generateUniqueId()}`;
    const wsUrl = buildWebSocketUrl(WEBSOCKET_ENDPOINTS.TASK_STATUS(taskId));

    // 不关闭现有连接，支持多连接并存
    const finalConfig = { ...this.defaultConfig, ...config };

    const ws = createWebSocket(wsUrl, {
      onMessage: (data) => {
        this.handleTaskMessage(data, callbacks);
      },
      onOpen: () => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.CONNECTED);
        this.addTaskConnection(taskId, connectionId);
        console.log(`✅ 任务状态WebSocket已连接: ${taskId} (${connectionId})`);
        callbacks.onConnected?.();
      },
      onClose: (event) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.DISCONNECTED);
        this.removeTaskConnection(taskId, connectionId);
        console.log(`❌ 任务状态WebSocket已断开: ${taskId} (${connectionId}), 代码: ${event.code}`);
        callbacks.onDisconnected?.();
      },
      onError: (error) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.ERROR);
        console.error(`🚨 任务状态WebSocket错误: ${taskId} (${connectionId})`, error);
        callbacks.onError?.(error);
      },
      onReconnecting: (attempt, maxAttempts) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.RECONNECTING);
        console.log(`🔄 任务状态WebSocket重连中 (${attempt}/${maxAttempts}): ${taskId} (${connectionId})`);
      },
      onReconnectFailed: () => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.ERROR);
        console.error(`💥 任务状态WebSocket重连失败: ${taskId}`);
        callbacks.onError?.({
          message: '无法连接到服务器，请检查网络连接',
          timestamp: new Date().toISOString(),
          fatal: true
        });
      },
      shouldStopReconnect: callbacks.shouldStopReconnect, // 传递任务完成检查函数
      ...finalConfig
    });

    const connection: WebSocketConnection = {
      id: connectionId,
      url: wsUrl,
      status: WebSocketStatus.CONNECTING,
      ws,
      config: finalConfig,
      lastConnected: new Date(),
      reconnectAttempts: 0,
      close: () => ws.close(),
      send: (data) => ws.send(data),
      // 新增方法实现，代理到底层WebSocket控制器
      isConnected: () => ws.isConnected(),
      getState: () => ws.getState(),
      getReadyState: () => ws.getReadyState()
    };

    this.connections.set(connectionId, connection);
    return connection;
  }

  /**
   * 创建通知WebSocket连接
   */
  createNotificationConnection(
    callbacks: {
      onTaskUpdate?: (notification: any) => void;
      onConnected?: () => void;
      onDisconnected?: () => void;
      onError?: (error: any) => void;
    } = {},
    config?: Partial<WebSocketConfig>
  ): WebSocketConnection {
    const connectionId = 'notifications';
    const wsUrl = buildWebSocketUrl(WEBSOCKET_ENDPOINTS.TASK_NOTIFICATIONS);

    // 如果连接已存在，先关闭
    if (this.connections.has(connectionId)) {
      this.closeConnection(connectionId);
    }

    const finalConfig = { ...this.defaultConfig, ...config };

    const ws = createWebSocket(wsUrl, {
      onMessage: (data) => {
        this.handleNotificationMessage(data, callbacks);
      },
      onOpen: () => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.CONNECTED);
        console.log('✅ 任务通知WebSocket已连接');
        callbacks.onConnected?.();
      },
      onClose: (event) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.DISCONNECTED);
        console.log(`❌ 任务通知WebSocket已断开, 代码: ${event.code}`);
        callbacks.onDisconnected?.();
      },
      onError: (error) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.ERROR);
        console.error('🚨 任务通知WebSocket错误', error);
        callbacks.onError?.(error);
      },
      onReconnecting: (attempt, maxAttempts) => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.RECONNECTING);
        console.log(`🔄 任务通知WebSocket重连中 (${attempt}/${maxAttempts})`);
      },
      onReconnectFailed: () => {
        this.updateConnectionStatus(connectionId, WebSocketStatus.ERROR);
        console.error('💥 任务通知WebSocket重连失败');
        callbacks.onError?.({
          message: '无法连接到服务器，请检查网络连接',
          timestamp: new Date().toISOString(),
          fatal: true
        });
      },
      ...finalConfig
    });

    const connection: WebSocketConnection = {
      id: connectionId,
      url: wsUrl,
      status: WebSocketStatus.CONNECTING,
      ws,
      config: finalConfig,
      lastConnected: new Date(),
      reconnectAttempts: 0,
      close: () => ws.close(),
      send: (data) => ws.send(data),
      // 新增方法实现，代理到底层WebSocket控制器
      isConnected: () => ws.isConnected(),
      getState: () => ws.getState(),
      getReadyState: () => ws.getReadyState()
    };

    this.connections.set(connectionId, connection);
    return connection;
  }

  /**
   * 处理任务消息
   */
  private handleTaskMessage(data: any, callbacks: any) {
    switch (data.type) {
      case 'task_status':
        if (data.task && callbacks.onTaskUpdate) {
          callbacks.onTaskUpdate(data.task);
        }
        break;
      case 'task_completion':
        if (data.task && callbacks.onTaskCompletion) {
          callbacks.onTaskCompletion(data.task);
        }
        break;
      case 'error':
        if (callbacks.onError) {
          callbacks.onError({
            message: data.message || '服务器返回错误',
            serverError: data,
            timestamp: new Date().toISOString()
          });
        }
        break;
      default:
        console.warn('未知的任务消息类型:', data.type);
    }
  }

  /**
   * 处理通知消息
   */
  private handleNotificationMessage(data: any, callbacks: any) {
    switch (data.type) {
      case 'task_update':
        if (callbacks.onTaskUpdate) {
          callbacks.onTaskUpdate(data);
        }
        break;
      case 'error':
        if (callbacks.onError) {
          callbacks.onError({
            message: data.message || '服务器返回错误',
            serverError: data,
            timestamp: new Date().toISOString()
          });
        }
        break;
      default:
        console.warn('未知的通知消息类型:', data.type);
    }
  }

  /**
   * 更新连接状态
   */
  private updateConnectionStatus(connectionId: string, status: WebSocketStatus) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.status = status;
      if (status === WebSocketStatus.CONNECTED) {
        connection.lastConnected = new Date();
        connection.reconnectAttempts = 0;
      } else if (status === WebSocketStatus.RECONNECTING) {
        connection.reconnectAttempts++;
      }
    }
  }

  /**
   * 获取连接
   */
  getConnection(connectionId: string): WebSocketConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * 关闭连接
   */
  closeConnection(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.close();
      this.connections.delete(connectionId);
      return true;
    }
    return false;
  }

  /**
   * 关闭所有连接
   */
  closeAllConnections(): void {
    for (const [connectionId] of this.connections) {
      this.closeConnection(connectionId);
    }
  }

  /**
   * 获取连接统计信息（详细版本）
   */
  getDetailedConnectionStats() {
    const stats = {
      total: this.connections.size,
      connected: 0,
      disconnected: 0,
      reconnecting: 0,
      error: 0
    };

    for (const connection of this.connections.values()) {
      switch (connection.status) {
        case WebSocketStatus.CONNECTED:
          stats.connected++;
          break;
        case WebSocketStatus.DISCONNECTED:
          stats.disconnected++;
          break;
        case WebSocketStatus.RECONNECTING:
          stats.reconnecting++;
          break;
        case WebSocketStatus.ERROR:
          stats.error++;
          break;
      }
    }

    return stats;
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 添加任务连接映射
   */
  private addTaskConnection(taskId: string, connectionId: string): void {
    if (!this.taskConnections.has(taskId)) {
      this.taskConnections.set(taskId, new Set());
    }
    this.taskConnections.get(taskId)!.add(connectionId);
  }

  /**
   * 移除任务连接映射
   */
  private removeTaskConnection(taskId: string, connectionId: string): void {
    const connections = this.taskConnections.get(taskId);
    if (connections) {
      connections.delete(connectionId);
      if (connections.size === 0) {
        this.taskConnections.delete(taskId);
      }
    }
  }

  /**
   * 获取任务的所有连接
   */
  getTaskConnections(taskId: string): WebSocketConnection[] {
    const connectionIds = this.taskConnections.get(taskId) || new Set();
    return Array.from(connectionIds)
      .map(id => this.connections.get(id))
      .filter(conn => conn !== undefined) as WebSocketConnection[];
  }

  /**
   * 关闭任务的所有连接
   */
  closeTaskConnections(taskId: string): void {
    const connections = this.getTaskConnections(taskId);
    connections.forEach(conn => conn.close());
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats(): {
    totalConnections: number;
    activeConnections: number;
    taskConnections: Record<string, number>;
  } {
    const activeConnections = Array.from(this.connections.values())
      .filter(conn => conn.status === WebSocketStatus.CONNECTED).length;

    const taskConnections: Record<string, number> = {};
    this.taskConnections.forEach((connections, taskId) => {
      taskConnections[taskId] = connections.size;
    });

    return {
      totalConnections: this.connections.size,
      activeConnections,
      taskConnections
    };
  }
}

// 创建默认实例
export const databaseWebSocketManager = new DatabaseWebSocketManager();

// 向后兼容的函数
export function createTaskStatusWebSocket(
  taskId: string,
  callbacks: {
    onTaskUpdate?: (task: CompareTask) => void;
    onTaskCompletion?: (task: CompareTask) => void;
    onConnected?: () => void;
    onDisconnected?: () => void;
    onError?: (error: any) => void;
    shouldStopReconnect?: () => boolean; // 新增：检查是否应该停止重连
  } = {}
) {
  return databaseWebSocketManager.createTaskConnection(taskId, callbacks);
}

export function createTaskNotificationWebSocket(
  callbacks: {
    onTaskUpdate?: (notification: any) => void;
    onConnected?: () => void;
    onDisconnected?: () => void;
    onError?: (error: any) => void;
  } = {}
) {
  return databaseWebSocketManager.createNotificationConnection(callbacks);
}

export function closeTaskStatusWebSocket(taskId: string) {
  // 关闭任务的所有连接，而不是单个连接
  return databaseWebSocketManager.closeTaskConnections(taskId);
}

export function closeTaskNotificationWebSocket() {
  return databaseWebSocketManager.closeConnection('notifications');
}

export default {
  DatabaseWebSocketManager,
  databaseWebSocketManager,
  createTaskStatusWebSocket,
  createTaskNotificationWebSocket,
  closeTaskStatusWebSocket,
  closeTaskNotificationWebSocket,
  WebSocketStatus
};
