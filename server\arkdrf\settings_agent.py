"""
Agent标准架构设置 - 极简配置
在现有settings基础上添加Agent支持配置
"""

# Agent标准架构配置开关
# 设置为True启用Agent标准架构，False使用原有架构
USE_AGENT_STANDARD = False  # 默认关闭，可通过环境变量覆盖

# Agent服务配置
AGENT_BASE_URL = 'http://localhost:8001'
AGENT_TIMEOUT = 30
AGENT_FALLBACK_ENABLED = True  # Agent失败时回退到原有方式

# 从环境变量读取配置（生产环境）
import os
USE_AGENT_STANDARD = os.getenv('USE_AGENT_STANDARD', 'false').lower() == 'true'
AGENT_BASE_URL = os.getenv('AGENT_BASE_URL', 'http://localhost:8001')
AGENT_TIMEOUT = int(os.getenv('AGENT_TIMEOUT', '30'))

# 日志配置 - 添加Agent相关日志
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/agent.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'apps.database.agent_adapter': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# 如果启用Agent标准架构，添加相关配置
if USE_AGENT_STANDARD:
    print("🚀 Agent标准架构已启用")
    print(f"   Agent服务地址: {AGENT_BASE_URL}")
    print(f"   回退机制: {'启用' if AGENT_FALLBACK_ENABLED else '禁用'}")
else:
    print("📋 使用原有架构")
