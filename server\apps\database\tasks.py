import threading
import time
import logging
import os
import glob
import shutil
from django.utils import timezone
from django.conf import settings
from .models import CompareTask
from utils.db_utils import get_db_agent_client, log_task_result
from utils.websocket_manager import send_task_update

logger = logging.getLogger(__name__)

# 存储正在运行的任务计时器
_running_timers = {}

# 存储任务重试次数
_retry_counts = {}
# 最大重试次数
MAX_RETRY_COUNT = 5

# 存储已清理的任务，避免重复清理
_cleaned_tasks = set()

def cleanup_task_resources(task_id, external_id=None):
    """
    清理任务相关资源

    参数:
        task_id: 本地任务ID
        external_id: DB-Agent任务ID (可选)
    """
    # 检查是否已经清理过，避免重复清理
    cleanup_key = f"{task_id}_{external_id}" if external_id else str(task_id)
    if cleanup_key in _cleaned_tasks:
        logger.debug(f"任务资源已清理过，跳过: task_id={task_id}, external_id={external_id}")
        return

    logger.info(f"开始清理任务资源: task_id={task_id}, external_id={external_id}")

    # 标记为已清理
    _cleaned_tasks.add(cleanup_key)

    try:
        # 1. 停止轮询
        if task_id in _running_timers:
            try:
                # 尝试取消定时器
                if _running_timers[task_id].is_alive():
                    logger.debug(f"尝试取消定时器: task_id={task_id}")
                    # 无法直接停止Timer，但可以标记后忽略
                    pass
                # 从字典中移除
                del _running_timers[task_id]
                logger.info(f"已移除任务定时器: task_id={task_id}")
            except Exception as e:
                logger.warning(f"清理任务定时器失败: task_id={task_id}, error={str(e)}")

        # 清理重试计数
        if str(task_id) in _retry_counts:
            del _retry_counts[str(task_id)]
            logger.debug(f"已清理任务重试计数: task_id={task_id}")

        # 2. 清理临时文件
        try:
            # 获取临时文件目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp', str(task_id))

            # 检查目录是否存在
            if os.path.exists(temp_dir):
                # 删除目录及其内容
                shutil.rmtree(temp_dir, ignore_errors=True)
                logger.info(f"已删除任务临时目录: {temp_dir}")

            # 清理其他可能的临时文件
            patterns = [
                f"{task_id}_*.csv",
                f"{task_id}_*.json",
                f"{task_id}_*.log",
                f"{task_id}_*.tmp"
            ]

            for pattern in patterns:
                for file_path in glob.glob(os.path.join(settings.MEDIA_ROOT, 'temp', pattern)):
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除临时文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败: {file_path}, error={str(e)}")
        except Exception as e:
            logger.warning(f"清理临时文件时出错: task_id={task_id}, error={str(e)}")

        # 3. 清理WebSocket连接和通知（不再重复发送通知）
        try:
            # 清理WebSocket连接
            from utils.websocket_manager import websocket_manager
            websocket_manager.cleanup_task_connections(str(task_id))
            logger.debug(f"已清理任务WebSocket连接: task_id={task_id}")
        except Exception as e:
            logger.warning(f"清理WebSocket连接失败: task_id={task_id}, error={str(e)}")

        logger.info(f"任务资源清理完成: task_id={task_id}")

        # 定期清理已清理任务记录，避免内存泄漏
        if len(_cleaned_tasks) > 1000:  # 当记录超过1000个时清理一半
            old_tasks = list(_cleaned_tasks)[:500]
            for old_task in old_tasks:
                _cleaned_tasks.discard(old_task)
            logger.debug(f"已清理旧的清理记录，剩余: {len(_cleaned_tasks)}")
    except Exception as e:
        logger.error(f"清理任务资源时发生异常: task_id={task_id}, error={str(e)}")

def schedule_task_status_check(task_id, external_id, delay=5):
    """
    安排一个定时任务来检查比对任务的状态

    参数:
        task_id: 本地任务ID
        external_id: DB-Agent任务ID
        delay: 第一次检查的延迟时间(秒)

    注意:
        轮询会在以下情况下自动终止:
        1. 任务状态变为 'success'、'failed' 或 'stopped'
        2. Agent服务连续返回404错误或连接被拒绝
        3. 收到Agent返回的错误响应，包含error字段
        4. 重试次数超过MAX_RETRY_COUNT次
    """
    # 检查重试次数
    current_retries = _retry_counts.get(str(task_id), 0)
    if current_retries >= MAX_RETRY_COUNT:
        logger.warning(f"任务状态检查重试次数超过限制({MAX_RETRY_COUNT}次)，停止轮询: task_id={task_id}")

        try:
            # 获取任务并将其标记为失败
            task = CompareTask.objects.get(id=task_id)
            if task.status not in ['success', 'failed', 'stopped']:
                task.status = 'failed'
                task.end_time = timezone.now()
                task.message = f"轮询超时：连续{MAX_RETRY_COUNT}次重试后仍无法获取任务状态"
                task.save()
                logger.error(f"将任务标记为失败(轮询超时): task_id={task_id}")
        except Exception as e:
            logger.error(f"尝试标记超时任务为失败时出错: {str(e)}")

        # 清理任务资源
        cleanup_task_resources(task_id, external_id)
        return

    # 更新重试次数
    _retry_counts[str(task_id)] = current_retries + 1

    # 停止已有的计时器(如果存在)
    if task_id in _running_timers and _running_timers[task_id].is_alive():
        # 无法直接停止Timer，但可以标记后忽略
        logger.debug(f"已有的定时器正在运行: task_id={task_id}")

    # 创建新的计时器
    timer = threading.Timer(
        delay,
        check_task_status,
        args=[task_id, external_id]
    )
    timer.daemon = True  # 设为守护线程，不阻止程序退出
    timer.start()

    # 记录计时器
    _running_timers[task_id] = timer
    logger.info(f"已安排任务状态检查: task_id={task_id}, external_id={external_id}, 延迟={delay}秒, 重试次数={current_retries+1}/{MAX_RETRY_COUNT}")

def check_task_status(task_id, external_id):
    """
    检查DB-Agent比对任务的状态

    参数:
        task_id: 本地任务ID
        external_id: DB-Agent任务ID
    """
    try:
        # 获取本地任务
        try:
            task = CompareTask.objects.get(id=task_id)
        except CompareTask.DoesNotExist:
            logger.error(f"任务不存在: task_id={task_id}")
            # 清理任务资源
            cleanup_task_resources(task_id, external_id)
            return False

        # 如果任务已经完成或失败，则不需要继续检查
        if task.status in ['success', 'failed', 'stopped']:
            logger.info(f"任务已处于终态，无需检查: task_id={task_id}, status={task.status}")
            # 清理任务资源
            cleanup_task_resources(task_id, external_id)
            return True

        # 获取DB-Agent客户端并查询任务状态
        db_agent_client = get_db_agent_client()
        result = db_agent_client.get_task_status(external_id)

        # 检查是否请求失败或返回错误
        if not result.get('success', False):
            error_message = result.get('message', '未知错误')
            error_detail = result.get('detail', {})
            logger.error(f"获取任务状态失败: task_id={task_id}, external_id={external_id}, error={error_message}")

            # 检查是否是严重错误 - 包括连接错误、404或500错误、返回详细错误消息的情况
            is_critical_error = (
                'Connection refused' in error_message or
                '404 Not Found' in error_message or
                '500 Internal Server Error' in error_message or
                isinstance(error_detail, dict) and (error_detail.get('error') or error_detail.get('type'))
            )

            # 如果是严重错误，标记任务为失败并终止轮询
            if is_critical_error or _retry_counts.get(str(task_id), 0) >= MAX_RETRY_COUNT - 1:
                logger.error(f"检测到关键错误或超过重试阈值，将任务标记为失败: {error_message}")
                task.status = 'failed'
                task.end_time = timezone.now()

                # 构建更详细的错误信息
                error_info = {}

                # 获取错误信息
                if isinstance(error_detail, dict) and 'error' in error_detail:
                    error_info = error_detail.get('error', {})
                    if isinstance(error_info, dict):
                        error_type = error_info.get('type', 'UnknownError')
                        error_message = error_info.get('message', error_message)
                        task.message = f"Agent执行错误: [{error_type}] {error_message}"
                    else:
                        task.message = f"Agent执行错误: {error_message}"
                # 如果错误在result中，但不在error_detail中
                elif isinstance(error_detail, dict) and error_detail.get('success') is False and 'error' in error_detail:
                    error_info = error_detail.get('error', {})
                    if isinstance(error_info, dict):
                        error_type = error_info.get('type', 'UnknownError')
                        error_message = error_info.get('message', error_message)
                        task.message = f"Agent执行错误: [{error_type}] {error_message}"
                else:
                    task.message = f"检测到任务异常: {error_message}"

                # 处理task.result可能不存在的情况
                if hasattr(task, 'result'):
                    if task.result is None:
                        task.result = {}

                    # 添加错误信息到结果中
                    task.result['error'] = error_info or {
                        'type': 'ConnectionError' if 'Connection refused' in error_message else 'TaskExecutionError',
                        'message': error_message,
                        'detail': error_detail
                    }
                else:
                    # 如果对象没有result属性，使用自定义方法存储错误信息
                    logger.warning(f"任务对象缺少result属性，使用message字段存储错误信息")

                task.save()

                # 记录错误日志
                log_task_result(task_id, "error", {
                    "message": task.message,
                    "error": error_message,
                    "detail": error_detail
                })

                # 清理任务资源
                cleanup_task_resources(task_id, external_id)
                return True  # 返回True表示已处理完成，不再需要重试

            # 其他情况下10秒后重试，但不重置重试计数
            retry_count = _retry_counts.get(str(task_id), 0)
            retry_delay = min(5 * (retry_count + 1), 30)  # 延迟随重试次数增加，最大30秒
            schedule_task_status_check(task_id, external_id, delay=retry_delay)
            return False

        # 获取状态数据
        status_data = result.get('data', {})
        status = status_data.get('status')
        progress = status_data.get('progress', 0)
        message = status_data.get('message', '')

        # 状态获取成功，重置重试计数
        if str(task_id) in _retry_counts:
            _retry_counts[str(task_id)] = 0

        # 更新本地任务状态
        task.progress = progress

        # 处理task.result可能不存在的情况
        if hasattr(task, 'result'):
            if task.result is None:
                task.result = {}
            task.result.update(status_data)
        else:
            # 如果对象没有result属性，则只更新基本状态
            logger.debug(f"任务对象缺少result属性，仅更新基本状态")

        # 记录任务执行过程日志
        logger.info(f"任务状态更新: task_id={task_id}, external_id={external_id}, status={status}, progress={progress}%")

        # 检查响应中是否包含error字段(包括顶级error和status_data中的error)
        has_error = result.get('error') or status_data.get('error')

        # 如果状态数据中包含error字段或状态是failed/error，则表示任务出错
        if has_error or status in ['failed', 'error']:
            error_info = result.get('error') or status_data.get('error') or {}
            error_message = error_info.get('message', '未知错误') if isinstance(error_info, dict) else str(error_info)
            error_type = error_info.get('type', 'TaskError') if isinstance(error_info, dict) else 'TaskError'

            logger.error(f"任务执行出错: [{error_type}] {error_message}")
            task.status = 'failed'
            task.end_time = timezone.now()
            task.message = f"任务执行失败: {error_message}"

            # 添加错误信息到结果中
            if hasattr(task, 'result'):
                if task.result is None:
                    task.result = {}
                task.result['error'] = error_info

            task.save()

            # 记录错误日志
            log_task_result(task_id, "error", {
                "message": task.message,
                "error": error_info
            })

            # 清理定时器和重试计数
            if task_id in _running_timers:
                del _running_timers[task_id]
            if str(task_id) in _retry_counts:
                del _retry_counts[str(task_id)]
            return True  # 不再需要继续检查

        # 检查是否需要根据进度判断任务完成
        # 获取表级进度信息
        table_progress = []
        all_tables_completed = False

        try:
            progress_result = db_agent_client.get_task_table_progress(external_id)
            if progress_result.get('success', False):
                table_progress = progress_result.get('tables', [])
                logger.debug(f"获取到表级进度: {table_progress}")

                # 检查是否所有表都已完成（进度100%）
                if table_progress:
                    # 过滤掉"总计"行，只检查实际的表
                    actual_tables = [t for t in table_progress if t.get('tableName') != '总计']
                    if actual_tables:
                        all_tables_completed = all(t.get('progress', 0) >= 100 for t in actual_tables)
                        logger.debug(f"实际表进度检查: {[(t.get('tableName'), t.get('progress', 0)) for t in actual_tables]}")
                    else:
                        # 如果只有"总计"行，检查总计的进度
                        total_row = next((t for t in table_progress if t.get('tableName') == '总计'), None)
                        if total_row:
                            all_tables_completed = total_row.get('progress', 0) >= 100
                            logger.debug(f"总计行进度检查: {total_row.get('progress', 0)}%")
        except Exception as e:
            logger.debug(f"获取表级进度失败: {str(e)}")

        # 智能状态判断：基于状态字段或进度判断
        task_completed = (
            status in ['completed', 'success'] or  # 标准完成状态
            all_tables_completed or                # 所有表进度100%
            progress >= 100                        # 总进度100%
        )

        logger.info(f"任务完成检查: task_id={task_id}, status={status}, progress={progress}%, all_tables_completed={all_tables_completed}, task_completed={task_completed}")

        # 根据状态更新任务
        if task_completed:
            # 确保状态正确设置
            if not status or status in ['', 'running']:
                task.status = 'success'  # 使用模型中定义的成功状态
                logger.info(f"任务完成但状态为空或running，设置为success: task_id={task_id}")
            elif status == 'completed':
                task.status = 'success'  # 映射completed为success
                logger.info(f"任务completed状态映射为success: task_id={task_id}")
            else:
                task.status = status  # 保持原状态

            task.end_time = timezone.now()
            task.message = "比对完成"

            # 获取比对结果详情
            try:
                results_result = db_agent_client.get_task_result(external_id)
                if results_result.get('success', False):
                    result_data = results_result.get('data', {})

                    # 处理result属性可能不存在的情况
                    if hasattr(task, 'result'):
                        if task.result is None:
                            task.result = {}
                        task.result.update(result_data)

                    # 提取并保存任务结果统计信息
                    summary = result_data.get('summary', {})
                    if summary:
                        task.total_records = summary.get('source_count', 0)
                        task.processed_records = summary.get('source_count', 0)
                        task.matched_records = summary.get('matched_count', 0)
                        task.different_records = summary.get('diff_count', 0)
                        task.message = (f"比对完成，总记录: {task.total_records}, "
                                      f"匹配: {task.matched_records}, "
                                      f"差异: {task.different_records}")
                        logger.info(f"任务结果统计: {task.message}")
            except Exception as e:
                logger.exception(f"获取任务结果失败: task_id={task_id}, error={str(e)}")

            # 先保存任务状态，确保状态更新到数据库
            task.save()

            # 发送WebSocket通知
            send_task_update(task)

            # 清理任务资源
            cleanup_task_resources(task_id, external_id)

        elif status == 'failed' or status == 'error':
            task.status = 'failed'  # 使用模型中定义的失败状态
            task.end_time = timezone.now()
            task.message = f"比对失败: {message}"

            # 获取比对结果详情
            try:
                results_result = db_agent_client.get_task_result(external_id)
                if results_result.get('success', False):
                    result_data = results_result.get('data', {})

                    # 处理result属性可能不存在的情况
                    if hasattr(task, 'result'):
                        if task.result is None:
                            task.result = {}
                        task.result.update(result_data)

                    # 提取并保存任务结果统计信息
                    summary = result_data.get('summary', {})
                    if summary:
                        task.total_records = summary.get('source_count', 0)
                        task.processed_records = summary.get('source_count', 0)
                        task.matched_records = summary.get('matched_count', 0)
                        task.different_records = summary.get('diff_count', 0)
                        task.message = (f"比对失败，总记录: {task.total_records}, "
                                      f"匹配: {task.matched_records}, "
                                      f"差异: {task.different_records}")
                        logger.info(f"任务结果统计: {task.message}")

                    # 检查结果中是否有错误信息
                    error_info = result_data.get('error', {})
                    if error_info:
                        error_type = error_info.get('type', 'UnknownError')
                        error_message = error_info.get('message', '未知错误')
                        task.message = f"比对失败: [{error_type}] {error_message}"
            except Exception as e:
                logger.exception(f"获取任务结果失败: task_id={task_id}, error={str(e)}")

            # 清理任务资源
            cleanup_task_resources(task_id, external_id)

        task.save()

        # 如果任务尚未完成，则继续安排检查
        if status not in ['completed', 'failed', 'success', 'error', 'stopped']:
            # 根据进度决定检查间隔
            if progress < 25:
                check_interval = 5  # 初始检查间隔，秒
            elif progress < 75:
                check_interval = 10  # 中进度检查间隔，秒
            else:
                check_interval = 5   # 最终检查间隔，秒

            logger.debug(f"任务未完成，{check_interval}秒后重新检查: task_id={task_id}, progress={progress}%")
            schedule_task_status_check(task_id, external_id, delay=check_interval)
        else:
            # 清理任务资源
            cleanup_task_resources(task_id, external_id)

        return True

    except Exception as e:
        logger.exception(f"检查任务状态时发生异常: task_id={task_id}, error={str(e)}")

        try:
            # 获取任务并添加错误信息
            task = CompareTask.objects.get(id=task_id)
            # 检测连续异常次数，超过阈值时终止任务
            current_retries = _retry_counts.get(str(task_id), 0)

            # 如果是关键错误或重试次数已接近上限，立即标记任务失败
            if current_retries >= MAX_RETRY_COUNT - 1 or isinstance(e, (ConnectionError, TimeoutError)):
                task.status = 'failed'
                task.end_time = timezone.now()
                task.message = f"任务状态检查失败: {str(e)}"
                if hasattr(task, 'result') and task.result is not None:
                    task.result['error'] = {
                        'type': type(e).__name__,
                        'message': str(e)
                    }
                task.save()

                # 清理任务资源
                cleanup_task_resources(task_id, external_id)
                return False
        except Exception as update_error:
            logger.error(f"尝试更新任务状态时发生错误: {str(update_error)}")

        # 增加延迟时间，给系统更多恢复时间
        retry_delay = min(30 * (current_retries + 1), 300)  # 延迟随重试次数增加，最大5分钟

        # 不立即清理计数器，让重试计数机制处理终止逻辑
        schedule_task_status_check(task_id, external_id, delay=retry_delay)
        return False