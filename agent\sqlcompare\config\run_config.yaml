# config/run_config.yaml

task:
  type: 'from_config' # 'from_config' or 'from_model'
  config_path: 'config/kgdb2kgdb.xml' # for 'from_config' type
  user_name: 'yaml_user'

  # for 'from_model' type
  # model_id: 1
  # task_name: 'Task from Model 1'
  # description: 'A task created from a model via YAML config'

reporters:
  # 指定要使用的报告器名称列表
  active: ['default_sqlite', 'daily_csv']

  # 定义所有可用的报告器配置
  configurations:
    default_sqlite:
      type: 'sqlite'
      db_path: 'reports/comparison_results.db'
      fallback_to_sqlite: false

    daily_csv:
      type: 'csv'
      output_dir: 'reports/csv_exports/'

    central_postgres:
      type: 'postgresql'
      host: 'localhost'
      port: 5432
      database: 'sqlcompare_results'
      username: 'reporter_user'
      password: 'secure_password'
      fallback_to_sqlite: true # 如果连接失败，回退到SQLite