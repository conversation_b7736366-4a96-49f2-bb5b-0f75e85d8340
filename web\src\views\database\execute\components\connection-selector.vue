<template>
  <div class="connection-selector-container">
    <div class="selector-header">
      <h3 class="section-title">数据库连接</h3>
      <el-button type="primary" link @click="openConnectionManager">
        <el-icon><Plus /></el-icon> 管理连接
      </el-button>
    </div>

    <el-tabs v-model="activeTab" class="connection-tabs">
      <el-tab-pane label="源数据库" name="source">
        <el-form :model="form" label-position="top" class="connection-form">
          <el-form-item label="选择源数据库连接" required>
            <el-select
              v-model="form.sourceId"
              filterable
              placeholder="选择源数据库连接"
              style="width: 100%"
              @change="handleSourceChange"
            >
              <el-option
                v-for="connection in filteredSourceConnections"
                :key="connection.id"
                :label="connection.name"
                :value="connection.id"
              >
                <div class="connection-option">
                  <span class="connection-name">{{ connection.name }}</span>
                  <span class="connection-info">
                    {{ connection.database_type }} | {{ connection.host }} | {{ connection.database }}
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.sourceId" label="连接信息">
            <ConnectionCard
              :connection="selectedSource"
              :loading="loadingSourceConnection"
              @test="testSourceConnection"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="目标数据库" name="target">
        <el-form :model="form" label-position="top" class="connection-form">
          <el-form-item label="选择目标数据库连接" required>
            <el-select
              v-model="form.targetId"
              filterable
              placeholder="选择目标数据库连接"
              style="width: 100%"
              @change="handleTargetChange"
            >
              <el-option
                v-for="connection in filteredTargetConnections"
                :key="connection.id"
                :label="connection.name"
                :value="connection.id"
              >
                <div class="connection-option">
                  <span class="connection-name">{{ connection.name }}</span>
                  <span class="connection-info">
                    {{ connection.database_type }} | {{ connection.host }} | {{ connection.database }}
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.targetId" label="连接信息">
            <ConnectionCard
              :connection="selectedTarget"
              :loading="loadingTargetConnection"
              @test="testTargetConnection"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 连接管理器弹窗 -->
    <el-dialog
      v-model="showConnectionManager"
      title="数据库连接管理"
      width="80%"
      :before-close="handleCloseConnectionManager"
      append-to-body
    >
      <ConnectionManager @connection-updated="refreshConnections" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, defineComponent, h } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ConnectionManager from '@/views/database/components/connection-manager.vue';
import { getConnectionList, testConnection } from '@/api/database';

// 自定义卡片组件用于显示连接信息
const ConnectionCard = defineComponent({
  props: {
    connection: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['test'],
  setup(props, { emit }) {
    const getDbTypeIcon = (type) => {
      const typeMap = {
        'mysql': 'icon-mysql',
        'postgresql': 'icon-postgresql',
        'oracle': 'icon-oracle',
        'sqlserver': 'icon-sqlserver',
        'db2': 'icon-db2'
      };
      return typeMap[type.toLowerCase()] || 'Database';
    };

    return () => h('div', { class: 'connection-card-wrapper' }, [
      h('div', { class: 'connection-info' }, [
        h('div', { class: 'connection-icon' }, [
          h('i', { class: `icon ${getDbTypeIcon(props.connection.database_type)}` })
        ]),
        h('div', { class: 'connection-details' }, [
          h('div', { class: 'detail-row' }, [
            h('span', { class: 'detail-label' }, '连接名称:'),
            h('span', { class: 'detail-value' }, props.connection.name)
          ]),
          h('div', { class: 'detail-row' }, [
            h('span', { class: 'detail-label' }, '数据库类型:'),
            h('span', { class: 'detail-value' }, props.connection.database_type)
          ]),
          h('div', { class: 'detail-row' }, [
            h('span', { class: 'detail-label' }, '主机地址:'),
            h('span', { class: 'detail-value' }, `${props.connection.host}:${props.connection.port}`)
          ]),
          h('div', { class: 'detail-row' }, [
            h('span', { class: 'detail-label' }, '数据库名称:'),
            h('span', { class: 'detail-value' }, props.connection.database)
          ]),
          h('div', { class: 'detail-row' }, [
            h('span', { class: 'detail-label' }, '用户名:'),
            h('span', { class: 'detail-value' }, props.connection.username)
          ]),
          h('div', { class: 'connection-actions' }, [
            h('button', {
              class: 'el-button el-button--primary el-button--small',
              onClick: () => emit('test'),
              disabled: props.loading
            }, '测试连接')
          ])
        ])
      ])
    ]);
  }
});

// 定义组件事件
const emit = defineEmits(['update:sourceId', 'update:targetId', 'change']);

// 组件属性
const props = defineProps({
  sourceId: {
    type: [String, Number],
    default: null
  },
  targetId: {
    type: [String, Number],
    default: null
  },
  filterSourceType: {
    type: String,
    default: null
  },
  filterTargetType: {
    type: String,
    default: null
  }
});

// 响应式状态
const activeTab = ref('source');
const showConnectionManager = ref(false);
const connections = ref([]);
const loadingConnections = ref(false);
const loadingSourceConnection = ref(false);
const loadingTargetConnection = ref(false);

const form = reactive({
  sourceId: props.sourceId,
  targetId: props.targetId
});

// 监听属性变化
watch(() => props.sourceId, (newVal) => {
  form.sourceId = newVal;
});

watch(() => props.targetId, (newVal) => {
  form.targetId = newVal;
});

// 监听表单变化
watch(() => form.sourceId, (newVal) => {
  emit('update:sourceId', newVal);
  emitChangeEvent();
});

watch(() => form.targetId, (newVal) => {
  emit('update:targetId', newVal);
  emitChangeEvent();
});

// 计算属性：过滤后的连接列表
const filteredSourceConnections = computed(() => {
  if (!props.filterSourceType) return connections.value;
  return connections.value.filter(connection =>
    connection.database_type.toLowerCase() === props.filterSourceType.toLowerCase()
  );
});

const filteredTargetConnections = computed(() => {
  if (!props.filterTargetType) return connections.value;
  return connections.value.filter(connection =>
    connection.database_type.toLowerCase() === props.filterTargetType.toLowerCase()
  );
});

// 选中的连接对象
const selectedSource = computed(() => {
  return connections.value.find(connection => connection.id === form.sourceId) || null;
});

const selectedTarget = computed(() => {
  return connections.value.find(connection => connection.id === form.targetId) || null;
});

// 获取数据库连接列表
const loadConnections = async () => {
  loadingConnections.value = true;

  try {
    const response = await getConnectionList();
    connections.value = (response.success && response.data) ? response.data : [];
  } catch (error) {
    console.error('加载数据库连接失败:', error);
    ElMessage.error('加载数据库连接失败，请重试');
  } finally {
    loadingConnections.value = false;
  }
};

// 刷新连接列表
const refreshConnections = async () => {
  await loadConnections();
  ElMessage.success('连接列表已更新');
};

// 打开连接管理器
const openConnectionManager = () => {
  showConnectionManager.value = true;
};

// 关闭连接管理器
const handleCloseConnectionManager = () => {
  showConnectionManager.value = false;
};

// 处理源连接变更
const handleSourceChange = (id) => {
  if (id === form.targetId) {
    ElMessage.warning('源数据库和目标数据库不能选择同一个连接');
  }
};

// 处理目标连接变更
const handleTargetChange = (id) => {
  if (id === form.sourceId) {
    ElMessage.warning('源数据库和目标数据库不能选择同一个连接');
  }
};

// 发出整体变更事件
const emitChangeEvent = () => {
  emit('change', {
    sourceId: form.sourceId,
    targetId: form.targetId,
    source_connection: selectedSource.value,
    target_connection: selectedTarget.value
  });
};

// 测试源数据库连接
const testSourceConnection = async () => {
  if (!selectedSource.value) return;

  loadingSourceConnection.value = true;

  try {
    await testConnection(selectedSource.value);
    ElMessage.success('源数据库连接测试成功');
  } catch (error) {
    console.error('源数据库连接测试失败:', error);
    ElMessage.error(`源数据库连接测试失败: ${error.message || '未知错误'}`);
  } finally {
    loadingSourceConnection.value = false;
  }
};

// 测试目标数据库连接
const testTargetConnection = async () => {
  if (!selectedTarget.value) return;

  loadingTargetConnection.value = true;

  try {
    await testConnection(selectedTarget.value);
    ElMessage.success('目标数据库连接测试成功');
  } catch (error) {
    console.error('目标数据库连接测试失败:', error);
    ElMessage.error(`目标数据库连接测试失败: ${error.message || '未知错误'}`);
  } finally {
    loadingTargetConnection.value = false;
  }
};

// 组件挂载时加载连接
onMounted(() => {
  loadConnections();
});
</script>

<style lang="scss" scoped>
.connection-selector-container {
  margin-bottom: 24px;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      height: 32px;
      line-height: 32px;
    }
  }

  .connection-tabs {
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 16px;
    background-color: #fff;

    .connection-form {
      margin-top: 8px;
    }
  }

  .connection-option {
    display: flex;
    flex-direction: column;

    .connection-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .connection-info {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  :deep(.connection-card) {
    .connection-info {
      display: flex;

      .connection-icon {
        font-size: 32px;
        color: var(--el-color-primary);
        margin-right: 16px;
        display: flex;
        align-items: center;
      }

      .connection-details {
        flex: 1;

        .detail-row {
          margin-bottom: 8px;
          display: flex;

          .detail-label {
            width: 100px;
            color: var(--el-text-color-secondary);
          }

          .detail-value {
            font-weight: 500;
          }
        }

        .connection-actions {
          margin-top: 12px;
        }
      }
    }
  }
}
</style>