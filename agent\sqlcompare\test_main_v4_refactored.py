#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_v4.py 重构功能测试脚本
验证多报告器配置功能的正确性
"""

import os
import sys
import time
import logging
import unittest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main_v4 import ComparisonTaskOrchestrator
from reporters.reporter_factory import ReporterFactory, MultiReporter, CSVReporter
from models.pydantic_models import TaskCreateDirect, TableRuleCreate

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestMainV4Refactored(unittest.TestCase):
    """main_v4.py重构功能测试类"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_url = f"sqlite:///{self.temp_dir}/test_sqlcompare.db"

    def test_default_reporter_config(self):
        """测试默认报告器配置"""
        try:
            orchestrator = ComparisonTaskOrchestrator(db_url=self.test_db_url)
            
            # 验证默认配置
            self.assertEqual(orchestrator.reporter_config['type'], 'sqlite')
            self.assertTrue(orchestrator.reporter_config['high_performance_mode'])
            self.assertTrue(orchestrator.reporter_config['silent_mode'])
            
            logger.info("✓ 默认报告器配置测试通过")
            
        except Exception as e:
            self.fail(f"默认报告器配置测试失败: {e}")

    def test_sqlite_reporter_config(self):
        """测试SQLite报告器配置"""
        try:
            # 测试类方法创建
            orchestrator1 = ComparisonTaskOrchestrator.create_with_sqlite_reporter(
                db_url=self.test_db_url,
                sqlite_db_path=f"{self.temp_dir}/test_sqlite.db"
            )
            
            self.assertEqual(orchestrator1.reporter_config['type'], 'sqlite')
            self.assertIn('test_sqlite.db', orchestrator1.reporter_config['db_path'])
            
            # 测试配置字典创建
            reporter_config = {
                'type': 'sqlite',
                'db_path': f"{self.temp_dir}/test_sqlite2.db",
                'batch_size': 5000
            }
            orchestrator2 = ComparisonTaskOrchestrator(
                db_url=self.test_db_url,
                reporter_config=reporter_config
            )
            
            self.assertEqual(orchestrator2.reporter_config['type'], 'sqlite')
            self.assertEqual(orchestrator2.reporter_config['batch_size'], 5000)
            
            logger.info("✓ SQLite报告器配置测试通过")
            
        except Exception as e:
            self.fail(f"SQLite报告器配置测试失败: {e}")

    def test_csv_reporter_config(self):
        """测试CSV报告器配置"""
        try:
            # 测试类方法创建
            orchestrator1 = ComparisonTaskOrchestrator.create_with_csv_reporter(
                db_url=self.test_db_url,
                output_dir=self.temp_dir
            )
            
            self.assertEqual(orchestrator1.reporter_config['type'], 'csv')
            self.assertEqual(orchestrator1.reporter_config['output_dir'], self.temp_dir)
            
            # 测试配置字典创建
            reporter_config = {
                'type': 'csv',
                'output_dir': self.temp_dir,
                'filename': 'test_results.csv'
            }
            orchestrator2 = ComparisonTaskOrchestrator(
                db_url=self.test_db_url,
                reporter_config=reporter_config
            )
            
            self.assertEqual(orchestrator2.reporter_config['filename'], 'test_results.csv')
            
            logger.info("✓ CSV报告器配置测试通过")
            
        except Exception as e:
            self.fail(f"CSV报告器配置测试失败: {e}")

    def test_multiple_reporters_config(self):
        """测试多报告器配置"""
        try:
            # 测试类方法创建（默认配置）
            orchestrator1 = ComparisonTaskOrchestrator.create_with_multiple_reporters(
                db_url=self.test_db_url
            )
            
            self.assertEqual(orchestrator1.reporter_config['type'], 'multiple')
            self.assertIsInstance(orchestrator1.reporter_config['reporters'], list)
            self.assertEqual(len(orchestrator1.reporter_config['reporters']), 2)
            
            # 测试自定义多报告器配置
            reporters_config = [
                {'type': 'sqlite', 'db_path': f"{self.temp_dir}/multi1.db"},
                {'type': 'csv', 'output_dir': self.temp_dir, 'filename': 'multi1.csv'}
            ]
            
            orchestrator2 = ComparisonTaskOrchestrator.create_with_multiple_reporters(
                db_url=self.test_db_url,
                reporters_config=reporters_config
            )
            
            self.assertEqual(len(orchestrator2.reporter_config['reporters']), 2)
            self.assertEqual(orchestrator2.reporter_config['reporters'][0]['type'], 'sqlite')
            self.assertEqual(orchestrator2.reporter_config['reporters'][1]['type'], 'csv')
            
            logger.info("✓ 多报告器配置测试通过")
            
        except Exception as e:
            self.fail(f"多报告器配置测试失败: {e}")

    def test_reporter_config_validation(self):
        """测试报告器配置验证"""
        try:
            # 测试无效的报告器类型
            with self.assertRaises(ValueError):
                invalid_config = {'type': 'invalid_type'}
                ComparisonTaskOrchestrator(
                    db_url=self.test_db_url,
                    reporter_config=invalid_config
                )
            
            # 测试多报告器缺少子报告器配置
            with self.assertRaises(ValueError):
                invalid_multi_config = {'type': 'multiple', 'reporters': []}
                ComparisonTaskOrchestrator(
                    db_url=self.test_db_url,
                    reporter_config=invalid_multi_config
                )
            
            logger.info("✓ 报告器配置验证测试通过")
            
        except Exception as e:
            self.fail(f"报告器配置验证测试失败: {e}")

    def test_reporter_factory_creation(self):
        """测试报告器工厂创建功能"""
        try:
            # 测试SQLite报告器创建
            sqlite_config = {
                'type': 'sqlite',
                'task_id': 'test_task',
                'comparison_table': 'test_table',
                'db_path': f"{self.temp_dir}/factory_test.db"
            }
            sqlite_reporter = ReporterFactory.create_reporter(sqlite_config)
            self.assertIsNotNone(sqlite_reporter)
            
            # 测试CSV报告器创建
            csv_config = {
                'type': 'csv',
                'task_id': 'test_task',
                'comparison_table': 'test_table',
                'output_dir': self.temp_dir
            }
            csv_reporter = ReporterFactory.create_reporter(csv_config)
            self.assertIsInstance(csv_reporter, CSVReporter)
            
            # 测试多报告器创建
            multi_config = {
                'type': 'multiple',
                'task_id': 'test_task',
                'comparison_table': 'test_table',
                'reporters': [
                    {'type': 'sqlite', 'db_path': f"{self.temp_dir}/multi_test.db"},
                    {'type': 'csv', 'output_dir': self.temp_dir}
                ]
            }
            multi_reporter = ReporterFactory.create_reporter(multi_config)
            self.assertIsInstance(multi_reporter, MultiReporter)
            
            logger.info("✓ 报告器工厂创建测试通过")
            
        except Exception as e:
            self.fail(f"报告器工厂创建测试失败: {e}")

    def test_reporter_fallback_mechanism(self):
        """测试报告器降级机制"""
        try:
            # 测试PostgreSQL失败时降级到SQLite
            primary_config = {
                'type': 'postgresql',
                'host': 'invalid_host',
                'port': 9999,
                'database': 'invalid_db',
                'username': 'invalid_user',
                'password': 'invalid_pass',
                'task_id': 'test_task',
                'comparison_table': 'test_table'
            }
            
            fallback_config = {
                'type': 'sqlite',
                'task_id': 'test_task',
                'comparison_table': 'test_table',
                'db_path': f"{self.temp_dir}/fallback_test.db"
            }
            
            # 应该成功创建降级报告器
            reporter = ReporterFactory.create_with_fallback(primary_config, fallback_config)
            self.assertIsNotNone(reporter)
            
            logger.info("✓ 报告器降级机制测试通过")
            
        except Exception as e:
            logger.warning(f"报告器降级机制测试跳过: {e}")

    @patch('main_v4.DB2Connector')
    @patch('main_v4.compare_sources_memory_dict')
    def test_create_reporter_for_task(self, mock_compare, mock_connector):
        """测试为任务创建报告器"""
        try:
            # 配置模拟
            mock_connector.return_value = Mock()
            mock_compare.return_value = None
            
            # 创建任务编排器
            reporter_config = {
                'type': 'sqlite',
                'batch_size': 1000
            }
            orchestrator = ComparisonTaskOrchestrator(
                db_url=self.test_db_url,
                reporter_config=reporter_config
            )
            
            # 测试创建报告器
            rule = {
                'table_id': 'test_table',
                'table_name': '测试表',
                'sql_1': 'SELECT * FROM test_table',
                'sql_2': 'SELECT * FROM test_table'
            }
            
            reporter = orchestrator._create_reporter_for_task('test_task_001', rule)
            self.assertIsNotNone(reporter)
            
            logger.info("✓ 为任务创建报告器测试通过")
            
        except Exception as e:
            self.fail(f"为任务创建报告器测试失败: {e}")

    def test_statistics_collection(self):
        """测试统计信息收集"""
        try:
            orchestrator = ComparisonTaskOrchestrator(db_url=self.test_db_url)
            
            # 创建模拟报告器
            mock_reporter = Mock()
            mock_reporter.get_performance_stats.return_value = {
                'total_records': 1000,
                'batch_size': 100
            }
            
            # 创建模拟连接器
            mock_source = Mock()
            mock_target = Mock()
            mock_source._stats = {'total_fetched': 500}
            mock_target._stats = {'total_fetched': 500}
            
            # 测试统计收集
            stats = orchestrator._collect_comparison_statistics(
                mock_reporter, mock_source, mock_target, 10.5
            )
            
            self.assertIn('total_records', stats)
            self.assertIn('exec_time', stats)
            self.assertIn('reporter_type', stats)
            self.assertEqual(stats['exec_time'], 10.5)
            
            logger.info("✓ 统计信息收集测试通过")
            
        except Exception as e:
            self.fail(f"统计信息收集测试失败: {e}")


def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("main_v4.py重构功能集成测试")
    print("=" * 60)
    
    try:
        # 测试基本功能
        print("测试基本功能...")
        temp_dir = tempfile.mkdtemp()
        test_db_url = f"sqlite:///{temp_dir}/integration_test.db"
        
        # 创建不同类型的任务编排器
        sqlite_orchestrator = ComparisonTaskOrchestrator.create_with_sqlite_reporter(
            db_url=test_db_url,
            sqlite_db_path=f"{temp_dir}/integration_sqlite.db"
        )
        print("✓ SQLite任务编排器创建成功")
        
        csv_orchestrator = ComparisonTaskOrchestrator.create_with_csv_reporter(
            db_url=test_db_url,
            output_dir=temp_dir
        )
        print("✓ CSV任务编排器创建成功")
        
        multi_orchestrator = ComparisonTaskOrchestrator.create_with_multiple_reporters(
            db_url=test_db_url
        )
        print("✓ 多报告器任务编排器创建成功")
        
        # 验证配置
        assert sqlite_orchestrator.reporter_config['type'] == 'sqlite'
        assert csv_orchestrator.reporter_config['type'] == 'csv'
        assert multi_orchestrator.reporter_config['type'] == 'multiple'
        print("✓ 配置验证通过")
        
        print("\n所有集成测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("main_v4.py重构功能测试套件")
    print("=" * 50)
    
    # 运行集成测试
    if not run_integration_test():
        return
    
    print("\n" + "=" * 50)
    print("运行详细单元测试")
    print("=" * 50)
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)


if __name__ == "__main__":
    main()
