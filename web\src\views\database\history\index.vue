<template>
  <div class="maincontent">
    <!-- 搜索表单 -->
    <div class="top">
      <el-form :inline="true" :model="searchForm" class="searchform">
        <el-form-item label="任务ID：">
          <el-input v-model="searchForm.taskId" placeholder="请输入任务ID" clearable class="!w-[180px]" />
        </el-form-item>

        <el-form-item label="比对模型：">
          <el-select v-model="searchForm.modelId" placeholder="请选择比对模型" clearable class="!w-[180px]">
            <el-option
              v-for="item in modelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态：">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="!w-[180px]">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="进行中" value="running" />
            <el-option label="等待中" value="waiting" />
          </el-select>
        </el-form-item>

        <el-form-item label="执行时间：">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格数据区域 -->
    <div ref="tableContainer" class="table">
      <el-table
        :data="tableData"
        border
        class="el-table"
        :show-overflow-tooltip="true"
        v-loading="loading"
        :height="tableMaxHeight"
      >
        <el-table-column prop="id" label="任务ID" width="180" align="center" />
        <el-table-column prop="model_name" label="比对模型" width="180" align="center" />
        <el-table-column prop="source_type" label="源类型" width="140" align="center">
          <template #default="scope">
            <el-tag v-if="getDbTypeColor(scope.row.source_type)" :type="getDbTypeColor(scope.row.source_type)">
              {{ getDbTypeName(scope.row.source_type) }}
            </el-tag>
            <el-tag v-else>
              {{ getDbTypeName(scope.row.source_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="target_type" label="目标类型" width="140" align="center">
          <template #default="scope">
            <el-tag v-if="getDbTypeColor(scope.row.target_type)" :type="getDbTypeColor(scope.row.target_type)">
              {{ getDbTypeName(scope.row.target_type) }}
            </el-tag>
            <el-tag v-else>
              {{ getDbTypeName(scope.row.target_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_records" label="总记录数" width="100" align="center" />
        <el-table-column prop="match_rate" label="一致率" width="90" align="center">
          <template #default="scope">
            <div class="match-rate">
              <div class="progress-bar">
                <div
                  class="progress"
                  :style="{ width: `${scope.row.match_rate}%`, backgroundColor: getMatchRateColor(scope.row.match_rate) }"
                ></div>
              </div>
              <span class="text">{{ scope.row.match_rate }}%</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180" align="center" />
        <el-table-column prop="duration" label="执行耗时" width="120" align="center" />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <div class="ellink">
              <el-link
                v-if="scope.row.status === 'success'"
                type="primary"
                underline="never"
                @click="viewResult(scope.row)"
              >
                查看结果
              </el-link>
              <el-link
                v-if="scope.row.status === 'running'"
                type="warning"
                underline="never"
                @click="viewProgress(scope.row)"
              >
                查看进度
              </el-link>
              <el-link type="primary" underline="never" @click="reExecute(scope.row)">重新执行</el-link>
              <el-link type="danger" underline="never" @click="deleteTask(scope.row)">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :layout="'total, sizes, prev, pager, next, jumper'"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="删除确认"
      width="30%"
    >
      <span>确定要删除该任务记录吗？此操作不可恢复。</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确认删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { getDbTypeColor, getDbTypeName } from "@/utils/db-utils";
import { getCompareTaskHistory, deleteCompareTask } from "@/api/database/compare";

defineOptions({
  name: "CompareHistory"
});

const router = useRouter();
const loading = ref(false);
const tableContainer = ref(null);
const tableMaxHeight = ref(0);

// 计算表格高度的函数
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainer.value) {
      // 获取父容器的高度
      const parentHeight = tableContainer.value.clientHeight;
      tableMaxHeight.value = parentHeight - 50; // 设置表格最大高度
    }
  });
};

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 搜索表单
const searchForm = reactive({
  taskId: '',
  modelId: '',
  status: '',
  timeRange: [] as string[]
});

// 模型选项
const modelOptions = ref([
  { value: 'model1', label: '用户数据比对' },
  { value: 'model2', label: '订单数据比对' },
  { value: 'model3', label: '产品数据比对' }
]);

// 表格数据
const tableData = ref([]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 删除对话框
const deleteDialogVisible = ref(false);
const currentTask = ref<any>(null);

// 获取状态样式
const getStatusType = (status: string): 'success' | 'danger' | 'warning' | 'info' | 'primary' => {
  const map: Record<string, 'success' | 'danger' | 'warning' | 'info' | 'primary'> = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning',
    'waiting': 'info'
  };
  return map[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: string): string => {
  const map: Record<string, string> = {
    'success': '成功',
    'failed': '失败',
    'running': '进行中',
    'waiting': '等待中'
  };
  return map[status] || '未知';
};

// 获取匹配率颜色
const getMatchRateColor = (rate: number): string => {
  if (rate >= 95) return '#67C23A';
  if (rate >= 80) return '#E6A23C';
  return '#F56C6C';
};

// 搜索操作
const handleSearch = () => {
  // 重置到第一页
  pagination.currentPage = 1;
  // 重新获取数据
  fetchData();
};

// 重置搜索表单
const handleReset = () => {
  searchForm.taskId = '';
  searchForm.modelId = '';
  searchForm.status = '';
  searchForm.timeRange = [];

  // 重新加载数据
  fetchData();
};

// 分页操作
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchData();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  fetchData();
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params: any = {
      page: pagination.currentPage,
      page_size: pagination.pageSize
    };

    // 添加搜索条件
    if (searchForm.taskId) {
      params.id = searchForm.taskId;
    }
    if (searchForm.status) {
      params.status = searchForm.status;
    }
    if (searchForm.timeRange && searchForm.timeRange.length === 2) {
      params.start_date = searchForm.timeRange[0];
      params.end_date = searchForm.timeRange[1];
    }

    // 调用API获取数据
    const response = await getCompareTaskHistory(params);

    // 检查响应格式并正确提取数据
    if (response && response.success) {
      // 从BaseApiResponse格式中提取data字段
      const responseData = response.data;
      const tasks = Array.isArray(responseData) ? responseData :
                   (responseData?.results || []);

      if (Array.isArray(tasks)) {
        tableData.value = tasks.map((task: any) => ({
          id: task.id,
          model_name: task.model_name || '未知模型',
          source_type: task.source_connection_type || task.source_connection?.type || 'Unknown',
          target_type: task.target_connection_type || task.target_connection?.type || 'Unknown',
          total_records: task.total_records || 0,
          match_rate: task.total_records > 0 ?
            Math.round((task.matched_records / task.total_records) * 100 * 100) / 100 : 0,
          status: task.status,
          create_time: task.create_time ?
            new Date(task.create_time).toLocaleString('zh-CN') : '',
          duration: task.duration || calculateDuration(task.start_time, task.end_time),
          // 保留原始数据用于操作
          _raw: task
        }));

        // 更新分页信息
        pagination.total = response.total || responseData?.count || tasks.length;
      } else {
        console.warn('API响应数据格式异常:', responseData);
        tableData.value = [];
        pagination.total = 0;
        ElMessage.warning('数据格式异常，请刷新重试');
      }
    } else {
      console.warn('API响应格式异常:', response);
      tableData.value = [];
      pagination.total = 0;
      ElMessage.warning('数据格式异常，请刷新重试');
    }

    ElMessage.success('数据加载成功');
  } catch (error) {
    console.error('获取任务历史失败:', error);
    ElMessage.error('获取任务历史失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
    // 数据加载完成后重新计算表格高度
    calculateTableHeight();
  }
};

// 计算任务持续时间
const calculateDuration = (startTime: string, endTime: string): string => {
  if (!startTime || !endTime) return '未知';

  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();

  if (diffMs < 1000) return '< 1秒';
  if (diffMs < 60000) return `${Math.round(diffMs / 1000)}秒`;
  if (diffMs < 3600000) return `${Math.round(diffMs / 60000)}分钟`;
  return `${Math.round(diffMs / 3600000)}小时`;
};

// 查看结果
const viewResult = (row: any) => {
  if (!row.id) {
    console.warn('任务ID为空，无法查看结果')
    ElMessage.warning('任务ID为空，无法查看结果')
    return
  }

  console.log(`从历史记录跳转到结果页面: taskId=${row.id}`)

  // 使用统一的URL参数格式 - taskId
  router.push({
    path: '/database/result',
    query: { taskId: row.id }
  });
};

// 查看进度
const viewProgress = (row: any) => {
  router.push({
    path: '/database/execute',
    query: { id: row.id, view: 'progress' }
  });
};

// 重新执行
const reExecute = (row: any) => {
  router.push({
    path: '/database/execute',
    query: { id: row.id, action: 'reexecute' }
  });
};

// 删除任务
const deleteTask = (row: any) => {
  currentTask.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentTask.value) return;

  loading.value = true;
  try {
    // 调用删除API
    await deleteCompareTask(currentTask.value.id);

    ElMessage.success('删除成功');
    deleteDialogVisible.value = false;

    // 重新获取数据
    fetchData();
  } catch (error) {
    console.error('删除任务失败:', error);
    ElMessage.error('删除任务失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  // 计算表格高度的函数并挂载监听事件
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);

  fetchData();
});

// 在组件卸载前移除监听器
onBeforeUnmount(() => {
  window.removeEventListener("resize", calculateTableHeight);
});
</script>

<style lang="scss" scoped>
:deep(.el-table .cell) {
  overflow: hidden; // 溢出隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap; // 规定段落中的文本不进行换行
}

.main-content {
  margin: 10px 10px 0 !important;
}

.maincontent {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 131px);
  background-color: var(--bg-color);
}

.left {
  min-width: 200px;
  margin-right: 10px;
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.searchform {
  background-color: var(--el-bg-color);
  .el-form-item {
    margin: 10px;
  }
}

.table {
  flex: 1;
  margin-top: 10px;
  background-color: var(--el-bg-color);
  height: 100%;
  /* 解决element表格在flex布局下无法自适应窗口宽度缩小的问题 */
  position: relative;
  .el-table {
    position: absolute;
  }
  .el-pagination {
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: center;
    bottom: 0;
    height: 50px;
  }
}

.ellink {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.match-rate {
  display: flex;
  align-items: center;
  gap: 8px;

  .progress-bar {
    width: 60px;
    height: 12px;
    background-color: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;

    .progress {
      height: 100%;
      border-radius: 6px;
    }
  }

  .text {
    font-size: 12px;
    color: #606266;
  }
}
</style>