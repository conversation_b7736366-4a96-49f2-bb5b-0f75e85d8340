"""
错误统计和分析模块

提供错误统计和分析功能，收集和分析错误信息，帮助开发人员发现和解决问题。
"""
import json
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set, Tuple
from collections import defaultdict, Counter
from django.utils import timezone
from .error_report import ErrorReport

logger = logging.getLogger(__name__)

class ErrorStats:
    """
    错误统计类

    收集和分析错误信息，提供错误统计和分析功能。
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ErrorStats, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化错误统计"""
        if self._initialized:
            return

        # 错误报告列表
        self.error_reports = []

        # 错误计数器
        self.error_counts = Counter()

        # 错误类型计数器
        self.error_type_counts = Counter()

        # 错误类别计数器
        self.error_category_counts = Counter()

        # 错误严重程度计数器
        self.error_severity_counts = Counter()

        # 任务错误计数器
        self.task_error_counts = defaultdict(Counter)

        # 最近错误时间
        self.last_error_time = None

        # 最近错误报告
        self.last_error_report = None

        # 错误率统计
        self.error_rates = {
            "1m": 0,  # 1分钟错误率
            "5m": 0,  # 5分钟错误率
            "15m": 0,  # 15分钟错误率
            "1h": 0,  # 1小时错误率
            "1d": 0   # 1天错误率
        }

        # 错误时间窗口
        self.error_windows = {
            "1m": [],  # 1分钟内的错误
            "5m": [],  # 5分钟内的错误
            "15m": [], # 15分钟内的错误
            "1h": [],  # 1小时内的错误
            "1d": []   # 1天内的错误
        }

        # 最大错误报告数量
        self.max_reports = 1000

        # 初始化完成标志
        self._initialized = True

        # 启动后台统计线程
        self._start_background_thread()

    def _start_background_thread(self):
        """启动后台统计线程"""
        def update_stats():
            while True:
                try:
                    # 更新错误率
                    self._update_error_rates()

                    # 清理过期错误报告
                    self._cleanup_old_reports()

                    # 每分钟更新一次
                    time.sleep(60)
                except Exception as e:
                    logger.error(f"错误统计后台线程异常: {str(e)}")
                    time.sleep(60)  # 发生异常时等待一分钟后重试

        # 创建后台线程
        thread = threading.Thread(target=update_stats, daemon=True)
        thread.start()

    def _update_error_rates(self):
        """更新错误率"""
        now = timezone.now()

        # 定义时间窗口
        windows = {
            "1m": now - timedelta(minutes=1),
            "5m": now - timedelta(minutes=5),
            "15m": now - timedelta(minutes=15),
            "1h": now - timedelta(hours=1),
            "1d": now - timedelta(days=1)
        }

        # 清理过期错误并计算错误率
        for window_name, window_start in windows.items():
            # 过滤出时间窗口内的错误
            window_errors = [
                report for report in self.error_reports
                if report.timestamp >= window_start.isoformat()
            ]

            # 更新错误窗口
            self.error_windows[window_name] = window_errors

            # 计算错误率（每分钟错误数）
            window_duration = (now - window_start).total_seconds() / 60  # 转换为分钟
            if window_duration > 0:
                self.error_rates[window_name] = len(window_errors) / window_duration
            else:
                self.error_rates[window_name] = 0

    def _cleanup_old_reports(self):
        """清理过期错误报告"""
        # 保留最近的错误报告
        if len(self.error_reports) > self.max_reports:
            # 按时间排序
            self.error_reports.sort(key=lambda report: report.timestamp, reverse=True)

            # 只保留最近的max_reports个报告
            self.error_reports = self.error_reports[:self.max_reports]

    def add_error(self, error_report: ErrorReport):
        """
        添加错误报告

        Args:
            error_report: 错误报告对象
        """
        # 添加错误报告
        self.error_reports.append(error_report)

        # 更新错误计数器
        self.error_counts["total"] += 1

        # 更新错误类型计数器
        self.error_type_counts[error_report.error_type] += 1

        # 更新错误类别计数器
        self.error_category_counts[error_report.category] += 1

        # 更新错误严重程度计数器
        self.error_severity_counts[error_report.severity] += 1

        # 更新任务错误计数器
        if error_report.task_id:
            self.task_error_counts[error_report.task_id]["total"] += 1
            self.task_error_counts[error_report.task_id][error_report.error_type] += 1

        # 更新最近错误时间
        self.last_error_time = timezone.now()

        # 更新最近错误报告
        self.last_error_report = error_report

        # 记录错误日志
        error_report.log()

    def get_stats(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            错误统计信息
        """
        return {
            "total_errors": self.error_counts["total"],
            "error_types": dict(self.error_type_counts),
            "error_categories": dict(self.error_category_counts),
            "error_severities": dict(self.error_severity_counts),
            "error_rates": self.error_rates,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None,
            "last_error": self.last_error_report.to_dict() if self.last_error_report else None
        }

    def get_task_stats(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务错误统计信息

        Args:
            task_id: 任务ID

        Returns:
            任务错误统计信息
        """
        if task_id not in self.task_error_counts:
            return {
                "total_errors": 0,
                "error_types": {},
                "task_id": task_id
            }

        return {
            "total_errors": self.task_error_counts[task_id]["total"],
            "error_types": {k: v for k, v in self.task_error_counts[task_id].items() if k != "total"},
            "task_id": task_id
        }

    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的错误报告

        Args:
            limit: 返回的错误报告数量

        Returns:
            最近的错误报告列表
        """
        # 按时间排序
        sorted_reports = sorted(self.error_reports, key=lambda report: report.timestamp, reverse=True)

        # 返回最近的limit个报告
        return [report.to_dict() for report in sorted_reports[:limit]]

    def get_error_trend(self, window: str = "1h") -> List[Tuple[str, int]]:
        """
        获取错误趋势

        Args:
            window: 时间窗口，可选值：1m, 5m, 15m, 1h, 1d

        Returns:
            错误趋势列表，每个元素为(时间, 错误数)
        """
        if window not in self.error_windows:
            return []

        # 获取时间窗口内的错误
        window_errors = self.error_windows[window]

        # 按时间分组
        error_counts = defaultdict(int)
        for report in window_errors:
            # 提取时间部分
            time_part = report.timestamp.split("T")[1][:5]  # 提取小时:分钟
            error_counts[time_part] += 1

        # 转换为列表并排序
        trend = [(time_str, count) for time_str, count in error_counts.items()]
        trend.sort()

        return trend

# 创建全局实例
try:
    error_stats = ErrorStats()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"初始化错误统计实例失败: {str(e)}")

    # 创建一个简单的替代实例，避免导入错误
    class SimpleErrorStats:
        def add_error(self, error_report):
            logger.error(f"错误报告: {error_report}")

        def get_stats(self):
            return {"total_errors": 0}

        def get_task_stats(self, task_id):
            return {"total_errors": 0, "task_id": task_id}

        def get_recent_errors(self, limit=10):
            return []

        def get_error_trend(self, window="1h"):
            return []

    error_stats = SimpleErrorStats()
