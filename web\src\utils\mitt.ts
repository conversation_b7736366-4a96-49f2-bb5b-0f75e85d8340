import type { Emitter } from "mitt";
import mitt from "mitt";

/** 全局公共事件需要在此处添加类型 */
type Events = {
  openPanel: string;
  tagOnClick: string;
  logoChange: boolean;
  tagViewsChange: string;
  changLayoutRoute: string;
  tagViewsShowModel: string;
  // 数据库连接相关事件
  connectionAdded: any; // 连接添加事件
  connectionUpdated: any; // 连接更新事件 
  connectionDeleted: string; // 连接删除事件 - 传递被删除的连接ID
  connectionsChanged: void; // 连接列表变更事件 - 通用事件，不需要参数
};

export const emitter: Emitter<Events> = mitt<Events>();
