<template>
  <el-card class="form-card compact-card same-height-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="card-title">数据源配置</span>
        <el-button link type="primary" @click="showConnectionManager">
          管理数据库连接
        </el-button>
      </div>
    </template>
    <!-- 数据源内部使用 el-row/col -->
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="source-config">
          <div class="source-title">源数据库</div>
          <el-form-item label="连接配置" prop="source_connection">
            <el-select
              v-model="sourceConnectionId"
              filterable
              placeholder="请选择源数据库连接"
              style="width: 100%"
              @update:modelValue="updateSourceConnection"
            >
              <el-option
                v-for="conn in sourceConnections"
                :key="conn.id"
                :label="conn.name"
                :value="String(conn.id)"
              >
                <div class="connection-option">
                  <span>{{ conn.name }}</span>
                  <el-tag size="small" effect="plain">{{ conn.type.toUpperCase() }}</el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="connection-display" v-if="sourceConnectionId">
              <span>当前选择: {{ getConnectionName(sourceConnectionId) }}</span>
            </div>
            <el-button
              link
              type="primary"
              @click="createNewConnection('source')"
              style="margin-left: 8px"
            >
              创建新连接
            </el-button>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="target-config">
          <div class="source-title">目标数据库</div>
          <el-form-item label="连接配置" prop="target_connection">
            <el-select
              v-model="targetConnectionId"
              filterable
              placeholder="请选择目标数据库连接"
              style="width: 100%"
              @update:modelValue="updateTargetConnection"
            >
              <el-option
                v-for="conn in targetConnections"
                :key="conn.id"
                :label="conn.name"
                :value="String(conn.id)"
              >
                <div class="connection-option">
                  <span>{{ conn.name }}</span>
                  <el-tag size="small" effect="plain">{{ conn.type.toUpperCase() }}</el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="connection-display" v-if="targetConnectionId">
              <span>当前选择: {{ getConnectionName(targetConnectionId) }}</span>
            </div>
            <el-button
              link
              type="primary"
              @click="createNewConnection('target')"
              style="margin-left: 8px"
            >
              创建新连接
            </el-button>
          </el-form-item>
        </div>
      </el-col>
    </el-row>

    <!-- 连接管理对话框 -->
    <el-dialog
      v-model="connectionManagerVisible"
      title="数据库连接管理"
      width="80%"
      destroy-on-close
    >
      <connection-manager
        ref="connectionManagerRef"
        @update="loadConnections"
      />
    </el-dialog>

    <!-- 连接创建对话框 -->
    <connection-dialog
      v-model:visible="connectionDialogVisible"
      :connection-data="currentConnection"
      @submit="handleConnectionSubmit"
      @test-connection="handleConnectionTest"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import ConnectionManager from '@/views/database/components/connection-manager.vue';
import ConnectionDialog from '@/views/database/components/connection-dialog.vue';
import { testConnection, createConnection } from '@/api/database/index';
import { useConnectionStore } from '@/store/modules/connection';
import type { DataConnection } from '@/types/database.d';
import { emitter } from '@/utils/mitt';

const props = defineProps<{
  source_connection: string | number;
  target_connection: string | number;
}>();

const emit = defineEmits<{
  (e: 'update:source_connection', value: string): void;
  (e: 'update:target_connection', value: string): void;
}>();

const connectionStore = useConnectionStore();

// 确保内部使用的是字符串类型
const sourceConnectionId = ref(props.source_connection ? String(props.source_connection) : '');
const targetConnectionId = ref(props.target_connection ? String(props.target_connection) : '');

// 连接列表
const sourceConnections = ref<DataConnection[]>([]);
const targetConnections = ref<DataConnection[]>([]);

// 连接管理器引用
const connectionManagerRef = ref();

// 对话框控制
const connectionManagerVisible = ref(false);
const connectionDialogVisible = ref(false);
const currentConnection = ref<DataConnection | undefined>(undefined);
const connectionMode = ref<'source' | 'target'>('source');

// 监听props变化，更新本地状态
watch(() => props.source_connection, (newVal) => {
  sourceConnectionId.value = newVal ? String(newVal) : '';
});

watch(() => props.target_connection, (newVal) => {
  targetConnectionId.value = newVal ? String(newVal) : '';
});

// 根据ID获取连接名称
const getConnectionName = (id: string): string => {
  // 将字符串id转换为数字
  const numId = parseInt(id, 10);
  const connection = connectionStore.connections.find(conn => conn.id === numId);
  return connection ? connection.name : id;
};

// 更新连接选择
const updateSourceConnection = (value: string) => {
  sourceConnectionId.value = value;
  emit('update:source_connection', value);
};

const updateTargetConnection = (value: string) => {
  targetConnectionId.value = value;
  emit('update:target_connection', value);
};

// 显示连接管理器
const showConnectionManager = () => {
  connectionManagerVisible.value = true;
};

// 创建新连接
const createNewConnection = (mode: 'source' | 'target') => {
  connectionMode.value = mode;
  currentConnection.value = undefined;
  connectionDialogVisible.value = true;
};

// 加载数据库连接列表
const loadConnections = async () => {
  // 如果存储中已有数据，无需重新加载
  if (connectionStore.isLoaded && connectionStore.connections.length > 0) {
    refreshConnectionLists();
    return;
  }

  try {
    // 从API加载连接列表数据
    await connectionStore.fetchConnections();
    // 刷新页面中的下拉选择框
    refreshConnectionLists();
  } catch (error) {
    console.error('加载数据库连接失败', error);
    ElMessage.error('加载数据库连接失败');
  }
};

// 刷新源和目标连接列表
const refreshConnectionLists = () => {
  // 所有连接都可以作为源或目标
  sourceConnections.value = [...connectionStore.connections];
  targetConnections.value = [...connectionStore.connections];

  // 如果没有连接数据，显示警告
  if (sourceConnections.value.length === 0) {
    ElMessage.warning('未找到数据库连接，请先创建连接');
  }
};

// 处理连接对话框提交
const handleConnectionSubmit = async (data: DataConnection) => {
  try {
    // 实际调用API保存连接
    const res = await createConnection(data);

    // 判断API返回是否成功
    if (res && res.code === 0) {
      ElMessage.success(res.message || '创建连接成功');

      // 获取新创建的连接ID
      const newConnectionId = res.data?.id || '';
      if (newConnectionId) {
        // 将新连接添加到Store
        const newConn = {
          ...data,
          id: newConnectionId
        };
        connectionStore.addConnection(newConn);

        // 触发连接添加事件
        emitter.emit('connectionAdded', newConn);

        // 立即刷新连接列表
        refreshConnectionLists();

        // 自动选择新创建的连接
        if (connectionMode.value === 'source') {
          sourceConnectionId.value = String(newConnectionId);
          emit('update:source_connection', String(newConnectionId));
        } else {
          targetConnectionId.value = String(newConnectionId);
          emit('update:target_connection', String(newConnectionId));
        }
      } else {
        console.warn('创建连接成功但未返回ID');
        // 即使没有ID也刷新连接列表
        refreshConnectionLists();
      }

      // 关闭对话框
      connectionDialogVisible.value = false;
    } else {
      ElMessage.error(res.message || '创建连接失败');
    }
  } catch (error: any) {
    console.error('创建连接失败:', error);
    let errorMessage = '未知错误';

    if (error.response && error.response.data) {
      errorMessage = error.response.data.message || '创建连接失败';
    } else if (error.message) {
      errorMessage = error.message;
    }

    ElMessage.error(`创建连接失败: ${errorMessage}`);
  }
};

// 处理连接测试
const handleConnectionTest = async (data: any) => {
  try {
    ElMessage.info('正在测试连接...');

    // 确保端口是数字类型
    const testData = {
      ...data,
      port: parseInt(data.port, 10)
    };

    const res = await testConnection(testData);
    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res && res.success) {
      ElMessage.success('测试连接成功');
    } else {
      ElMessage.error(`测试连接失败: ${res?.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('测试连接失败', error);
    ElMessage.error(`测试连接失败: ${error?.message || '未知错误'}`);
  }
};

// 组件挂载时加载连接列表
onMounted(async () => {
  // 加载连接列表
  await loadConnections();

  // 监听数据库连接相关事件
  emitter.on('connectionAdded', () => {
    loadConnections();
  });

  emitter.on('connectionUpdated', () => {
    loadConnections();
  });

  emitter.on('connectionDeleted', () => {
    loadConnections();
  });

  emitter.on('connectionsChanged', () => {
    loadConnections();
  });
});
</script>

<style lang="scss" scoped>
.form-card {
  border-radius: 0 !important;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border: none;
  background-color: var(--el-bg-color);

  &.compact-card {
    :deep(.el-card__body) {
      padding: 15px;
      flex-grow: 1;
    }
    :deep(.el-card__header) {
      padding: 10px 15px;
    }
    :deep(.el-form-item) {
      margin-bottom: 15px;
    }
    .source-title {
      margin-bottom: 8px;
    }
    .el-row > .el-col:last-child .el-form-item:last-child {
      margin-bottom: 0;
    }
    .el-row > .el-col:first-child .el-form-item:last-child {
      margin-bottom: 0;
    }
  }

  &.same-height-card {
    height: 100%;
  }

  &.table-config-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    margin-bottom: 0;
    margin-top: 0;
    padding: 0;
    background-color: var(--el-bg-color);
    width: 100%;
    box-sizing: border-box;

      :deep(.el-card__body) {
      flex: 1;
      overflow-y: auto;
      padding: 10px 15px;
    }
      :deep(.el-card__header) {
      padding: 10px 15px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      padding-right: 15px;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .action-buttons {
    display: flex;
    gap: 10px;
  }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}

.source-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--el-text-color-primary);
  border-left: 3px solid var(--el-color-primary);
  padding-left: 8px;
  margin-left: 15px;
}

.connection-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.connection-display {
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
</style>