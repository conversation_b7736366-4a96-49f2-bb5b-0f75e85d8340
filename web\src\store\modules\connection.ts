import { defineStore } from 'pinia';
import { getConnectionList } from '@/api/database/index';
import type { DataConnection } from '@/types/database';
import { ref, computed } from 'vue';

// 添加接口来存储带时间戳的删除ID
interface StoredDeletedId {
  id: string;
  timestamp: number;
}

// 定义删除ID的过期时间（10分钟）
const DELETE_ID_EXPIRY_TIME = 10 * 60 * 1000; // 10分钟，单位毫秒

interface ConnectionListResponse {
  data?: DataConnection[] | {
    data: DataConnection[];
    total: number;
  };
  success?: boolean;
  message?: string;
}

export const useConnectionStore = defineStore('connection', () => {
  // state
  const connections = ref<DataConnection[]>([]);
  const connectionTotal = ref(0);
  const loading = ref(false);
  const loaded = ref(false);
  const error = ref<string | null>(null);
  
  // getters
  const getConnections = computed(() => connections.value);
  
  const getConnectionById = (id: string | number) => {
    const stringId = String(id);
    
    // 检查ID是否在已删除列表中
    const deletedIds = getValidDeletedIds();
    if (deletedIds.some(item => item.id === stringId)) {
      return undefined; // 如果ID已被删除，返回undefined
    }
    
    return connections.value.find(conn => String(conn.id) === stringId);
  };
  
  const isLoaded = computed(() => loaded.value);
  
  const isLoading = computed(() => loading.value);

  // 获取有效的已删除ID列表（过滤掉过期的ID）
  function getValidDeletedIds(): StoredDeletedId[] {
    try {
      const now = Date.now();
      const deletedIdsStr = localStorage.getItem('deleted_connection_ids') || '[]';
      let deletedIds: StoredDeletedId[] = [];
      
      try {
        deletedIds = JSON.parse(deletedIdsStr);
        
        // 兼容旧格式：如果是字符串数组，转换为带时间戳的格式
        if (Array.isArray(deletedIds) && deletedIds.length > 0 && typeof deletedIds[0] === 'string') {
          deletedIds = (deletedIds as unknown as string[]).map(id => ({
            id,
            timestamp: now // 为旧数据添加当前时间戳
          }));
        }
      } catch (e) {
        console.error('解析已删除ID列表出错:', e);
        deletedIds = [];
      }
      
      // 确保格式正确
      if (!Array.isArray(deletedIds)) {
        console.warn('已删除ID列表格式错误，重置为空数组');
        deletedIds = [];
      }
      
      // 过滤掉过期的ID
      const validIds = deletedIds.filter(item => {
        // 检查项目是否有效
        if (!item || typeof item !== 'object' || !item.id || !item.timestamp) {
          return false;
        }
        return (now - item.timestamp) < DELETE_ID_EXPIRY_TIME;
      });
      
      // 如果有过期ID被过滤掉，更新存储
      if (validIds.length !== deletedIds.length) {
        localStorage.setItem('deleted_connection_ids', JSON.stringify(validIds));
        console.log(`已清理 ${deletedIds.length - validIds.length} 个过期的删除ID记录`);
      }
      
      return validIds;
    } catch (e) {
      console.error('处理已删除ID列表时出错:', e);
      localStorage.setItem('deleted_connection_ids', '[]');
      return [];
    }
  }
  
  // actions
  async function fetchConnections() {
    if (loading.value) return;
    
    try {
      loading.value = true;
      error.value = null;
      
      const res = await getConnectionList({
        page: 1,
        size: 100  // 获取足够多的连接
      }) as ConnectionListResponse;
      
      if (res && res.data) {
        // 获取有效的已删除连接ID列表
        const deletedIds = getValidDeletedIds();
        const deletedIdStrings = deletedIds.map(item => item.id);
        
        let fetchedConnections: DataConnection[] = [];
        
        // 处理响应结构可能的差异
        if (Array.isArray(res.data)) {
          fetchedConnections = res.data;
        } else if (typeof res.data === 'object' && 'data' in res.data && Array.isArray(res.data.data)) {
          fetchedConnections = res.data.data;
        } else {
          console.warn('连接数据格式不符合预期:', res);
          fetchedConnections = [];
        }
        
        // 过滤掉已删除的连接
        connections.value = fetchedConnections.filter(conn => 
          !deletedIdStrings.includes(String(conn.id))
        );
        connectionTotal.value = connections.value.length;
      } else {
        console.warn('API响应格式不符合预期:', res);
        connections.value = [];
        connectionTotal.value = 0;
      }
      
      loaded.value = true;
    } catch (err: any) {
      console.error('获取连接列表失败:', err);
      error.value = err.message || '获取连接列表失败';
      connections.value = [];
      connectionTotal.value = 0;
    } finally {
      loading.value = false;
    }
  }
  
  function addConnection(connection: DataConnection) {
    // 检查是否已存在相同ID的连接
    const index = connections.value.findIndex(conn => conn.id === connection.id);
    if (index >= 0) {
      // 更新现有连接
      connections.value[index] = { ...connections.value[index], ...connection };
    } else {
      // 添加新连接
      connections.value.push(connection);
      connectionTotal.value++;
    }
  }
  
  function updateConnection(id: string | number, connection: Partial<DataConnection>) {
    const index = connections.value.findIndex(conn => conn.id === id);
    if (index >= 0) {
      connections.value[index] = { ...connections.value[index], ...connection };
    }
  }
  
  function removeConnection(id: string | number) {
    // 确保任何类型的ID都能匹配
    const stringId = String(id);
    connections.value = connections.value.filter(conn => String(conn.id) !== stringId);
    connectionTotal.value = connections.value.length;
    
    // 获取当前有效的已删除ID列表
    const deletedIds = getValidDeletedIds();
    
    // 检查是否已存在，不存在则添加
    if (!deletedIds.some(item => item.id === stringId)) {
      deletedIds.push({
        id: stringId,
        timestamp: Date.now()
      });
      localStorage.setItem('deleted_connection_ids', JSON.stringify(deletedIds));
    }
  }
  
  // 添加同步已删除ID的方法
  function syncWithDeletedIds() {
    // 直接使用getValidDeletedIds方法，它会处理并返回有效的ID
    return getValidDeletedIds();
  }
  
  function clearConnections() {
    connections.value = [];
    connectionTotal.value = 0;
    loaded.value = false;
    
    // 同步已删除ID列表
    syncWithDeletedIds();
  }
  
  // 添加验证连接ID是否有效的方法
  function isValidConnectionId(id: string | number): boolean {
    const stringId = String(id);
    // 检查ID是否在已删除列表中
    const deletedIds = getValidDeletedIds();
    return !deletedIds.some(item => item.id === stringId);
  }
  
  return {
    // state
    connections,
    connectionTotal,
    loading,
    loaded,
    error,
    
    // getters
    getConnections,
    getConnectionById,
    isLoaded,
    isLoading,
    
    // actions
    fetchConnections,
    addConnection,
    updateConnection,
    removeConnection,
    clearConnections,
    isValidConnectionId,
    getValidDeletedIds
  };
}); 