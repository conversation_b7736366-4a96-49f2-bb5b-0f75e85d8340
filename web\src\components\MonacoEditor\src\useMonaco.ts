import { ref, onBeforeUnmount, watch, onMounted, Ref } from 'vue';
import { getOrCreateEditor } from '@/utils/monaco-cache';
import { getCurrentTheme } from '@/utils/monaco-theme';
import type * as Monaco from 'monaco-editor';
import type { MonacoEditorProps, MonacoEditorEmits } from './types';

// Monaco编辑器加载器类型
type MonacoType = typeof Monaco;

/**
 * Monaco编辑器加载钩子函数
 * 负责动态导入Monaco编辑器实例并初始化SQL语言支持
 */
export function useMonacoLoader(): {
  monaco: Ref<MonacoType | null>;
  loading: Ref<boolean>;
} {
  const monaco = ref<MonacoType | null>(null);
  const loading = ref(true);

  // 加载Monaco编辑器
  const loadMonaco = async () => {
    try {
      // 动态导入Monaco编辑器
      const module = await import('monaco-editor');
      monaco.value = module as unknown as MonacoType;

      // 自定义SQL语法高亮
      if (monaco.value?.languages) {
        try {
          const { registerSqlLanguage } = await import('./sql-language');
          registerSqlLanguage(monaco.value);
        } catch (err) {
          console.error('Failed to load SQL language configuration:', err);
        }
      }

      loading.value = false;
    } catch (err) {
      console.error('Failed to load Monaco editor:', err);
      loading.value = false;
    }
  };

  // 在组件挂载时加载Monaco
  onMounted(() => {
    loadMonaco();
  });

  return {
    monaco,
    loading,
  };
}

export type EditorMountedEvent = (editor: Monaco.editor.IStandaloneCodeEditor) => void;

export function useMonacoEditor(
  props: MonacoEditorProps,
  emit: MonacoEditorEmits,
  containerId: string
) {
  const loading = ref(true);
  let editor: Monaco.editor.IStandaloneCodeEditor | null = null;
  let themeObserver: MutationObserver | null = null;

  // 格式化代码
  const formatCode = () => {
    if (editor) {
      editor.getAction('editor.action.formatDocument')?.run();
    }
  };

  // 更新编辑器主题
  const updateEditorTheme = () => {
    // 忽略props.theme，总是检查系统主题
    const isDarkMode = 
      document.documentElement.classList.contains('dark') || 
      document.body.classList.contains('dark') ||
      document.documentElement.getAttribute('data-theme') === 'dark' ||
      document.body.getAttribute('data-theme') === 'dark' ||
      getComputedStyle(document.documentElement).getPropertyValue('--el-bg-color').trim() === '#141414' ||
      window.matchMedia('(prefers-color-scheme: dark)').matches;

    // 应用对应主题
    const theme = isDarkMode ? 'vs-dark' : 'vs';
    // 检查编辑器实例
    if (editor) {
      editor.updateOptions({ theme: theme });
    } else {
      // 如果编辑器实例还未准备好，延迟设置
      setTimeout(() => {
        if (editor) {
          editor.updateOptions({ theme: theme });
        }
      }, 100);
    }

    // 同时设置全局主题
    if (window.monaco?.editor) {
      window.monaco.editor.setTheme(theme);
    }
  };

  // 初始化编辑器
  const initMonaco = async (editorContainer: HTMLElement) => {
    try {
      loading.value = true;
      
      if (!editorContainer) return;
      
      // 默认配置
      const defaultOptions: Monaco.editor.IStandaloneEditorConstructionOptions = {
        language: props.language,
        theme: props.theme || getCurrentTheme(),
        readOnly: props.readOnly,
        automaticLayout: true,
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        scrollbar: {
          useShadows: false,
          verticalScrollbarSize: 10,
          horizontalScrollbarSize: 10,
        },
        fontSize: 14,
        tabSize: 2,
        lineNumbers: 'on',
        wordWrap: 'on',
        formatOnPaste: true,
        formatOnType: true,
        value: props.modelValue,
      };
      
      // 使用缓存创建编辑器
      editor = await getOrCreateEditor(
        containerId, 
        editorContainer, 
        { ...defaultOptions, ...props.options }
      );

      // 监听编辑器内容变化
      editor.onDidChangeModelContent(() => {
        const value = editor?.getValue() || '';
        if (value !== props.modelValue) {
          emit('update:modelValue', value);
          emit('change', value);
        }
      });

      // 获取monaco全局实例
      const monaco = (window as any).monaco;

      // 通知外部编辑器已挂载
      emit('editor-mounted', {
        editor: editor,
        monaco: monaco
      });
      
      // 监听主题变化
      setupThemeObserver();
      
      // 完成加载
      loading.value = false;
    } catch (error) {
      console.error('Monaco初始化失败:', error);
      loading.value = false;
    }
  };

  // 监听主题变化
  const setupThemeObserver = () => {
    // 先清理现有的观察器
    if (themeObserver) {
      themeObserver.disconnect();
      themeObserver = null;
    }

    // 创建MutationObserver来监听DOM变化
    themeObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || 
             mutation.attributeName === 'data-theme' || 
             mutation.attributeName === 'style')) {
          updateEditorTheme();
        }
      }
    });

    // 监听文档根元素的class和data-theme属性变化
    themeObserver.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class', 'data-theme', 'style'] 
    });
    
    // 也监听body元素
    themeObserver.observe(document.body, { 
      attributes: true, 
      attributeFilter: ['data-theme'] 
    });

    // 返回清理函数，而不是调用生命周期钩子
    return () => {
      themeObserver.disconnect();
    };
  };

  // 监听值变化
  watch(() => props.modelValue, (newValue) => {
    if (editor && editor.getValue() !== newValue) {
      editor.setValue(newValue);
      
      // 强制更新编辑器视图
      setTimeout(() => {
        editor?.layout();
        // 将光标移到文档末尾
        if (editor && !props.readOnly) {
          const position = editor.getModel()?.getPositionAt(newValue.length);
          if (position) {
            editor.setPosition(position);
            editor.revealPositionInCenter(position);
          }
        }
      }, 50);
    }
  }, { immediate: true });

  // 监听主题变化
  watch(() => props.theme, updateEditorTheme);

  // 生命周期钩子
  onBeforeUnmount(() => {
    // 断开主题观察器
    if (themeObserver) {
      themeObserver.disconnect();
      themeObserver = null;
    }
    
    // 不做完全销毁，只解除引用，实例由缓存模块管理
    editor = null;
  });

  return {
    loading,
    editor: () => editor,
    formatCode,
    initMonaco,
    updateEditorTheme
  };
} 