// 模拟后端动态生成路由
import { defineFakeRoute } from "vite-plugin-fake-server/client";

/**
 * roles：页面级别权限，这里模拟二种 "admin"、"common"
 * admin：管理员角色
 * common：普通角色
 */
const permissionRouter = {
  path: "/permission",
  meta: {
    title: "权限管理",
    icon: "ep:lollipop",
    rank: 10
  },
  children: [
    {
      path: "/permission/page/index",
      name: "PermissionPage",
      meta: {
        title: "页面权限",
        roles: ["admin", "common"]
      }
    },
    {
      path: "/permission/button",
      meta: {
        title: "按钮权限",
        roles: ["admin", "common"]
      },
      children: [
        {
          path: "/permission/button/router",
          component: "permission/button/index",
          name: "PermissionButtonRouter",
          meta: {
            title: "路由返回按钮权限",
            auths: [
              "permission:btn:add",
              "permission:btn:edit",
              "permission:btn:delete"
            ]
          }
        },
        {
          path: "/permission/button/login",
          component: "permission/button/perms",
          name: "PermissionButtonLogin",
          meta: {
            title: "登录接口返回按钮权限"
          }
        }
      ]
    }
  ]
};

// 数据比对模块路由
const dataCompareRouter = {
  path: "/database",
  meta: {
    icon: "ri:database-2-line",
    title: "数据比对",
    rank: 4
  },
  children: [
    {
      path: "/database/model",
      name: "comparemodel",
      component: "database/model/index",
      meta: {
        icon: "ri:settings-2-line",
        title: "比对模型",       
        keepAlive: true
      }
    },
    {
      path: "/database/execute",
      name: "compareexecute",
      component: "database/execute/index",
      meta: {
        icon: "ri:play-circle-line",
        title: "执行比对",
        keepAlive: true
      }
    },
    {
      path: "/database/result",
      name: "compareresult",
      component: "database/result/index",
      meta: {
        icon: "ri:file-text-line",
        title: "结果分析",
        keepAlive: true
      }
    },
    {
      path: "/database/history",
      name: "comparehistory",
      component: "database/history/index",
      meta: {
        icon: "ri:history-line",
        title: "历史记录",
        keepAlive: true
      }
    }
  ]
};

export default defineFakeRoute([
  {
    url: "/api/system/asyncroutes/fake",
    method: "get",
    response: () => {
      return {
        success: true,
        data: [permissionRouter]
      };
    }
  }
]);
