"""
Agent通信服务
简化的Agent交互接口，只负责发送任务请求和获取状态

设计原则：
1. 只发送任务创建和控制请求给Agent
2. 所有数据读取都从数据库获取（Agent写入的数据）
3. 不维护复杂的通信状态
4. 提供简单的错误处理和重试机制
"""
import requests
import logging
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.utils import timezone
import time

logger = logging.getLogger(__name__)


class AgentCommunicationService:
    """Agent通信服务 - 简化的HTTP通信"""
    
    def __init__(self):
        self.agent_base_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
        self.timeout = getattr(settings, 'AGENT_TIMEOUT', 30)
        self.retry_count = getattr(settings, 'AGENT_RETRY_COUNT', 3)
        self.retry_delay = getattr(settings, 'AGENT_RETRY_DELAY', 1)
    
    def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None, 
                     params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """发送HTTP请求到Agent"""
        url = f"{self.agent_base_url}{endpoint}"
        
        for attempt in range(self.retry_count):
            try:
                if method.upper() == 'GET':
                    response = requests.get(url, params=params, timeout=self.timeout)
                elif method.upper() == 'POST':
                    response = requests.post(url, json=data, timeout=self.timeout)
                elif method.upper() == 'PUT':
                    response = requests.put(url, json=data, timeout=self.timeout)
                elif method.upper() == 'DELETE':
                    response = requests.delete(url, timeout=self.timeout)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                if response.status_code < 400:
                    return response.json() if response.content else {}
                else:
                    logger.warning(f"Agent请求失败 (状态码: {response.status_code}): {response.text}")
                    return None
                    
            except requests.RequestException as e:
                logger.warning(f"Agent请求异常 (尝试 {attempt + 1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Agent请求最终失败: {e}")
                    return None
        
        return None
    
    def create_comparison_task(self, task_request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建比对任务"""
        logger.info(f"向Agent发送任务创建请求: {task_request}")
        
        result = self._make_request('POST', '/api/v1/tasks/create', data=task_request)
        
        if result:
            logger.info(f"任务创建成功: {result.get('task_id')}")
        else:
            logger.error("任务创建失败")
        
        return result
    
    def control_task(self, task_id: str, action: str) -> Optional[Dict[str, Any]]:
        """控制任务（启动、暂停、恢复、取消）"""
        logger.info(f"向Agent发送任务控制请求: {task_id} - {action}")
        
        result = self._make_request('POST', f'/api/v1/tasks/{task_id}/control', data={
            'action': action,
            'task_id': task_id
        })
        
        if result:
            logger.info(f"任务控制成功: {task_id} - {action}")
        else:
            logger.error(f"任务控制失败: {task_id} - {action}")
        
        return result
    
    def get_agent_health(self) -> Optional[Dict[str, Any]]:
        """获取Agent健康状态"""
        return self._make_request('GET', '/api/v1/health')
    
    def test_connection(self, connection_id: int) -> Optional[Dict[str, Any]]:
        """测试数据库连接"""
        logger.info(f"向Agent发送连接测试请求: {connection_id}")
        
        result = self._make_request('POST', f'/api/v1/connections/{connection_id}/test')
        
        if result:
            logger.info(f"连接测试完成: {connection_id}")
        else:
            logger.error(f"连接测试失败: {connection_id}")
        
        return result
    
    def get_task_logs(self, task_id: str, lines: int = 100) -> Optional[List[str]]:
        """获取任务日志"""
        result = self._make_request('GET', f'/api/v1/tasks/{task_id}/logs', params={
            'lines': lines
        })
        
        return result.get('logs', []) if result else None
    
    def cleanup_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """清理任务资源"""
        logger.info(f"向Agent发送任务清理请求: {task_id}")
        
        result = self._make_request('DELETE', f'/api/v1/tasks/{task_id}')
        
        if result:
            logger.info(f"任务清理成功: {task_id}")
        else:
            logger.error(f"任务清理失败: {task_id}")
        
        return result
    
    def is_agent_available(self) -> bool:
        """检查Agent是否可用"""
        try:
            health = self.get_agent_health()
            return health is not None
        except Exception:
            return False


class AgentTaskManager:
    """Agent任务管理器 - 高级任务管理功能"""
    
    def __init__(self):
        self.comm_service = AgentCommunicationService()
    
    def create_model_comparison_task(self, model_id: int, user_id: str, 
                                   table_rule_ids: List[int] = None,
                                   task_name: str = None) -> Optional[str]:
        """创建模型比对任务"""
        from ..models_agent_standard import ComparisonModel, ComparisonTableRule
        
        try:
            # 验证模型存在
            model = ComparisonModel.objects.get(id=model_id, status=True)
            
            # 如果没有指定表规则，使用模型的所有活跃规则
            if not table_rule_ids:
                table_rule_ids = list(
                    ComparisonTableRule.objects.filter(
                        model_id=model, is_active=True
                    ).values_list('id', flat=True)
                )
            
            if not table_rule_ids:
                logger.error(f"模型 {model_id} 没有可用的表规则")
                return None
            
            # 构建任务请求
            task_request = {
                'model_id': model_id,
                'table_rule_ids': table_rule_ids,
                'task_name': task_name or f"{model.name}_比对_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
                'user_id': user_id,
                'description': f"模型 {model.name} 的比对任务"
            }
            
            # 发送给Agent
            result = self.comm_service.create_comparison_task(task_request)
            
            if result:
                return result.get('task_id')
            else:
                return None
                
        except ComparisonModel.DoesNotExist:
            logger.error(f"模型 {model_id} 不存在或已禁用")
            return None
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None
    
    def batch_create_tasks(self, task_requests: List[Dict[str, Any]]) -> List[Optional[str]]:
        """批量创建任务"""
        task_ids = []
        
        for request in task_requests:
            task_id = self.create_model_comparison_task(
                model_id=request['model_id'],
                user_id=request['user_id'],
                table_rule_ids=request.get('table_rule_ids'),
                task_name=request.get('task_name')
            )
            task_ids.append(task_id)
        
        return task_ids
    
    def start_task(self, task_id: str) -> bool:
        """启动任务"""
        result = self.comm_service.control_task(task_id, 'start')
        return result is not None
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        result = self.comm_service.control_task(task_id, 'pause')
        return result is not None
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        result = self.comm_service.control_task(task_id, 'resume')
        return result is not None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        result = self.comm_service.control_task(task_id, 'cancel')
        return result is not None
    
    def cleanup_completed_tasks(self, days_old: int = 7) -> int:
        """清理完成的旧任务"""
        from ..models_agent_standard import ComparisonTask
        
        cutoff_date = timezone.now() - timezone.timedelta(days=days_old)
        
        old_tasks = ComparisonTask.objects.filter(
            status__in=['completed', 'failed', 'cancelled'],
            complete_time__lt=cutoff_date
        )
        
        cleaned_count = 0
        for task in old_tasks:
            if self.comm_service.cleanup_task(task.task_id):
                cleaned_count += 1
        
        return cleaned_count


# 全局服务实例
agent_comm_service = AgentCommunicationService()
agent_task_manager = AgentTaskManager()


def get_agent_communication_service() -> AgentCommunicationService:
    """获取Agent通信服务实例"""
    return agent_comm_service


def get_agent_task_manager() -> AgentTaskManager:
    """获取Agent任务管理器实例"""
    return agent_task_manager
