/**
 * 通用工具函数
 * 提供Element Plus表格组件和其他通用功能的工具函数
 */

/**
 * 获取表格溢出提示属性配置
 * 修复Element Plus表格组件鼠标悬停时的空指针异常
 * 
 * @param options 可选配置参数
 * @returns 表格溢出提示属性配置对象
 */
export function getTableOverflowTooltipProps(options: {
  placement?: string;
  effect?: string;
  enterable?: boolean;
  hideAfter?: number;
  showAfter?: number;
  tooltipFormatter?: (value: any) => string;
} = {}) {
  const defaultOptions = {
    placement: 'top',
    effect: 'dark',
    enterable: false,
    hideAfter: 200,
    showAfter: 500,
    // 提供默认的tooltipFormatter函数，防止空指针异常
    tooltipFormatter: (value: any) => {
      if (value === null || value === undefined) {
        return '';
      }
      return String(value);
    }
  };

  // 合并默认配置和用户配置
  const mergedOptions = { ...defaultOptions, ...options };

  // 确保tooltipFormatter始终存在且为函数
  if (typeof mergedOptions.tooltipFormatter !== 'function') {
    mergedOptions.tooltipFormatter = defaultOptions.tooltipFormatter;
  }

  return {
    placement: mergedOptions.placement,
    effect: mergedOptions.effect,
    enterable: mergedOptions.enterable,
    hideAfter: mergedOptions.hideAfter,
    showAfter: mergedOptions.showAfter,
    formatter: mergedOptions.tooltipFormatter
  };
}

/**
 * 安全的表格单元格内容格式化函数
 * 防止在表格单元格渲染时出现空指针异常
 * 
 * @param value 单元格值
 * @param defaultValue 默认值
 * @returns 格式化后的字符串
 */
export function safeTableCellFormatter(value: any, defaultValue: string = '-'): string {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  return String(value);
}

/**
 * 检查元素是否需要显示tooltip
 * 判断元素内容是否超出容器宽度
 * 
 * @param element HTML元素
 * @returns 是否需要显示tooltip
 */
export function shouldShowTooltip(element: HTMLElement): boolean {
  if (!element) return false;
  
  try {
    return element.scrollWidth > element.clientWidth;
  } catch (error) {
    console.warn('检查tooltip显示条件时出错:', error);
    return false;
  }
}

/**
 * 创建安全的Element Plus表格配置
 * 提供默认的tooltip配置，防止组件错误
 * 
 * @param customConfig 自定义表格配置
 * @returns 安全的表格配置对象
 */
export function createSafeTableConfig(customConfig: any = {}) {
  const defaultConfig = {
    showOverflowTooltip: true,
    tooltipOptions: getTableOverflowTooltipProps(),
    // 其他默认配置
    border: false,
    stripe: false,
    size: 'default',
    highlightCurrentRow: false
  };

  return { ...defaultConfig, ...customConfig };
}

/**
 * 防抖函数
 * 用于优化表格搜索等高频操作
 * 
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * 用于优化表格滚动等高频操作
 * 
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func.apply(this, args);
    }
  };
}

/**
 * 深度克隆对象
 * 用于避免对象引用问题
 * 
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 格式化文件大小
 * 将字节数转换为可读的文件大小格式
 * 
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 生成唯一ID
 * 用于组件key等场景
 * 
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export function generateUniqueId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
