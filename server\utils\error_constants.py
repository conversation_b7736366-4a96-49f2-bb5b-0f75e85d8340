"""
错误常量定义模块

定义系统中使用的错误类别、严重程度和错误类型，确保前端、后端和DB-Agent使用相同的错误定义。
"""
from enum import Enum, auto

class ErrorCategory(str, Enum):
    """错误类别枚举"""
    DATABASE = "database"           # 数据库通用错误
    QUERY = "query"                 # 数据库查询语法或执行错误
    COMPARISON = "comparison"       # 数据比对错误
    NETWORK = "network"             # 网络通用错误
    CONNECTION = "connection"       # 网络连接建立或维护错误
    TIMEOUT = "timeout"             # 操作超时错误
    AUTHENTICATION = "authentication" # 用户身份验证错误
    AUTHORIZATION = "authorization" # 用户权限验证错误
    PERMISSION = "permission"       # 资源访问权限错误
    VALIDATION = "validation"       # 数据格式或内容验证错误
    RESOURCE = "resource"           # 系统资源不足或不可用错误
    CONFIGURATION = "configuration" # 系统配置相关错误
    EXTERNAL = "external"           # 外部服务调用错误
    INTERNAL = "internal"           # 系统内部错误
    SYSTEM = "system"               # 操作系统或环境相关错误
    UNKNOWN = "unknown"             # 无法分类的错误

class ErrorSeverity(str, Enum):
    """错误严重程度枚举"""
    INFO = "info"           # 信息级别，不影响任务执行
    WARNING = "warning"     # 警告级别，可能影响部分功能
    ERROR = "error"         # 错误级别，影响任务正常执行
    CRITICAL = "critical"   # 严重错误，导致任务失败

class ErrorType(str, Enum):
    """错误类型枚举"""
    # 数据库错误
    DATABASE_CONNECTION_ERROR = "DatabaseConnectionError"  # 数据库连接错误
    DATABASE_QUERY_ERROR = "DatabaseQueryError"           # 数据库查询错误
    DATABASE_SCHEMA_ERROR = "DatabaseSchemaError"         # 数据库架构错误
    DATABASE_DATA_ERROR = "DatabaseDataError"             # 数据库数据错误
    
    # 比对错误
    COMPARISON_CONFIG_ERROR = "ComparisonConfigError"     # 比对配置错误
    COMPARISON_EXECUTION_ERROR = "ComparisonExecutionError" # 比对执行错误
    COMPARISON_RESULT_ERROR = "ComparisonResultError"     # 比对结果错误
    
    # 网络错误
    NETWORK_CONNECTION_ERROR = "NetworkConnectionError"   # 网络连接错误
    NETWORK_TIMEOUT_ERROR = "NetworkTimeoutError"         # 网络超时错误
    NETWORK_PROTOCOL_ERROR = "NetworkProtocolError"       # 网络协议错误
    
    # 认证和授权错误
    AUTHENTICATION_ERROR = "AuthenticationError"          # 认证错误
    AUTHORIZATION_ERROR = "AuthorizationError"            # 授权错误
    PERMISSION_ERROR = "PermissionError"                  # 权限错误
    
    # 验证错误
    VALIDATION_ERROR = "ValidationError"                  # 验证错误
    INPUT_ERROR = "InputError"                            # 输入错误
    FORMAT_ERROR = "FormatError"                          # 格式错误
    
    # 资源错误
    RESOURCE_NOT_FOUND_ERROR = "ResourceNotFoundError"    # 资源未找到错误
    RESOURCE_EXHAUSTED_ERROR = "ResourceExhaustedError"   # 资源耗尽错误
    RESOURCE_CONFLICT_ERROR = "ResourceConflictError"     # 资源冲突错误
    
    # 系统错误
    SYSTEM_ERROR = "SystemError"                          # 系统错误
    INTERNAL_ERROR = "InternalError"                      # 内部错误
    EXTERNAL_SERVICE_ERROR = "ExternalServiceError"       # 外部服务错误
    
    # 其他错误
    UNKNOWN_ERROR = "UnknownError"                        # 未知错误
    TASK_ERROR = "TaskError"                              # 任务错误
    CONFIGURATION_ERROR = "ConfigurationError"            # 配置错误

# 错误对象标准结构
ERROR_OBJECT_SCHEMA = {
    "timestamp": "",        # ISO格式的时间戳
    "type": "",             # 错误类型，如TypeError
    "message": "",          # 错误消息
    "category": "",         # 错误类别，如validation
    "severity": "",         # 错误严重程度，如error
    "is_fatal": False,      # 是否为致命错误
    "task_id": "",          # 相关任务ID
    "context": {},          # 错误上下文信息
    "traceback": ""         # 错误堆栈信息
}

def categorize_error(error):
    """
    根据错误类型确定错误类别
    
    Args:
        error: 错误对象
        
    Returns:
        ErrorCategory: 错误类别
    """
    error_type = type(error).__name__
    
    # 数据库错误
    if error_type in ['OperationalError', 'DatabaseError', 'IntegrityError', 'DataError']:
        return ErrorCategory.DATABASE
    
    # 网络错误
    if error_type in ['ConnectionError', 'ConnectionRefusedError', 'ConnectionResetError']:
        return ErrorCategory.CONNECTION
    
    # 超时错误
    if error_type in ['TimeoutError', 'ConnectTimeout', 'ReadTimeout']:
        return ErrorCategory.TIMEOUT
    
    # 验证错误
    if error_type in ['ValidationError', 'ValueError', 'TypeError']:
        return ErrorCategory.VALIDATION
    
    # 资源错误
    if error_type in ['FileNotFoundError', 'PermissionError', 'MemoryError']:
        return ErrorCategory.RESOURCE
    
    # 默认为未知错误
    return ErrorCategory.UNKNOWN

def determine_severity(error, category=None):
    """
    根据错误类型和类别确定错误严重程度
    
    Args:
        error: 错误对象
        category: 错误类别
        
    Returns:
        ErrorSeverity: 错误严重程度
    """
    if category is None:
        category = categorize_error(error)
    
    # 致命错误
    if category in [ErrorCategory.CONNECTION, ErrorCategory.SYSTEM, ErrorCategory.INTERNAL]:
        return ErrorSeverity.CRITICAL
    
    # 严重错误
    if category in [ErrorCategory.DATABASE, ErrorCategory.AUTHENTICATION, ErrorCategory.AUTHORIZATION]:
        return ErrorSeverity.ERROR
    
    # 警告
    if category in [ErrorCategory.VALIDATION, ErrorCategory.RESOURCE]:
        return ErrorSeverity.WARNING
    
    # 默认为错误级别
    return ErrorSeverity.ERROR

def is_fatal_error(error, category=None, severity=None):
    """
    判断错误是否为致命错误
    
    Args:
        error: 错误对象
        category: 错误类别
        severity: 错误严重程度
        
    Returns:
        bool: 是否为致命错误
    """
    if category is None:
        category = categorize_error(error)
    
    if severity is None:
        severity = determine_severity(error, category)
    
    # 严重程度为CRITICAL的错误视为致命错误
    if severity == ErrorSeverity.CRITICAL:
        return True
    
    # 特定类别的错误视为致命错误
    if category in [ErrorCategory.SYSTEM, ErrorCategory.INTERNAL, ErrorCategory.CONNECTION]:
        return True
    
    return False
