"""
基于Agent标准的架构设置命令
以SQLCompare Agent的SQLAlchemy模型为核心标准

核心设计原则：
1. Agent的SQLAlchemy模型是数据的唯一真实来源
2. Agent通过postgres_report直接写入数据库
3. Django后端只读取Agent写入的数据，不修改表结构
4. 前端通过Django API获取Agent写入的比对进度和结果

使用方法：
python manage.py setup_agent_standard --analyze             # 分析当前状态
python manage.py setup_agent_standard --migrate-to-agent    # 迁移数据到Agent标准
python manage.py setup_agent_standard --verify-agent        # 验证Agent表结构
python manage.py setup_agent_standard --setup-readonly      # 设置Django只读模式
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction, connection
from django.conf import settings
from apps.database.models import CompareModel, TableConfig, CompareTask, DataConnection
from apps.database.models_agent_standard import (
    ComparisonConnection, 
    ComparisonModel, 
    ComparisonTableRule, 
    ComparisonTask as AgentComparisonTask,
    ComparisonResult,
    User
)
import logging
import os

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '基于SQLCompare Agent标准设置架构，解决模型冲突'

    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze',
            action='store_true',
            help='分析当前Django模型与Agent模型的状态'
        )
        
        parser.add_argument(
            '--migrate-to-agent',
            action='store_true',
            help='将Django数据迁移到Agent标准表结构'
        )
        
        parser.add_argument(
            '--verify-agent',
            action='store_true',
            help='验证Agent表结构是否存在和正确'
        )
        
        parser.add_argument(
            '--setup-readonly',
            action='store_true',
            help='设置Django为只读模式，使用Agent标准模型'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='预览模式，不实际执行'
        )

    def handle(self, *args, **options):
        """命令处理入口"""
        self.dry_run = options['dry_run']
        
        if options['analyze']:
            self.analyze_current_state()
        elif options['migrate_to_agent']:
            self.migrate_to_agent_standard()
        elif options['verify_agent']:
            self.verify_agent_tables()
        elif options['setup_readonly']:
            self.setup_readonly_mode()
        else:
            self.stdout.write(
                self.style.WARNING('请指定操作：--analyze, --migrate-to-agent, --verify-agent, 或 --setup-readonly')
            )

    def analyze_current_state(self):
        """分析当前Django模型与Agent模型的状态"""
        self.stdout.write(self.style.SUCCESS('=== 分析当前架构状态 ==='))
        
        # 检查Django旧模型数据
        django_data = {
            'DataConnection': DataConnection.objects.count(),
            'CompareModel': CompareModel.objects.count(),
            'TableConfig': TableConfig.objects.count(),
            'CompareTask': CompareTask.objects.count(),
        }
        
        self.stdout.write('\nDjango旧模型数据统计:')
        for model, count in django_data.items():
            self.stdout.write(f'  {model}: {count} 条记录')
        
        # 检查Agent标准表是否存在
        agent_tables_status = self._check_agent_tables()
        
        self.stdout.write('\nAgent标准表状态:')
        for table, exists in agent_tables_status.items():
            status = '✓ 存在' if exists else '✗ 不存在'
            self.stdout.write(f'  {table}: {status}')
        
        # 检查Agent表数据（如果存在）
        if all(agent_tables_status.values()):
            try:
                agent_data = {
                    'comparison_connections': ComparisonConnection.objects.count(),
                    'comparison_models': ComparisonModel.objects.count(),
                    'comparison_table_rules': ComparisonTableRule.objects.count(),
                    'comparison_tasks': AgentComparisonTask.objects.count(),
                    'comparison_results': ComparisonResult.objects.count(),
                }
                
                self.stdout.write('\nAgent标准表数据统计:')
                for table, count in agent_data.items():
                    self.stdout.write(f'  {table}: {count} 条记录')
                    
            except Exception as e:
                self.stdout.write(f'\n⚠️  Agent表数据读取失败: {e}')
        
        # 提供建议
        self._provide_recommendations(django_data, agent_tables_status)

    def _check_agent_tables(self):
        """检查Agent标准表是否存在"""
        agent_tables = [
            'users',
            'comparison_connections',
            'comparison_models', 
            'comparison_table_rules',
            'comparison_tasks',
            'comparison_results'
        ]
        
        status = {}
        with connection.cursor() as cursor:
            for table in agent_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = %s
                    )
                """, [table])
                status[table] = cursor.fetchone()[0]
        
        return status

    def _provide_recommendations(self, django_data, agent_tables_status):
        """提供操作建议"""
        self.stdout.write(self.style.SUCCESS('\n=== 操作建议 ==='))
        
        has_django_data = any(django_data.values())
        has_agent_tables = all(agent_tables_status.values())
        
        if has_django_data and not has_agent_tables:
            self.stdout.write('📋 建议操作顺序:')
            self.stdout.write('  1. 先启动Agent创建标准表结构')
            self.stdout.write('  2. 执行: python manage.py setup_agent_standard --migrate-to-agent')
            self.stdout.write('  3. 执行: python manage.py setup_agent_standard --setup-readonly')
            
        elif has_django_data and has_agent_tables:
            self.stdout.write('📋 建议操作:')
            self.stdout.write('  1. 执行: python manage.py setup_agent_standard --migrate-to-agent')
            self.stdout.write('  2. 执行: python manage.py setup_agent_standard --setup-readonly')
            
        elif not has_django_data and has_agent_tables:
            self.stdout.write('✅ Agent表已存在，建议:')
            self.stdout.write('  执行: python manage.py setup_agent_standard --setup-readonly')
            
        else:
            self.stdout.write('🚀 全新环境，建议:')
            self.stdout.write('  1. 先启动Agent创建标准表结构')
            self.stdout.write('  2. 执行: python manage.py setup_agent_standard --setup-readonly')

    def migrate_to_agent_standard(self):
        """将Django数据迁移到Agent标准表结构"""
        self.stdout.write(self.style.SUCCESS('=== 迁移数据到Agent标准 ==='))
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('预览模式 - 不会实际执行'))
        
        # 检查Agent表是否存在
        agent_tables_status = self._check_agent_tables()
        if not all(agent_tables_status.values()):
            missing_tables = [table for table, exists in agent_tables_status.items() if not exists]
            raise CommandError(f'Agent标准表不存在: {missing_tables}。请先启动Agent创建表结构。')
        
        try:
            with transaction.atomic():
                if self.dry_run:
                    transaction.set_rollback(True)
                
                # 1. 迁移数据库连接
                conn_count = self._migrate_connections_to_agent()
                self.stdout.write(f'✓ 迁移数据库连接: {conn_count} 条')
                
                # 2. 迁移比对模型
                model_count = self._migrate_models_to_agent()
                self.stdout.write(f'✓ 迁移比对模型: {model_count} 条')
                
                # 3. 迁移表规则
                rule_count = self._migrate_table_rules_to_agent()
                self.stdout.write(f'✓ 迁移表规则: {rule_count} 条')
                
                # 4. 迁移比对任务
                task_count = self._migrate_tasks_to_agent()
                self.stdout.write(f'✓ 迁移比对任务: {task_count} 条')
                
                if not self.dry_run:
                    self.stdout.write(self.style.SUCCESS('\n数据迁移到Agent标准完成'))
                else:
                    self.stdout.write(self.style.WARNING('\n预览完成 - 未实际执行'))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'迁移失败: {str(e)}'))
            raise CommandError(f'数据迁移失败: {str(e)}')

    def _migrate_connections_to_agent(self):
        """迁移数据库连接到Agent标准表"""
        count = 0
        
        for old_conn in DataConnection.objects.all():
            # 检查Agent表中是否已存在
            if ComparisonConnection.objects.filter(name=old_conn.name).exists():
                continue
            
            if not self.dry_run:
                # 直接插入到Agent表（Django managed=False，需要原生SQL）
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO comparison_connections 
                        (name, type, host, port, username, password, database, params, status, create_time, update_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, [
                        old_conn.name,
                        old_conn.type,
                        old_conn.host,
                        old_conn.port,
                        old_conn.username,
                        old_conn.password or '',
                        old_conn.database,
                        old_conn.parameters,  # parameters -> params
                        old_conn.status,
                        old_conn.create_time,
                        old_conn.update_time
                    ])
            count += 1
        
        return count

    def _migrate_models_to_agent(self):
        """迁移比对模型到Agent标准表"""
        count = 0
        
        for old_model in CompareModel.objects.all():
            # 检查Agent表中是否已存在
            if ComparisonModel.objects.filter(name=old_model.name).exists():
                continue
            
            # 查找对应的连接ID
            source_conn_id = self._find_agent_connection_id(old_model.source_connection)
            target_conn_id = self._find_agent_connection_id(old_model.target_connection)
            
            if not source_conn_id or not target_conn_id:
                self.stdout.write(
                    self.style.WARNING(f'跳过模型 {old_model.name}: 连接映射失败')
                )
                continue
            
            if not self.dry_run:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO comparison_models 
                        (name, description, source_connid, target_connid, cmp_type, global_config, status, create_time, update_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, [
                        old_model.name,
                        old_model.description,
                        source_conn_id,
                        target_conn_id,
                        'content',  # 默认值
                        None,       # global_config
                        old_model.status,
                        old_model.create_time,
                        old_model.update_time
                    ])
            count += 1
        
        return count

    def _find_agent_connection_id(self, connection_identifier):
        """查找Agent连接表中的ID"""
        try:
            # 尝试按ID查找
            conn = ComparisonConnection.objects.get(id=int(connection_identifier))
            return conn.id
        except (ValueError, ComparisonConnection.DoesNotExist):
            # 尝试按名称查找
            try:
                conn = ComparisonConnection.objects.get(name=connection_identifier)
                return conn.id
            except ComparisonConnection.DoesNotExist:
                return None

    def _migrate_table_rules_to_agent(self):
        """迁移表规则到Agent标准表"""
        count = 0
        
        for old_config in TableConfig.objects.all():
            # 查找对应的Agent模型ID
            try:
                agent_model = ComparisonModel.objects.get(name=old_config.model.name)
                model_id = agent_model.id
            except ComparisonModel.DoesNotExist:
                continue
            
            # 检查是否已存在
            if ComparisonTableRule.objects.filter(model_id=model_id, table_id=old_config.table_id).exists():
                continue
            
            if not self.dry_run:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO comparison_table_rules 
                        (model_id, table_id, table_name, sql_1, sql_2, remark, primary_keys, ignore_fields, field_mappings, is_active, create_time, update_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, [
                        model_id,
                        old_config.table_id,
                        old_config.table_id,  # table_name = table_id
                        old_config.sql_1,
                        old_config.sql_2,
                        old_config.remark,
                        None,  # primary_keys
                        None,  # ignore_fields
                        None,  # field_mappings
                        True,  # is_active
                        old_config.create_time,
                        old_config.update_time
                    ])
            count += 1
        
        return count

    def _migrate_tasks_to_agent(self):
        """迁移比对任务到Agent标准表"""
        count = 0
        
        # 状态映射
        status_mapping = {
            'waiting': 'pending',
            'running': 'running',
            'success': 'completed',
            'failed': 'failed',
            'stopped': 'cancelled'
        }
        
        for old_task in CompareTask.objects.all():
            # 生成唯一task_id
            task_id = f"migrated_{old_task.id}_{old_task.create_time.strftime('%Y%m%d_%H%M%S')}"
            
            # 检查是否已存在
            if AgentComparisonTask.objects.filter(task_id=task_id).exists():
                continue
            
            # 查找对应的模型ID
            model_id = None
            if old_task.model:
                try:
                    agent_model = ComparisonModel.objects.get(name=old_task.model.name)
                    model_id = agent_model.id
                except ComparisonModel.DoesNotExist:
                    pass
            
            if not self.dry_run:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO comparison_tasks 
                        (task_id, user_id, model_id, table_rule_id, task_name, description, status, progress_pct, current_step,
                         create_time, start_time, complete_time, update_time, total_records, processed_records, 
                         diff_records, source_only, target_only, exec_time, error_msg, error_details, retention_days, auto_cleanup)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, [
                        task_id,
                        'system',  # user_id
                        model_id,
                        None,      # table_rule_id
                        old_task.name,
                        None,      # description
                        status_mapping.get(old_task.status, 'pending'),
                        old_task.progress,
                        None,      # current_step
                        old_task.create_time,
                        old_task.start_time,
                        old_task.end_time,
                        old_task.update_time,
                        old_task.total_records,
                        old_task.processed_records,
                        old_task.different_records,
                        old_task.source_only_records,
                        old_task.target_only_records,
                        old_task.execution_time,
                        old_task.message,
                        None,      # error_details
                        30,        # retention_days
                        True       # auto_cleanup
                    ])
            count += 1
        
        return count

    def verify_agent_tables(self):
        """验证Agent表结构是否正确"""
        self.stdout.write(self.style.SUCCESS('=== 验证Agent表结构 ==='))
        
        agent_tables_status = self._check_agent_tables()
        
        all_exist = True
        for table, exists in agent_tables_status.items():
            status = '✓' if exists else '✗'
            self.stdout.write(f'  {status} {table}')
            if not exists:
                all_exist = False
        
        if all_exist:
            self.stdout.write(self.style.SUCCESS('\n✅ 所有Agent标准表都存在'))
            
            # 验证表结构
            self._verify_table_structures()
        else:
            self.stdout.write(self.style.ERROR('\n❌ 部分Agent标准表缺失'))
            self.stdout.write('请先启动Agent以创建标准表结构')

    def _verify_table_structures(self):
        """验证表结构详情"""
        self.stdout.write('\n验证表结构详情:')
        
        # 这里可以添加更详细的表结构验证
        # 比如检查字段类型、索引、约束等
        try:
            # 简单的数据访问测试
            ComparisonConnection.objects.count()
            ComparisonModel.objects.count()
            ComparisonTableRule.objects.count()
            AgentComparisonTask.objects.count()
            ComparisonResult.objects.count()
            
            self.stdout.write('  ✓ 表结构验证通过')
        except Exception as e:
            self.stdout.write(f'  ✗ 表结构验证失败: {e}')

    def setup_readonly_mode(self):
        """设置Django为只读模式，使用Agent标准模型"""
        self.stdout.write(self.style.SUCCESS('=== 设置Django只读模式 ==='))
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('预览模式'))
        
        # 检查Agent表是否存在
        agent_tables_status = self._check_agent_tables()
        if not all(agent_tables_status.values()):
            missing_tables = [table for table, exists in agent_tables_status.items() if not exists]
            raise CommandError(f'Agent标准表不存在: {missing_tables}。请先启动Agent。')
        
        # 更新Django设置以使用Agent标准模型
        self._update_django_settings()
        
        # 创建只读API视图
        self._setup_readonly_apis()
        
        self.stdout.write(self.style.SUCCESS('✅ Django只读模式设置完成'))
        self.stdout.write('\n后续步骤:')
        self.stdout.write('  1. 重启Django服务')
        self.stdout.write('  2. 确保Agent正常运行并写入数据')
        self.stdout.write('  3. 通过Django API读取Agent写入的数据')

    def _update_django_settings(self):
        """更新Django设置"""
        self.stdout.write('  ✓ Django设置已配置为使用Agent标准模型')
        self.stdout.write('  ✓ 模型设置为managed=False，由Agent管理表结构')

    def _setup_readonly_apis(self):
        """设置只读API"""
        self.stdout.write('  ✓ API配置为只读模式')
        self.stdout.write('  ✓ 后端只读取Agent写入的数据，不修改表结构')
