<?xml version="1.0" encoding="utf-8"?>


<!--
  all_play = true  : 是否回放所有的功能号，如果为false则只回放下面包含的功能号
  get_second_rows  : 全局和单功能号均有效；获取第二结果集行数，最大99行，0不获取第二结果集
  
  is_play          : 单功能号有效，是否回放
  serial_flag      : 是否串行回放，true or false
  serial_field     : 串行字段，一般是自取取模，注意大小写敏感
  compare_flag     ：是否对比结果 true or false
  cmp_rule         : 比对规则，include比较包含字段，exclude忽略字段
-->
    
<rules all_play ="true" get_second_rows ="1" compare_flag1 ="true" compare_flag2 ="true" >

<lbm lbm_id="L0300034" remark ="刷新行情">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="include" ></ans2>
</lbm>


<lbm lbm_id="L0306259" remark ="客户交易业务权限维护(批量)">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0380730" remark ="查询委托">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="ORDER_DATE" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0380303" remark ="查询成交汇总">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="ORDER_DATE" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0304196" remark ="查询港股通额度信息">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0204026" remark ="查询银行余额异步处理">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0206021" remark ="三方存管解约">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0206105" remark ="三方存管开户">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0120903" remark ="资金与其他系统之间划拨">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0206204" remark ="三方存管资金余额查询">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>


<lbm lbm_id="L0204004" remark ="券查询银行余额">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0204006" remark ="银行发起销户">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="OCCUR_TIME" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0300656" remark ="PROP回报处理">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0360920" remark ="红利税差别化缴纳PROP申报">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0107024" remark ="资金利息归本">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0107001" remark ="资产帐户开户">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>


<lbm lbm_id="L0380500" remark ="查询保证金日结表">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0400232" remark ="查询全量基金信息">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0384063" remark ="查询债券交易参考信息">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0380404" remark ="资金帐单打印">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0380403" remark ="交易帐单查询">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0100000" remark ="查询系统信息">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0109101" remark ="用户注册认证">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0409101" remark ="外围用户登录，支持使用开放式基金帐号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0300120" remark ="数据转入结束处理">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0200060" remark ="单帐号多银行设置">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0200061" remark ="设置主资金帐号">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0200062" remark ="单帐号多银行记录查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0106021" remark ="客户销户">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>
<lbm lbm_id="L0305745" remark ="银证销户检查接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>


<lbm lbm_id="L0207010" remark ="设置三方存管账户">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0107008" remark ="资产帐户币种销户">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0301008" remark ="交易账户销户">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>


<lbm lbm_id="L0180231" remark ="资金流水查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" >
        <field name="OCCUR_DATE" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0380005" remark ="查询资金及股份">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0380301" remark ="操作员成交查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="5" compare_flag ="true" cmp_rule ="exclude" >
        <field name="ORDER_DATE" remark="" />
        <field name="SERIAL_NO" remark="" />
        <field name="MATCHED_TIME" remark="" />
    </ans2>
</lbm>



<lbm lbm_id="L0380462" remark ="当日成交汇总查询">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>



<lbm lbm_id="L0301005" remark ="维护指定交易信息">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0380201" remark ="操作员委托查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
		<field name="ORDER_DATE" remark="" />
		<field name="DCL_TIME" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0380301" remark ="操作员成交查询">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="false" cmp_rule ="exclude" > 

    </ans2>
</lbm>

<lbm lbm_id="L0380219" remark ="报盘委托重发查询">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 

    </ans2>
</lbm>


<lbm lbm_id="L0206543" remark ="外部接口查询股份">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0300087" remark ="查询分类收费设置">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="false" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0204016" remark ="查询保证金余额(银行发起）">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>


<lbm lbm_id="L0400203" remark ="查询帐号风险等级">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0206526" remark ="OTC业务资金冻结处理">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="SERIALNO" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0206527" remark ="OTC业务资金解冻处理">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
        <field name="SERIALNO" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0206529" remark ="OTC业务查询请求状态">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L0103010" remark ="查询操作员可访问资产账户类别">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > </ans2>
</lbm>

<lbm lbm_id="L2912004" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0206128" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0206106" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0206127" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0206131" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0306001" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="Lxxxxxxx" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="65060002" remark ="非集中交易功能号">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="false" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="false" cmp_rule ="" > </ans2>
</lbm>




<!-- 客户管理 -->
<lbm lbm_id="L0301007" remark ="股东挂失">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0107003" remark ="资产帐号挂失">
    <req is_play ="true" serial_flag ="true" serial_field ="ACCOUNT">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>


<lbm lbm_id="L0301201" remark ="设置主股东">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0301006" remark ="设置股东资产账号">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0106004" remark ="操作渠道开通取消">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0106313" remark ="设置用户职业资料">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0102061" remark ="客户风险评测结果设置">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0109440" remark ="预留信息设置">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0301014" remark ="重置成本">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0300458" remark ="增加协议签署流水记录">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0203609" remark ="查询协议签署流水记录">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>


<lbm lbm_id="L0102063" remark ="风险评测等级查询、评测过期查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0301041" remark ="客户交易业务权限维护">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0106013" remark ="权证开通取消">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>


<lbm lbm_id="L0300330" remark ="债券质押风控指标实际值查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>


<!--> 普通交易 <-->

<lbm lbm_id="L0300152" remark ="交易中风险超限提示">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0300080" remark ="取权证行权价格">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
    </ans2>
</lbm>

<lbm lbm_id="L0303020" remark ="计算最大可交易数量">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0303001" remark ="委托">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="USED_TIME" remark="" />
        <field name="BIZ_NO" remark="" />
        <field name="ORDER_ID" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0303002" remark ="撤单">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="ORDER_ID" remark="撤单合同序号" />
        <field name="STATUS" remark="0:委托成功, 1:内部撤单成功" />
    </ans2>
</lbm>

<lbm lbm_id="L0303102" remark ="回写发送标志">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="" > </ans2>
</lbm>
 
<lbm lbm_id="L0303103" remark ="回写合法标志">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0303101" remark ="成交回报">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0303104" remark ="STEP回写合法标志,港股通">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="" > </ans2>
</lbm>

<lbm lbm_id="L0303105" remark ="STEP成交处理">
    <req is_play ="false" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="0" compare_flag ="true" cmp_rule ="" > </ans2>
</lbm>


<lbm lbm_id="L0303010" remark ="指定交易">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" >
        <field name="BIZ_NO" remark="" />
        <field name="ORDER_ID" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0303087" remark ="非公开报价申报">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!--> 银证业务 <-->
<lbm lbm_id="L0204001" remark ="银证转账转入">
    <req is_play ="false" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0204002" remark ="银证转账转出">
    <req is_play ="false" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0204009" remark ="银证转账查询">
    <req is_play ="false" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0204020" remark ="银证转帐结果异步处理">
    <req is_play ="false" serial_flag ="false" serial_field ="SERIAL_NO">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0207013" remark ="由资管系统发起的客户资金划入或划出">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="SERIAL_NO" remark="" />
    </ans2>
</lbm>
<lbm lbm_id="L0206541" remark ="外部接口冻结解冻股份(如个股期权)">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="SERIALNO" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0207010" remark ="三方存管账户调整">
    <req is_play ="true" serial_flag ="true" serial_field ="ACCOUNT">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="SERIALNO" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0384068" remark ="查询现券交易逐笔委托成交信息">
    <req is_play ="false" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="SERIALNO" remark="" />
    </ans2>
</lbm>



<!--> 综合查询 <-->
<lbm lbm_id="L0102002" remark ="客户资料查询">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0107036" remark ="查询资金账户资料">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0106014" remark ="查询电子签名、客户理财代理协议">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0102062" remark ="客户风险评测结果查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0380485" remark ="查询设置客户代理业务协议">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301002" remark ="客户股东查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
      <field name="ID" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0107011" remark ="资金查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        
    </ans2>
</lbm>

<lbm lbm_id="L0107027" remark ="资金业务流水查询">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="OCCUR_DATE" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0301022" remark ="股份查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380205" remark ="委托查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" remark="" cmp_rule ="exclude" > 
	    <field name="ORDER_DATE" remark="" />
		<field name="ORDER_ID" remark="" />
		
    </ans2>
</lbm>

<lbm lbm_id="L0380305" remark ="成交查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
	<field name="ORDER_DATE" remark="" />
	<field name="MATCHED_TIME" remark="" />
	<field name="SERIAL_NO" remark="" />
	
    </ans2>
</lbm>


<lbm lbm_id="L0380002" remark ="股份汇总查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0106312" remark ="查询客户职业">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0106002" remark ="客户资料查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0106310" remark ="查询客户基本资料">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380001" remark ="股份信息列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0180201" remark ="资金信息列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380494" remark ="股份流水查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0206543" remark ="外部接口查询股份">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0305256" remark ="股份流水查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0304122" remark ="预发行客户持仓合约查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304123" remark ="预发行平仓明细查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380307" remark ="查询成交">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="MATCHED_TIME" remark="" />
        <field name="ORDER_DATE" remark="" />
    </ans2>
</lbm>


<lbm lbm_id="L0380011" remark ="回购合约查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0102126" remark ="客户标准券使用情况查询">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301077" remark ="已质押债券查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0305225" remark ="查询限售股持仓信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0102127" remark ="查询客户回购放大倍数">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0303029" remark ="查询深港通权益">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0107501" remark ="资产信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="USER_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300465" remark ="查询证券交易权限控制">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300467" remark ="查询客户证券交易协议">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0300466" remark ="增删改客户证券交易协议列表设置">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300560" remark ="算债券回购资金实际占款天数">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300474" remark ="查询小贷通客户级别产品外围">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300570" remark ="查询客户预估收益信息">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301108" remark ="股份减持信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0360411" remark ="跨系统查询股份">
    <req is_play ="true" serial_flag ="true" serial_field ="CUSTOMER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0360119" remark ="跨系统查询普通帐号股东席位">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301058" remark ="网络投票结果查询查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
		
    </ans2>
</lbm>

<lbm lbm_id="L0304042" remark ="约定购回未到期合约查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301042" remark ="查询客户交易权限限制">
    <req is_play ="true" serial_flag ="true" serial_field ="CUST_CODE">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" remark ="" cmp_rule ="exclude" > 
        <field name="RECORD_SN" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0300193" remark ="查询客户履约担保比例监控">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0301110" remark ="股份减持记录查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300302" remark ="新股申购预冻结资金查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="OCR_DATE" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0303749" remark ="预清算资金信息列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0303758" remark ="股份信息列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300360" remark ="债券质押现金担保品预欠库查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380760" remark ="询价交易转发数据查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0207012" remark ="资金可取金额查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384050" remark ="询价交易委托报送信息列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384038" remark ="成交查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="2" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="MATCHED_TIME" remark="" />
        <field name="ORDER_DATE" remark="" />
        <field name="RECORD_SN" remark="" />
		
    </ans2>
</lbm>

<lbm lbm_id="L0305646" remark ="RTGS交收上账信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

 
<lbm lbm_id="L0380488" remark ="帐户银行帐户查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384076" remark ="T日上海B转H成交查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0305643" remark ="RTGS勾单数据查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!-- 新股业务 -->
<lbm lbm_id="L0380480" remark ="新股配号查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304142" remark ="新股申购帐户市值查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300162" remark ="新股申购认购扣收信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300164" remark ="新股日历信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300202" remark ="客户新股申购扣款优先级设置">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!-- 股转业务 -->
<lbm lbm_id="L0304070" remark ="中小企业私募债意向行情查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300096" remark ="协议转让申报信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0380473" remark ="股份转让受限投资者信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300628" remark ="获取股转市场证券是否允许盘后大宗交易">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!--> 港股通 <-->

<lbm lbm_id="L0304179" remark ="查询港股通汇率信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304181" remark ="查询港股通价差信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304203" remark ="港股通组合费查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!--> 资金控制 <-->
<lbm lbm_id="L0107090" remark ="外部接口资金冻结解冻">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0107120" remark ="客户资金台帐间转帐">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0107121" remark ="客户台帐资金归集">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>



<lbm lbm_id="L0300301" remark ="新股申购资金预冻结及解冻">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<!--> 网络投票 <-->
<lbm lbm_id="L0301057" remark ="网络投票公告信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
		 
    </ans2>
</lbm>

<!--> 证券交易风险警示 <-->

<lbm lbm_id="L0380491" remark ="证券交易风险警示提示查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380492" remark ="证券交易风险警示提示信息维护">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380495" remark ="风险警示信息流水列表查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<!--> 报价回购/约定购回 <-->

<lbm lbm_id="L0304001" remark ="自动展期终止接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304020" remark ="大额提前中止预约维护">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304061" remark ="预约展期终止">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>
<lbm lbm_id="L0304019" remark ="预约提前中止查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304011" remark ="报价回购信息查询接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
	 <field name="REMARK" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0304013" remark ="报价回购品种额度设置查询">
    <req is_play ="true" serial_flag ="false" serial_field ="">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="3" compare_flag ="true" cmp_rule ="exclude" > 
	 <field name="REMARK" remark="" />
    </ans2>
</lbm>

<lbm lbm_id="L0304012" remark ="未到期报价回购查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304016" remark ="可展期终止信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304040" remark ="质押物明细查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304043" remark ="延期购回交易">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304045" remark ="约定购回品种信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304046" remark ="约定购回标的证券信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304056" remark ="约定购回购回合约查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0303666" remark ="约定购回购回交易">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0304050" remark ="约定购回客户履约比例查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300303" remark ="设置报价回购代理委托留存金额接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384017" remark ="特定报价回购产品客户权益设置">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0120104" remark ="报价回购自动到期购回流水查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0360922" remark ="报价回购一笔合约下的提前终止记录接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0360924" remark ="报价回购展期续存记录接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384014" remark ="查询报价回购代理客户信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
        <field name="MATCH_TIME" remark="" />
    </ans2>
</lbm>

<!--> 自主行权 <-->
<lbm lbm_id="L0302808" remark ="查询行权所得税">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0302809" remark ="查询期权证券信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0302807" remark ="所得税查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0302801" remark ="期权信息查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0302816" remark ="提交期权所得税">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0300190" remark ="查询客户融资行权合约">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300195" remark ="融资行权直接还款">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380753" remark ="融资行权合约展期申报">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0384051" remark ="融资行权卖券还款试算接口">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>


<lbm lbm_id="L0580004" remark ="融资行权担保品提交及提取试算">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0305760" remark ="融资行权授信额度申请">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380756" remark ="查询客户融资行权信息">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0305763" remark ="融资行权委托履约担保比例试算">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0300197" remark ="融资行权签署协议查询">
    <req is_play ="true" serial_flag ="true" serial_field ="F_OP_USER">  </req>
    <ans1 compare_flag ="true" >  </ans1>
    <ans2 get_second_rows ="1" compare_flag ="true" cmp_rule ="exclude" > 
    </ans2>
</lbm>

<lbm lbm_id="L0380751" remark="融资行权已提交担保品查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
</lbm>

<lbm lbm_id="L0300636" remark="客户股东级权限查询">
    <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
    <ans1 compare_flag="true"> </ans1>
    <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
</lbm>
<lbm lbm_id="L0380758" remark="融资行权担保证券信息查询">
    <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
    <ans1 compare_flag="true"> </ans1>
    <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
</lbm>
    
<!--> 股票质押 <-->

    <lbm lbm_id="L0305213" remark="股票质押提前购回、购回交易、终止购">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305227" remark="股票质押合约延期">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305229" remark="股票质押场外了结">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305231" remark="股票质押押履约比例查询(按交易日期购回日期)">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305219" remark="股票质押股份质押查询小贷通质押明细查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305230" remark="股票质押回购合约查询股票质押购回合约查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305240" remark="股票质押客户额度查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305232" remark="股票质押延期合约查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305234" remark="股票质押上海股份性质查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305238" remark="股票质押限售股明细查询 \t185">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305241" remark="股票质押上海红利查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305252" remark="股票质押还款情况查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305253" remark="股票质押还款批量查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300655" remark="小约定获取最小补充质押数量">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300590" remark="小约融资买入交易">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300591" remark="小约融资买入撤销">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0304666" remark="小约购回交易">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300592" remark="小约融资买入申请信息查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0304082" remark="小约资金归还">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300650" remark="小约融资买入历史申请信息查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0304087" remark="小约购回购回申请查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300651" remark="小约购回购回历史申请查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0304089" remark="小约定获取质押数据成功">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
   
<!--> 债券质押式协议回购 <-->   
   
    <lbm lbm_id="L0304230" remark="质押式协议回购质押查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0304231" remark="质押式协议回购合约查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> 
	
		</ans2>
    </lbm>
    <lbm lbm_id="L0304228" remark="质押式协议回购标的券查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300356" remark="质押协议回购账户名称查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0384027" remark="转发成交申报查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true">  </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
   
 <!--> 客户适当性业务 <-->   
  
    <lbm lbm_id="L0300417" remark="查询客户购买产品适当性匹配信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300416" remark="查询客户适当性匹配的产品信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499047" remark="查询投资者专项协议信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300415" remark="查询客户适当性匹配流水">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499064" remark="查询基金客户适当性匹配流水">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300411" remark="查询证券产品适当性信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499060" remark="查询基金产品适当性信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300424" remark="写消息揭示版本流水">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499052" remark="客户确认委托接口">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499055" remark="回访数据维护">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499041" remark="查询基金投资人风险承受能力匹配信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300003" remark="交易所信息">
        <req is_play="true" serial_flag="false" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> 
          <field name="TRD_END_TIME" remark="交易结束时间" />
        </ans2>
    </lbm>
    <lbm lbm_id="L0400270" remark="查询客户风险匹配的基金信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499058" remark="设置资格认定及电子合同记录">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0499063" remark="设置基金产品适当性信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300413" remark="设置产品适当性信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
   
 <!--> 三方回购 <-->
    
    <lbm lbm_id="L0300672" remark="三方回购篮子信息查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305685" remark="三方回购合约查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300679" remark="查询三方回购已质押入库信息">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300683" remark="三方回购质押明细查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0300684" remark="三方回购点击成交申报查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0203606" remark="查询客户预约转账设置">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0203600" remark="客户预约转账设置">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0207036" remark="查询预约转账营业部设置">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0203603" remark="当日预约转账流水查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0203608" remark="历史预约转账流水查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305705" remark="证券风险揭示书/承诺函版本配置查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305707" remark="客户风险揭示书签署维护">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305708" remark="客户风险揭示书签署信息查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305711" remark="客户ETF承诺函签署信息查询">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    <lbm lbm_id="L0305712" remark="客户ETF承诺函签署维护">
        <req is_play="true" serial_flag="true" serial_field ="F_OP_USER"> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
    </lbm>
    
    <lbm lbm_id="L0305728" remark="查询证券信息到23.dat的数据字典">
        <req is_play="false" serial_flag="false" serial_field =""> </req>
        <ans1 compare_flag="false"> </ans1>
        <ans2 get_second_rows="0" compare_flag="false" cmp_rule="exclude"> </ans2>
    </lbm>
	 <lbm lbm_id="L0107032" remark="查询申请取款记录">
        <req is_play="true" serial_flag="false" serial_field =""> </req>
        <ans1 compare_flag="true"> </ans1>
        <ans2 get_second_rows="2" compare_flag="true" cmp_rule="exclude"> 
		 <field name="OCCUR_DATE" remark="" />
		</ans2>
    </lbm>
	
    <lbm lbm_id="L0350312" remark="订单日终数据导出">
        <req is_play="false" serial_flag="false" serial_field =""> </req>
        <ans1 compare_flag="false"> </ans1>
        <ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
    </lbm>
	
    <lbm lbm_id="L0350320" remark="订单客户资产账户费用分类信息同步">
		<req is_play="false" serial_flag="true" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
	<lbm lbm_id="L0350321" remark="订单客户分支机构费用信息同步">
		<req is_play="false" serial_flag="true" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
    <lbm lbm_id="L0350322" remark="订单客户分支机构费用信息同步">
		<req is_play="false" serial_flag="true" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
    <lbm lbm_id="L0350323" remark="订单客户分支机构费用信息同步">
		<req is_play="false" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
    <lbm lbm_id="L0406013" remark="导出现金宝可用资金数据">
		<req is_play="false" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
     <lbm lbm_id="L0300019" remark="查询债券信息">
		<req is_play="false" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>

    <lbm lbm_id="L0300083" remark="证券分类收费种类维护">
		<req is_play="false" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
    <lbm lbm_id="L0300084" remark="证券分类收费设置维护">
		<req is_play="false" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>
    
    <lbm lbm_id="L0300085" remark="设置资产账户收费种类">
		<req is_play="false" serial_flag="false" serial_field =""> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>

     <lbm lbm_id="L0109003" remark="查询报表参数设置">
		<req is_play="false" serial_flag="false" serial_field =""> </req>
		<ans1 compare_flag="false"> </ans1>
		<ans2 get_second_rows="1" compare_flag="false" cmp_rule="exclude"> </ans2>
	</lbm>


	<lbm lbm_id="L0301109" remark="股份减持信息设置">
		<req is_play="true" serial_flag="false" serial_field ="F_OP_USER"> </req>
		<ans1 compare_flag="true"> </ans1>
		<ans2 get_second_rows="1" compare_flag="true" cmp_rule="exclude"> </ans2>
	</lbm>

</rules>



