#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_v4.py 报告器配置使用示例
演示如何使用不同类型的报告器配置
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main_v4 import ComparisonTaskOrchestrator
from models.pydantic_models import TaskCreateDirect, TableRuleCreate

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_sqlite_reporter():
    """SQLite报告器使用示例"""
    print("=" * 60)
    print("SQLite报告器使用示例")
    print("=" * 60)
    
    try:
        # 方式1: 使用类方法创建
        orchestrator = ComparisonTaskOrchestrator.create_with_sqlite_reporter(
            sqlite_db_path='example_sqlite_results.db'
        )
        
        # 方式2: 使用配置字典创建
        reporter_config = {
            'type': 'sqlite',
            'db_path': 'example_sqlite_results.db',
            'batch_size': 10000,
            'high_performance_mode': True,
            'silent_mode': False
        }
        orchestrator2 = ComparisonTaskOrchestrator(reporter_config=reporter_config)
        
        print(f"✓ SQLite报告器创建成功")
        print(f"  报告器类型: {orchestrator.reporter_config['type']}")
        print(f"  数据库路径: {orchestrator.reporter_config['db_path']}")
        
        # 创建示例任务
        task_data = create_sample_task_data()
        task_id = orchestrator.sqlalchemy_service.create_task_direct("test_user", task_data)
        
        print(f"✓ 示例任务创建成功: {task_id}")
        
        # 注意：实际执行需要真实的数据库连接
        # results = orchestrator.execute_task(task_id, cmp_type=2)
        # print(f"✓ 任务执行完成: {results}")
        
    except Exception as e:
        logger.error(f"SQLite报告器示例失败: {e}")


def example_postgresql_reporter():
    """PostgreSQL报告器使用示例"""
    print("\n" + "=" * 60)
    print("PostgreSQL报告器使用示例")
    print("=" * 60)
    
    try:
        # 方式1: 使用类方法创建
        orchestrator = ComparisonTaskOrchestrator.create_with_postgresql_reporter(
            pg_host='localhost',
            pg_port=5432,
            pg_database='sqlcompare',
            pg_username='postgres',
            pg_password='your_password'
        )
        
        # 方式2: 使用配置字典创建
        reporter_config = {
            'type': 'postgresql',
            'host': 'localhost',
            'port': 5432,
            'database': 'sqlcompare',
            'username': 'postgres',
            'password': 'your_password',
            'schema': 'public',
            'batch_size': 50000,
            'use_copy': True,
            'high_performance_mode': True,
            'silent_mode': False
        }
        orchestrator2 = ComparisonTaskOrchestrator(reporter_config=reporter_config)
        
        print(f"✓ PostgreSQL报告器创建成功")
        print(f"  报告器类型: {orchestrator.reporter_config['type']}")
        print(f"  数据库主机: {orchestrator.reporter_config['host']}:{orchestrator.reporter_config['port']}")
        print(f"  数据库名称: {orchestrator.reporter_config['database']}")
        print(f"  使用COPY命令: {orchestrator.reporter_config['use_copy']}")
        
    except Exception as e:
        logger.error(f"PostgreSQL报告器示例失败: {e}")


def example_csv_reporter():
    """CSV报告器使用示例"""
    print("\n" + "=" * 60)
    print("CSV报告器使用示例")
    print("=" * 60)
    
    try:
        # 方式1: 使用类方法创建
        orchestrator = ComparisonTaskOrchestrator.create_with_csv_reporter(
            output_dir='./csv_exports/'
        )
        
        # 方式2: 使用配置字典创建
        reporter_config = {
            'type': 'csv',
            'output_dir': './csv_exports/',
            'filename': 'comparison_results.csv'
        }
        orchestrator2 = ComparisonTaskOrchestrator(reporter_config=reporter_config)
        
        print(f"✓ CSV报告器创建成功")
        print(f"  报告器类型: {orchestrator.reporter_config['type']}")
        print(f"  输出目录: {orchestrator.reporter_config['output_dir']}")
        print(f"  文件名: {orchestrator.reporter_config['filename']}")
        
        # 确保输出目录存在
        os.makedirs(orchestrator.reporter_config['output_dir'], exist_ok=True)
        print(f"✓ 输出目录已创建: {orchestrator.reporter_config['output_dir']}")
        
    except Exception as e:
        logger.error(f"CSV报告器示例失败: {e}")


def example_multiple_reporters():
    """多报告器使用示例"""
    print("\n" + "=" * 60)
    print("多报告器使用示例")
    print("=" * 60)
    
    try:
        # 方式1: 使用类方法创建（默认SQLite + CSV）
        orchestrator = ComparisonTaskOrchestrator.create_with_multiple_reporters()
        
        # 方式2: 自定义多报告器配置
        reporters_config = [
            {
                'type': 'sqlite',
                'db_path': 'multi_sqlite_results.db',
                'batch_size': 10000
            },
            {
                'type': 'csv',
                'output_dir': './multi_exports/',
                'filename': 'multi_comparison_results.csv'
            },
            {
                'type': 'postgresql',
                'host': 'localhost',
                'port': 5432,
                'database': 'sqlcompare',
                'username': 'postgres',
                'password': 'your_password',
                'use_copy': True
            }
        ]
        
        reporter_config = {
            'type': 'multiple',
            'reporters': reporters_config
        }
        orchestrator2 = ComparisonTaskOrchestrator(reporter_config=reporter_config)
        
        print(f"✓ 多报告器创建成功")
        print(f"  报告器类型: {orchestrator2.reporter_config['type']}")
        print(f"  子报告器数量: {len(orchestrator2.reporter_config['reporters'])}")
        
        for i, sub_config in enumerate(orchestrator2.reporter_config['reporters']):
            print(f"    报告器 {i+1}: {sub_config['type']}")
        
    except Exception as e:
        logger.error(f"多报告器示例失败: {e}")


def example_environment_based_config():
    """基于环境变量的配置示例"""
    print("\n" + "=" * 60)
    print("基于环境变量的配置示例")
    print("=" * 60)
    
    try:
        # 根据环境变量选择报告器类型
        reporter_type = os.getenv('SQLCOMPARE_REPORTER_TYPE', 'sqlite').lower()
        
        if reporter_type == 'postgresql':
            reporter_config = {
                'type': 'postgresql',
                'host': os.getenv('POSTGRES_HOST', 'localhost'),
                'port': int(os.getenv('POSTGRES_PORT', 5432)),
                'database': os.getenv('POSTGRES_DB', 'sqlcompare'),
                'username': os.getenv('POSTGRES_USER', 'postgres'),
                'password': os.getenv('POSTGRES_PASSWORD', 'postgres'),
                'use_copy': True,
                'high_performance_mode': True
            }
        elif reporter_type == 'csv':
            reporter_config = {
                'type': 'csv',
                'output_dir': os.getenv('CSV_OUTPUT_DIR', './exports/'),
                'filename': os.getenv('CSV_FILENAME', 'comparison_results.csv')
            }
        else:
            # 默认使用SQLite
            reporter_config = {
                'type': 'sqlite',
                'db_path': os.getenv('SQLITE_DB_PATH', 'comparison_results.db'),
                'high_performance_mode': True
            }
        
        orchestrator = ComparisonTaskOrchestrator(reporter_config=reporter_config)
        
        print(f"✓ 环境变量配置创建成功")
        print(f"  报告器类型: {reporter_config['type']}")
        print(f"  配置详情: {reporter_config}")
        
        # 显示相关环境变量
        print(f"\n相关环境变量:")
        print(f"  SQLCOMPARE_REPORTER_TYPE: {os.getenv('SQLCOMPARE_REPORTER_TYPE', '未设置')}")
        if reporter_type == 'postgresql':
            print(f"  POSTGRES_HOST: {os.getenv('POSTGRES_HOST', '未设置')}")
            print(f"  POSTGRES_DB: {os.getenv('POSTGRES_DB', '未设置')}")
        elif reporter_type == 'csv':
            print(f"  CSV_OUTPUT_DIR: {os.getenv('CSV_OUTPUT_DIR', '未设置')}")
        
    except Exception as e:
        logger.error(f"环境变量配置示例失败: {e}")


def create_sample_task_data():
    """创建示例任务数据"""
    return TaskCreateDirect(
        task_name="报告器示例任务",
        description="用于演示不同报告器配置的示例任务",
        comparison_type="content",
        source_connection={
            "type": "db2",
            "host": "localhost",
            "port": 50000,
            "username": "test_user",
            "password": "test_password",
            "dbname": "test_db"
        },
        target_connection={
            "type": "db2",
            "host": "localhost",
            "port": 50000,
            "username": "test_user",
            "password": "test_password",
            "dbname": "test_db"
        },
        sql_rules=[
            TableRuleCreate(
                table_id="demo_table",
                table_name="演示表",
                sql_1="SELECT id AS KEY, name, age FROM demo_table WHERE status = 'ACTIVE'",
                sql_2="SELECT id AS KEY, name, age FROM demo_table WHERE status = 'ACTIVE'"
            )
        ]
    )


def main():
    """主函数"""
    print("main_v4.py 报告器配置使用示例")
    print("=" * 80)
    
    # 运行各种示例
    example_sqlite_reporter()
    example_postgresql_reporter()
    example_csv_reporter()
    example_multiple_reporters()
    example_environment_based_config()
    
    print("\n" + "=" * 80)
    print("所有示例完成！")
    print("\n使用说明:")
    print("1. 根据需要选择合适的报告器类型")
    print("2. SQLite适合小到中等规模的数据比对")
    print("3. PostgreSQL适合大规模、高并发的企业级应用")
    print("4. CSV适合需要导出和分析的场景")
    print("5. 多报告器适合需要同时输出到多个目标的场景")
    print("6. 可以通过环境变量灵活配置不同环境的报告器")


if __name__ == "__main__":
    main()
