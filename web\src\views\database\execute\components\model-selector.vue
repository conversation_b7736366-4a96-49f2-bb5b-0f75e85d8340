<template>
  <div class="model-selector-container">
    <div class="search-filter">
      <el-input
        v-model="searchQuery"
        placeholder="搜索比对模型"
        clearable
        prefix-icon="el-icon-search"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-radio-group v-model="viewMode" size="small">
        <el-radio-button value="card">卡片视图</el-radio-button>
        <el-radio-button value="list">列表视图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 卡片式视图 -->
    <div v-if="viewMode === 'card'" class="model-card-grid">
      <el-empty v-if="filteredModels.length === 0" description="未找到匹配的比对模型" />
      <el-card
        v-for="model in filteredModels"
        :key="model.id"
        shadow="hover"
        class="model-card"
        :class="{ 'selected': selectedModelId === model.id }"
        @click="selectModel(model)"
      >
        <template #header>
          <div class="card-header">
            <span class="model-name">{{ model.name }}</span>
            <el-tag
              size="small"
              v-if="getDbTypeColor(model.source_type)"
              :type="getDbTypeColor(model.source_type)"
            >{{ getDbTypeName(model.source_type) || '未知' }}</el-tag>
            <el-tag
              size="small"
              v-else
            >{{ model.source_type || '未知' }}</el-tag>
          </div>
        </template>
        <div class="card-content">
          <div class="model-description">{{ truncateText(model.description || '无描述', 80) }}</div>
          <div class="model-meta">
            <div class="meta-item">
              <el-icon><DataAnalysis /></el-icon>
              <span>表数量: {{ model.tables?.length || 0 }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>更新: {{ formatDate(model.updated_at) }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 列表式视图 -->
    <div v-else class="model-list">
      <el-empty v-if="filteredModels.length === 0" description="未找到匹配的比对模型" />
      <el-table
        v-else
        :data="filteredModels"
        style="width: 100%"
        @row-click="selectModel"
        highlight-current-row
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="name" label="模型名称" min-width="180">
          <template #default="{ row }">
            <div class="model-name-cell">
              <span>{{ row.name }}</span>
              <el-tag
                size="small"
                v-if="getDbTypeColor(row.source_type)"
                :type="getDbTypeColor(row.source_type)"
              >{{ row.source_type || '未知' }}</el-tag>
              <el-tag
                size="small"
                v-else
              >{{ row.source_type || '未知' }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="220">
          <template #default="{ row }">
            {{ truncateText(row.description || '无描述', 50) }}
          </template>
        </el-table-column>
        <el-table-column prop="tables" label="表数量" width="100">
          <template #default="{ row }">
            {{ row.tables?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column width="80">
          <template #default="{ row }">
            <el-button size="small" type="primary" text @click.stop="previewModel(row)">
              预览
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="model-pagination">
        <el-pagination
          v-if="filteredModels.length > pageSize"
          v-model:current-page="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="filteredModels.length"
          background
          hide-on-single-page
        />
      </div>
    </div>

    <!-- 模型预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="模型预览"
      width="70%"
      destroy-on-close
    >
      <model-preview v-if="previewModelData" :model="previewModelData" />
    </el-dialog>

    <!-- 新建模型按钮 -->
    <div class="create-model">
      <el-button type="primary" @click="createNewModel">
        <el-icon><Plus /></el-icon>
        新建比对模型
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Search, DataAnalysis, Calendar, Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ModelPreview from './model-preview.vue';
import { getCompareModels } from '@/api/database/index';
import { getConnectionList } from '@/api/database/connection';
import { getDbTypeColor, getDbTypeName } from '@/utils/db-utils';

const emit = defineEmits(['select-model', 'create-model']);
const props = defineProps({
  initialModelId: {
    type: String,
    default: ''
  }
});

// 数据状态
const models = ref([]);
const connections = ref([]);
const connectionMap = ref(new Map());
const loading = ref(false);
const connectionsLoading = ref(false);
const searchQuery = ref('');
const viewMode = ref('card');
const selectedModelId = ref('');
const previewDialogVisible = ref(false);
const previewModelData = ref(null);
const currentPage = ref(1);
const pageSize = ref(8);

// 加载数据库连接列表
const loadConnections = async () => {
  connectionsLoading.value = true;
  try {
    const response = await getConnectionList();

    // 处理不同响应格式
    if (response.success && response.data) {
      connections.value = response.data;

      // 建立连接ID到连接对象的映射
      connectionMap.value.clear();
      connections.value.forEach(conn => {
        connectionMap.value.set(String(conn.id), conn);
      });

    } else if (Array.isArray(response)) {
      connections.value = response;

      // 建立连接ID到连接对象的映射
      connectionMap.value.clear();
      connections.value.forEach(conn => {
        connectionMap.value.set(String(conn.id), conn);
      });

    } else if (response && response.data) {
      connections.value = response.data;

      // 建立连接ID到连接对象的映射
      connectionMap.value.clear();
      connections.value.forEach(conn => {
        connectionMap.value.set(String(conn.id), conn);
      });

    } else {
      console.warn('加载数据库连接失败:', response);
      ElMessage.warning('数据库连接信息加载失败，可能影响模型信息显示');
    }
  } catch (error) {
    console.error('加载数据库连接错误:', error);
    ElMessage.error('数据库连接信息加载失败: ' + (error.message || '未知错误'));
  } finally {
    connectionsLoading.value = false;
  }
};

// 根据连接ID获取数据库类型
const getConnectionType = (connectionId) => {
  if (!connectionId) return '未知';

  // 从映射中获取连接对象
  const conn = connectionMap.value.get(String(connectionId));
  if (conn && conn.type) {
    return conn.type.toUpperCase();
  }

  return '未知';
};

// 加载模型列表
const loadModels = async () => {
  loading.value = true;
  try {
    // 首先尝试加载数据库连接信息
    if (connections.value.length === 0) {
      await loadConnections();
    }

    const response = await getCompareModels();

    // 修复：统一使用success字段判断，与其他页面保持一致
    if (response.success && response.data) {
      // 标准BaseApiResponse格式
      models.value = response.data;
    } else if (Array.isArray(response)) {
      // 直接返回数组的情况
      models.value = response;
    } else if (typeof response === 'object' && response !== null) {
      // 尝试找到可能的数据字段
      const possibleData = response.data || response.result || response.models || response.items;
      if (possibleData && (Array.isArray(possibleData) || typeof possibleData === 'object')) {
        models.value = Array.isArray(possibleData) ? possibleData : [possibleData];
        console.log('从可能的字段找到模型数据，数量:', models.value.length);
      } else {
        console.warn('未找到有效的模型数据结构:', response);
        models.value = [];
      }
    } else {
      console.error('获取比对模型失败，响应格式不符合预期:', response);
      ElMessage.error('获取比对模型失败，请检查API格式');
      models.value = [];
    }

    // 检查是否成功加载了模型数据
    if (models.value.length === 0) {
      console.warn('未加载到任何模型数据');
    } else {
      // 为每个模型添加source_type字段(如果未设置)，方便显示数据库类型
      models.value.forEach(model => {
        if (!model.source_type && model.source_connection) {
          // 使用连接ID获取数据库类型
          model.source_type = getConnectionType(model.source_connection);
        }
      });

      // 添加连接信息到模型中，以便后续步骤使用
      models.value.forEach(model => {
        if (model.source_connection) {
          model.source_db = connectionMap.value.get(String(model.source_connection));
        }
        if (model.target_connection) {
          model.target_db = connectionMap.value.get(String(model.target_connection));
        }
      });
    }

    return models.value;
  } catch (error) {
    console.error('加载比对模型出错', error);
    ElMessage.error('加载比对模型失败: ' + (error.message || '未知错误'));
    models.value = [];
    return [];
  } finally {
    loading.value = false;
  }
};

// 过滤后的模型列表
const filteredModels = computed(() => {
  if (!searchQuery.value) return models.value;

  const query = searchQuery.value.toLowerCase();
  return models.value.filter(model =>
    model.name.toLowerCase().includes(query) ||
    (model.description && model.description.toLowerCase().includes(query))
  );
});

// 分页后的模型列表
const paginatedModels = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredModels.value.slice(start, end);
});

// 选择模型
const selectModel = (model) => {
  // 确保模型包含连接信息
  if (model.source_connection && !model.source_db) {
    model.source_db = connectionMap.value.get(String(model.source_connection));
  }
  if (model.target_connection && !model.target_db) {
    model.target_db = connectionMap.value.get(String(model.target_connection));
  }

  selectedModelId.value = model.id;
  emit('select-model', model);
};

// 预览模型
const previewModel = async (model) => {
  // 确保模型包含连接信息
  if (model.source_connection && !model.source_db) {
    model.source_db = connectionMap.value.get(String(model.source_connection));
  }
  if (model.target_connection && !model.target_db) {
    model.target_db = connectionMap.value.get(String(model.target_connection));
  }

  previewModelData.value = model;
  previewDialogVisible.value = true;
};

// 创建新模型
const createNewModel = () => {
  emit('create-model');
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

// 表格行类名
const tableRowClassName = ({ row }) => {
  return selectedModelId.value === row.id ? 'selected-row' : '';
};

// 监听搜索条件变化，重置分页
watch(searchQuery, () => {
  currentPage.value = 1;
});

// 监听初始模型ID
watch(() => props.initialModelId, (newVal) => {
  if (newVal) {
    selectedModelId.value = newVal;
  }
}, { immediate: true });

// 重置选择
const resetSelection = () => {
  selectedModelId.value = '';
  // 重新加载模型列表
  loadModels();
};

// 公开方法
defineExpose({
  resetSelection,
  loadModels
});

// 组件挂载时加载数据
onMounted(loadModels);
</script>

<style lang="scss" scoped>
.model-selector-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .el-input {
      width: 300px;
      margin-right: 15px;
    }
  }

  .model-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    min-height: 300px;
    margin-bottom: 20px;
    overflow-y: auto;
    flex: 1;
    padding: 10px;

    .el-empty {
      grid-column: 1 / -1;
      align-self: center;
      margin: 0;
    }
    .model-card {
      cursor: pointer;
      transition: all 0.3s;
      height: 100%;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border: 2px solid var(--el-color-primary);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px; // 统一标题栏高度

        .model-name {
          font-weight: bold;
          font-size: 15px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .card-content {
        display: flex;
        flex-direction: column;

        .model-description {
          color: #606266;
          margin-bottom: 15px;
          height: 60px;
          overflow: hidden;
        }

        .model-meta {
          margin-top: auto;
          display: flex;
          justify-content: space-between;

          .meta-item {
            display: flex;
            align-items: center;
            color: #909399;
            font-size: 13px;

            .el-icon {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }

  .model-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .el-table {
      flex: 1;
      width: 100%;

      :deep(.selected-row) {
        background-color: var(--el-color-primary-light-9);
      }

      .model-name-cell {
        display: flex;
        align-items: center;

        span {
          margin-right: 10px;
          font-weight: 500;
        }
      }
    }

    .empty-list {
      text-align: center;
      padding: 40px 0;
      color: #909399;
    }
  }

  .model-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .create-model {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>