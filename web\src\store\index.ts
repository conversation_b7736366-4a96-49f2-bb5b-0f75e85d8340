import type { App } from 'vue';
import { useUserStore } from './modules/user';
import { useConnectionStore } from './modules/connection';
import { store, setStore } from './utils';
import { createPinia } from 'pinia';

export function setupStore(app: App<Element>) {
  const pinia = createPinia();
  app.use(pinia);
  setStore(pinia); // 设置全局引用
  return pinia;
}
// 供组件中使用的store
export { useUserStore, useConnectionStore, store };