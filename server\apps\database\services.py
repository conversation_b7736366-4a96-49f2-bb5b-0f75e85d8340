import logging
from typing import Dict, Any, Optional, List
from django.utils import timezone
from django.db import transaction
from .models import CompareTask, CompareDiff
from utils.db_utils import get_db_agent_client

logger = logging.getLogger(__name__)


class DiffPersistenceService:
    """差异记录持久化服务

    负责将Agent返回的差异结果持久化到数据库，确保数据的长期可用性
    """

    @staticmethod
    def persist_task_diffs(task: CompareTask, force_refresh: bool = False) -> Dict[str, Any]:
        """持久化任务的差异记录

        Args:
            task: 比对任务实例
            force_refresh: 是否强制刷新已存在的记录

        Returns:
            Dict包含操作结果和统计信息
        """
        result = {
            'success': False,
            'message': '',
            'stats': {
                'total_diffs': 0,
                'persisted_count': 0,
                'skipped_count': 0,
                'error_count': 0
            },
            'source': 'unknown'
        }

        try:
            # 检查是否已有持久化的差异记录
            existing_diffs = CompareDiff.objects.filter(task=task)
            if existing_diffs.exists() and not force_refresh:
                result.update({
                    'success': True,
                    'message': '差异记录已存在，跳过持久化',
                    'source': 'database',
                    'stats': {
                        'total_diffs': existing_diffs.count(),
                        'persisted_count': 0,
                        'skipped_count': existing_diffs.count(),
                        'error_count': 0
                    }
                })
                return result

            # 如果强制刷新，先删除现有记录
            if force_refresh and existing_diffs.exists():
                deleted_count = existing_diffs.count()
                existing_diffs.delete()
                logger.info(f"强制刷新：删除任务{task.id}的{deleted_count}条现有差异记录")

            # 获取Agent结果
            agent_data = DiffPersistenceService._get_agent_diff_data(task)
            if not agent_data['success']:
                # 尝试从任务结果字段获取
                task_data = DiffPersistenceService._get_task_result_data(task)
                if task_data['success']:
                    agent_data = task_data
                else:
                    result['message'] = f"无法获取差异数据: Agent={agent_data['message']}, Task={task_data['message']}"
                    return result

            # 持久化差异记录
            diff_items = agent_data.get('items', [])
            result['stats']['total_diffs'] = len(diff_items)
            result['source'] = agent_data.get('source', 'unknown')

            if not diff_items:
                result.update({
                    'success': True,
                    'message': '任务无差异记录需要持久化'
                })
                return result

            # 批量创建差异记录
            with transaction.atomic():
                persisted_count = DiffPersistenceService._create_diff_records(task, diff_items)
                result['stats']['persisted_count'] = persisted_count

                # 更新任务的差异统计
                task.different_records = persisted_count
                task.save(update_fields=['different_records'])

            result.update({
                'success': True,
                'message': f'成功持久化{persisted_count}条差异记录'
            })

            logger.info(f"任务{task.id}差异记录持久化完成: {persisted_count}条记录")

        except Exception as e:
            logger.exception(f"持久化任务{task.id}差异记录时发生异常: {str(e)}")
            result.update({
                'success': False,
                'message': f'持久化失败: {str(e)}',
                'stats': {'error_count': 1}
            })

        return result

    @staticmethod
    def _get_agent_diff_data(task: CompareTask) -> Dict[str, Any]:
        """从Agent获取差异数据"""
        try:
            if not task.external_id:
                return {'success': False, 'message': '任务无外部ID'}

            client = get_db_agent_client()
            agent_result = client.get_task_result(task.external_id)

            if not agent_result.get("success", False):
                return {
                    'success': False,
                    'message': f"Agent返回错误: {agent_result.get('message', '未知错误')}"
                }

            data = agent_result.get("data", {})
            details = data.get('details', {})
            items = details.get('items', [])

            return {
                'success': True,
                'items': items,
                'source': 'agent',
                'message': f'从Agent获取到{len(items)}条差异记录'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Agent连接异常: {str(e)}'
            }

    @staticmethod
    def _get_task_result_data(task: CompareTask) -> Dict[str, Any]:
        """从任务结果字段获取差异数据"""
        try:
            if not task.result:
                return {'success': False, 'message': '任务结果为空'}

            details = task.result.get('details', {})
            items = details.get('items', [])

            return {
                'success': True,
                'items': items,
                'source': 'task_result',
                'message': f'从任务结果获取到{len(items)}条差异记录'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'解析任务结果异常: {str(e)}'
            }

    @staticmethod
    def _create_diff_records(task: CompareTask, diff_items: List[Dict]) -> int:
        """批量创建差异记录"""
        diff_records = []

        for i, item in enumerate(diff_items):
            try:
                # 解析差异项数据
                key_info = item.get('key', {})
                primary_key = key_info.get('primary_key', '')
                diff_type = item.get('diff_type', 'unknown')
                field_diffs = item.get('field_diffs')
                source_record = item.get('source_record')
                target_record = item.get('target_record')

                # 构造差异记录
                diff_record = CompareDiff(
                    task=task,
                    table_name=item.get('table_name', ''),
                    field_name='',  # Agent格式中通常没有单独的字段名
                    record_id=str(primary_key),
                    diff_type=diff_type,
                    severity='medium',  # 默认严重程度
                    source_value=str(source_record) if source_record is not None else '',
                    target_value=str(target_record) if target_record is not None else '',
                    context={
                        'key': key_info,
                        'field_diffs': field_diffs,
                        'source_record': source_record,
                        'target_record': target_record,
                        'agent_format': True,
                        'persisted_at': timezone.now().isoformat()
                    },
                    create_time=timezone.now()
                )
                diff_records.append(diff_record)

            except Exception as e:
                logger.error(f"解析差异项{i}时出错: {str(e)}, 数据: {item}")
                continue

        # 批量创建
        if diff_records:
            CompareDiff.objects.bulk_create(diff_records, batch_size=1000)
            logger.info(f"批量创建了{len(diff_records)}条差异记录")

        return len(diff_records)

    @staticmethod
    def auto_persist_on_completion(task: CompareTask) -> Dict[str, Any]:
        """任务完成时自动持久化差异记录

        这个方法应该在任务状态更新为success时调用
        """
        if task.status != 'success':
            return {
                'success': False,
                'message': f'任务状态为{task.status}，不执行自动持久化'
            }

        logger.info(f"开始自动持久化任务{task.id}的差异记录")
        result = DiffPersistenceService.persist_task_diffs(task, force_refresh=False)

        if result['success']:
            logger.info(f"任务{task.id}自动持久化完成: {result['message']}")
        else:
            logger.error(f"任务{task.id}自动持久化失败: {result['message']}")

        return result


class DiffRetrievalService:
    """差异记录检索服务

    提供统一的差异记录检索接口，支持多数据源的优雅降级
    优先级：文件存储 > 数据库记录 > Agent实时数据 > 任务结果字段
    """

    @staticmethod
    def get_task_differences(task_id: str, page: int = 1, size: int = 10,
                                  diff_type: Optional[str] = None,
                                  keyword: Optional[str] = None,
                                  field_name: Optional[str] = None) -> Dict[str, Any]:
        """获取任务的差异记录 - 新的统一接口

        Args:
            task_id: 任务ID (可以是external_id或数据库ID)
            page: 页码
            size: 每页大小
            diff_type: 差异类型过滤
            keyword: 关键字搜索
            field_name: 字段名过滤

        Returns:
            Dict: 查询结果
        """
        try:
            # 直接使用数据库查询逻辑
            logger.info(f"开始查询任务差异 - 任务ID: {task_id}")

            # 尝试通过task_id找到CompareTask实例
            task = None
            try:
                from .models import CompareTask
                # 先尝试通过external_id查找
                task = CompareTask.objects.filter(external_id=task_id).first()
                # 如果找不到，尝试通过id查找
                if not task:
                    task = CompareTask.objects.filter(id=task_id).first()
            except Exception as e:
                logger.warning(f"查找任务失败: {str(e)}")

            if task:
                # 使用原有的查询逻辑
                filters = {}
                if diff_type:
                    filters['diff_type'] = diff_type
                if keyword:
                    filters['keyword'] = keyword
                if field_name:
                    filters['field_name'] = field_name

                legacy_result = DiffRetrievalService.get_task_diffs(task, page, size, filters)
                if legacy_result['success']:
                    # 转换为新格式
                    return {
                        'success': True,
                        'data': legacy_result['results'],
                        'total': legacy_result['total'],
                        'page': legacy_result['page'],
                        'size': legacy_result['size'],
                        'pages': legacy_result['pages'],
                        'has_next': page < legacy_result['pages'],
                        'has_prev': page > 1,
                        'source': legacy_result.get('source', 'database_legacy'),
                        'query_time_ms': 0,
                        'cache_hit': False
                    }

            # 所有方法都失败
            return {
                'success': False,
                'message': '无法从任何数据源获取差异记录',
                'data': [],
                'total': 0,
                'page': page,
                'size': size,
                'pages': 0,
                'has_next': False,
                'has_prev': False,
                'source': 'none'
            }

        except Exception as e:
            logger.error(f"获取任务差异失败: {str(e)}")
            return {
                'success': False,
                'message': f'查询失败: {str(e)}',
                'data': [],
                'total': 0,
                'page': page,
                'size': size,
                'pages': 0,
                'has_next': False,
                'has_prev': False,
                'source': 'error'
            }

    @staticmethod
    def get_task_diffs(task: CompareTask, page: int = 1, size: int = 10,
                      filters: Optional[Dict] = None) -> Dict[str, Any]:
        """获取任务的差异记录 - 原有接口保持兼容

        优先级：数据库记录 > Agent实时数据 > 任务结果字段
        """
        filters = filters or {}

        # 1. 尝试从数据库获取持久化的差异记录
        db_result = DiffRetrievalService._get_from_database(task, page, size, filters)
        if db_result['success'] and db_result['total'] > 0:
            db_result['source'] = 'database'
            return db_result

        # 2. 如果数据库中没有记录，尝试从Agent获取并持久化
        logger.info(f"数据库中无差异记录，尝试从Agent获取任务{task.id}的差异数据")

        # 先尝试持久化
        persist_result = DiffPersistenceService.persist_task_diffs(task, force_refresh=False)
        if persist_result['success'] and persist_result['stats']['persisted_count'] > 0:
            # 持久化成功，重新从数据库查询
            db_result = DiffRetrievalService._get_from_database(task, page, size, filters)
            if db_result['success']:
                db_result['source'] = 'database_from_agent'
                db_result['persistence_info'] = persist_result
                return db_result

        # 3. 如果持久化失败，尝试直接从Agent获取临时数据
        agent_result = DiffRetrievalService._get_from_agent(task, page, size, filters)
        if agent_result['success']:
            agent_result['source'] = 'agent_temporary'
            agent_result['warning'] = '数据来自临时缓存，建议手动持久化'
            return agent_result

        # 4. 最后尝试从任务结果字段获取
        task_result = DiffRetrievalService._get_from_task_result(task, page, size, filters)
        if task_result['success']:
            task_result['source'] = 'task_result_backup'
            task_result['warning'] = '数据来自备份字段，可能不是最新版本'
            return task_result

        # 5. 所有数据源都失败
        return {
            'success': False,
            'message': '无法从任何数据源获取差异记录',
            'source': 'none',
            'results': [],
            'total': 0,
            'page': page,
            'size': size,
            'pages': 0
        }

    @staticmethod
    def _get_from_database(task: CompareTask, page: int, size: int,
                          filters: Dict) -> Dict[str, Any]:
        """从数据库获取差异记录"""
        try:
            from django.db.models import Q

            queryset = CompareDiff.objects.filter(task=task)

            # 应用筛选条件
            if filters.get('diff_type'):
                queryset = queryset.filter(diff_type=filters['diff_type'])
            if filters.get('severity'):
                queryset = queryset.filter(severity=filters['severity'])
            if filters.get('field_name'):
                queryset = queryset.filter(field_name__icontains=filters['field_name'])
            if filters.get('keyword'):
                keyword = filters['keyword']
                queryset = queryset.filter(
                    Q(table_name__icontains=keyword) |
                    Q(field_name__icontains=keyword) |
                    Q(record_id__icontains=keyword) |
                    Q(source_value__icontains=keyword) |
                    Q(target_value__icontains=keyword)
                )

            # 排序和分页
            queryset = queryset.order_by('-create_time')
            total = queryset.count()

            start = (page - 1) * size
            end = start + size
            results = queryset[start:end]

            # 序列化
            from .serializers import CompareDiffSerializer
            serializer = CompareDiffSerializer(results, many=True)

            return {
                'success': True,
                'results': serializer.data,
                'total': total,
                'page': page,
                'size': size,
                'pages': (total + size - 1) // size if total > 0 else 0
            }

        except Exception as e:
            logger.error(f"从数据库获取差异记录失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def _get_from_agent(task: CompareTask, page: int, size: int,
                       filters: Dict) -> Dict[str, Any]:
        """从Agent获取差异记录（临时数据）"""
        try:
            if not task.external_id:
                return {'success': False, 'message': '任务无外部ID'}

            client = get_db_agent_client()
            agent_result = client.get_task_result(task.external_id)

            if not agent_result.get("success", False):
                return {
                    'success': False,
                    'message': f"Agent返回错误: {agent_result.get('message', '未知错误')}"
                }

            data = agent_result.get("data", {})
            details = data.get('details', {})
            items = details.get('items', [])

            return DiffRetrievalService._process_agent_items(
                task, items, page, size, filters
            )

        except Exception as e:
            logger.error(f"从Agent获取差异记录失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def _get_from_task_result(task: CompareTask, page: int, size: int,
                             filters: Dict) -> Dict[str, Any]:
        """从任务结果字段获取差异记录（备份数据）"""
        try:
            if not task.result:
                return {'success': False, 'message': '任务结果为空'}

            details = task.result.get('details', {})
            items = details.get('items', [])

            return DiffRetrievalService._process_agent_items(
                task, items, page, size, filters
            )

        except Exception as e:
            logger.error(f"从任务结果获取差异记录失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def _process_agent_items(task: CompareTask, items: List[Dict],
                           page: int, size: int, filters: Dict) -> Dict[str, Any]:
        """处理Agent格式的差异项数据"""
        try:
            # 应用筛选条件
            filtered_items = []
            for item in items:
                # 差异类型筛选
                if filters.get('diff_type') and item.get('diff_type') != filters['diff_type']:
                    continue

                # 关键字筛选
                if filters.get('keyword'):
                    keyword = filters['keyword'].lower()
                    searchable_text = f"{item.get('key', {}).get('primary_key', '')} {item.get('diff_type', '')}".lower()
                    if keyword not in searchable_text:
                        continue

                filtered_items.append(item)

            # 分页处理
            total = len(filtered_items)
            start = (page - 1) * size
            end = start + size
            paginated_items = filtered_items[start:end]

            # 转换为标准格式
            converted_results = []
            for i, item in enumerate(paginated_items):
                diff_record = {
                    'id': start + i + 1,  # 临时ID
                    'task_id': task.id,
                    'table_name': item.get('table_name', ''),
                    'field_name': '',
                    'record_id': str(item.get('key', {}).get('primary_key', '')),
                    'diff_type': item.get('diff_type', ''),
                    'diff_type_display': DiffRetrievalService._get_diff_type_display(item.get('diff_type', '')),
                    'severity': 'medium',
                    'severity_display': '中',
                    'source_value': str(item.get('source_record', '')),
                    'target_value': str(item.get('target_record', '')),
                    'context': {
                        'key': item.get('key', {}),
                        'field_diffs': item.get('field_diffs'),
                        'agent_format': True,
                        'temporary_data': True
                    },
                    'create_time': task.end_time.isoformat() if task.end_time else timezone.now().isoformat()
                }
                converted_results.append(diff_record)

            return {
                'success': True,
                'results': converted_results,
                'total': total,
                'page': page,
                'size': size,
                'pages': (total + size - 1) // size if total > 0 else 0
            }

        except Exception as e:
            logger.error(f"处理Agent差异项失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def _get_diff_type_display(diff_type: str) -> str:
        """获取差异类型的显示名称"""
        diff_type_map = {
            'target_missing': '目标缺失',
            'source_missing': '源缺失',
            'value_different': '值不同',
            'type_different': '类型不同',
            'structure_different': '结构不同'
        }
        return diff_type_map.get(diff_type, diff_type)


class TaskCompletionService:
    """任务完成处理服务"""

    @staticmethod
    def handle_task_completion(task: CompareTask) -> Dict[str, Any]:
        """处理任务完成后的操作

        包括数据持久化、状态更新、通知发送等
        """
        result = {
            'success': True,
            'message': '任务完成处理成功',
            'actions': []
        }

        try:
            # 1. 自动持久化差异记录
            persist_result = DiffPersistenceService.auto_persist_on_completion(task)
            result['actions'].append({
                'action': 'persist_diffs',
                'success': persist_result['success'],
                'message': persist_result['message'],
                'stats': persist_result.get('stats', {})
            })

            # 2. 更新任务统计信息
            if persist_result['success']:
                stats = persist_result.get('stats', {})
                if stats.get('persisted_count', 0) > 0:
                    task.different_records = stats['persisted_count']
                    task.save(update_fields=['different_records'])
                    result['actions'].append({
                        'action': 'update_stats',
                        'success': True,
                        'message': f'更新任务统计: 差异记录{stats["persisted_count"]}条'
                    })

            # 3. 清理临时数据（可选）
            # 这里可以添加清理Agent临时数据的逻辑

            logger.info(f"任务{task.id}完成处理成功")

        except Exception as e:
            logger.exception(f"处理任务{task.id}完成时发生异常: {str(e)}")
            result.update({
                'success': False,
                'message': f'任务完成处理失败: {str(e)}'
            })

        return result
