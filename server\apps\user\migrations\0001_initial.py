# Generated by Django 4.1.8 on 2024-09-04 09:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('system', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('avatar', models.CharField(default=None, max_length=100, null=True)),
                ('nickname', models.CharField(default=None, max_length=100, null=True)),
                ('username', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('email', models.<PERSON>ail<PERSON>ield(max_length=254, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_staff', models.<PERSON>oleanField(default=False)),
                ('is_superuser', models.BooleanField(default=False)),
                ('dept', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='system.deptinfo', verbose_name='部门')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('role', models.ManyToManyField(blank=True, to='system.role', verbose_name='角色')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
