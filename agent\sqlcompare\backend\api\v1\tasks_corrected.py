#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理API端点 - 修正版
直接使用SQLCompare核心数据模型，Backend API作为服务包装层
"""

import os
import sys
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field

# 确保能导入SQLCompare核心模块
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# 直接使用SQLCompare的数据模型（不重复定义）
from models.service_models import TaskStatus, Task, ComparisonResult
from backend.services.comparison_service import ComparisonService

router = APIRouter()


# API请求/响应模型（仅用于API层数据验证，不重复定义业务模型）
class TaskCreateRequest(BaseModel):
    """创建任务请求模型 - 仅用于API参数验证"""
    name: str = Field(..., min_length=1, max_length=200, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    priority: int = Field(5, ge=1, le=10, description="任务优先级")
    
    # 数据库连接配置
    source_connection: Dict[str, Any] = Field(..., description="源数据库连接配置")
    target_connection: Dict[str, Any] = Field(..., description="目标数据库连接配置")
    
    # 比对规则配置
    comparison_rules: List[Dict[str, Any]] = Field(..., min_items=1, description="比对规则列表")
    comparison_type: int = Field(2, ge=1, le=3, description="比对类型")


class TaskActionRequest(BaseModel):
    """任务操作请求模型"""
    action: str = Field(..., description="操作类型: start, cancel")
    reason: Optional[str] = Field(None, description="操作原因")


class APIResponse(BaseModel):
    """API统一响应格式"""
    success: bool = Field(True, description="是否成功")
    message: str = Field("操作成功", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


def get_comparison_service() -> ComparisonService:
    """获取比对服务实例"""
    return ComparisonService()


@router.post("/", response_model=APIResponse)
async def create_task(
    task_data: TaskCreateRequest,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    创建比对任务
    
    直接调用SQLCompare核心功能，不通过适配器
    """
    try:
        # 直接调用SQLCompare的比对服务
        task_id = await comparison_service.create_comparison_task(
            name=task_data.name,
            description=task_data.description,
            source_connection=task_data.source_connection,
            target_connection=task_data.target_connection,
            comparison_rules=task_data.comparison_rules,
            comparison_type=task_data.comparison_type,
            priority=task_data.priority
        )
        
        return APIResponse(
            success=True,
            message="任务创建成功",
            data={"task_id": task_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=APIResponse)
async def get_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务列表
    
    直接使用SQLCompare的TaskManager查询
    """
    try:
        # 直接使用SQLCompare的TaskManager
        task_manager = comparison_service.task_manager
        tasks = task_manager.get_task_list(status=status, limit=limit)
        
        return APIResponse(
            success=True,
            message="获取任务列表成功",
            data={"tasks": tasks, "total": len(tasks)}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}", response_model=APIResponse)
async def get_task(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务详情
    
    直接使用SQLCompare的TaskManager查询
    """
    try:
        # 直接调用SQLCompare的任务查询
        task_info = comparison_service.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        return APIResponse(
            success=True,
            message="获取任务详情成功",
            data=task_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/actions", response_model=APIResponse)
async def execute_task_action(
    task_id: str,
    action_request: TaskActionRequest,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    执行任务操作
    
    直接调用SQLCompare的任务管理功能
    """
    try:
        if action_request.action == "start":
            # 直接调用SQLCompare的任务启动
            success = await comparison_service.start_comparison_task(task_id)
            
            if success:
                return APIResponse(
                    success=True,
                    message="任务启动成功",
                    data={"task_id": task_id, "action": "start"}
                )
            else:
                raise HTTPException(status_code=400, detail="任务启动失败")
                
        elif action_request.action == "cancel":
            # 直接调用SQLCompare的任务取消
            success = comparison_service.cancel_task(task_id)
            
            if success:
                return APIResponse(
                    success=True,
                    message="任务取消成功",
                    data={"task_id": task_id, "action": "cancel"}
                )
            else:
                raise HTTPException(status_code=400, detail="任务取消失败")
                
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action_request.action}")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/progress", response_model=APIResponse)
async def get_task_progress(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务进度
    
    直接使用SQLCompare的进度查询
    """
    try:
        # 直接调用SQLCompare的进度查询
        progress_logs = comparison_service.get_task_progress(task_id)
        task_info = comparison_service.get_task_status(task_id)
        
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        # 计算进度百分比
        total_records = task_info.get('total_records', 0)
        processed_records = task_info.get('processed_records', 0)
        progress_percentage = 0.0
        
        if total_records > 0:
            progress_percentage = (processed_records / total_records) * 100
        
        progress_data = {
            "task_id": task_id,
            "status": task_info.get('status'),
            "progress_percentage": progress_percentage,
            "processed_records": processed_records,
            "total_records": total_records,
            "recent_logs": progress_logs[:10]  # 最近10条日志
        }
        
        return APIResponse(
            success=True,
            message="获取任务进度成功",
            data=progress_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/results", response_model=APIResponse)
async def get_task_results(
    task_id: str,
    limit: int = Query(100, ge=1, le=1000, description="返回结果数量限制"),
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务比对结果
    
    直接使用SQLCompare的结果查询
    """
    try:
        # 直接调用SQLCompare的结果查询
        results = comparison_service.get_comparison_results(task_id, limit=limit)
        
        return APIResponse(
            success=True,
            message="获取比对结果成功",
            data={"task_id": task_id, "results": results, "count": len(results)}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}", response_model=APIResponse)
async def delete_task(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    删除任务
    
    直接调用SQLCompare的任务删除功能
    """
    try:
        # 检查任务是否存在
        task_info = comparison_service.get_task_status(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        # 如果任务正在运行，先取消
        if task_info.get('status') == TaskStatus.RUNNING:
            comparison_service.cancel_task(task_id)
        
        # 这里可以添加实际的删除逻辑
        # 目前通过标记为已取消来实现软删除
        success = comparison_service.cancel_task(task_id)
        
        if success:
            return APIResponse(
                success=True,
                message="任务删除成功",
                data={"task_id": task_id}
            )
        else:
            raise HTTPException(status_code=400, detail="任务删除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
