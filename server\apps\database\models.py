from django.db import models
from django.utils import timezone


class CompareModel(models.Model):
    """数据比对模型"""
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='模型名称')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    source_connection = models.CharField(max_length=100, blank=True, null=True, verbose_name='源连接')
    target_connection = models.CharField(max_length=100, blank=True, null=True, verbose_name='目标连接')
    status = models.BooleanField(default=True, verbose_name='状态')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '比对模型'
        verbose_name_plural = '比对模型'
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class TableConfig(models.Model):
    """表配置"""
    model = models.ForeignKey(CompareModel, on_delete=models.CASCADE, related_name='tables', verbose_name='比对模型')
    table_id = models.CharField(max_length=50, verbose_name='表ID')
    remark = models.CharField(max_length=255, blank=True, null=True, verbose_name='备注')
    sql_1 = models.TextField(verbose_name='源SQL')
    sql_2 = models.TextField(verbose_name='目标SQL')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '表配置'
        verbose_name_plural = '表配置'
        ordering = ['id']

    def __str__(self):
        return f"{self.model.name}-{self.table_id}"


class CompareTask(models.Model):
    """比对任务"""
    STATUS_CHOICES = (
        ('waiting', '等待中'),
        ('running', '运行中'),
        ('success', '成功'),
        ('failed', '失败'),
        ('stopped', '已停止'),
    )

    id = models.AutoField(primary_key=True, verbose_name='ID')
    model = models.ForeignKey(CompareModel, on_delete=models.CASCADE, related_name='tasks', verbose_name='比对模型')
    name = models.CharField(max_length=100, blank=True, null=True, verbose_name='任务名称')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='waiting', verbose_name='状态')
    progress = models.IntegerField(default=0, verbose_name='进度')
    total_records = models.IntegerField(default=0, verbose_name='总记录数')
    processed_records = models.IntegerField(default=0, verbose_name='已处理记录数')
    matched_records = models.IntegerField(default=0, verbose_name='匹配记录数')
    different_records = models.IntegerField(default=0, verbose_name='差异记录数')
    error_records = models.IntegerField(default=0, verbose_name='错误记录数')
    source_only_records = models.IntegerField(default=0, verbose_name='源独有记录数')
    target_only_records = models.IntegerField(default=0, verbose_name='目标独有记录数')
    execution_time = models.FloatField(default=0.0, verbose_name='执行时间(秒)')
    start_time = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    message = models.TextField(blank=True, null=True, verbose_name='消息')
    result = models.JSONField(blank=True, null=True, verbose_name='任务结果')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    external_id = models.CharField(max_length=100, null=True, blank=True, verbose_name='外部任务ID')

    class Meta:
        verbose_name = '比对任务'
        verbose_name_plural = '比对任务'
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.model.name}-{self.id}"
    
    @property
    def duration(self):
        """任务持续时间"""
        if not self.start_time:
            return None
        
        end = self.end_time or timezone.now()
        if end < self.start_time:
            return None
            
        seconds = (end - self.start_time).total_seconds()
        
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        
        if hours > 0:
            return f"{int(hours)}小时{int(minutes)}分钟"
        elif minutes > 0:
            return f"{int(minutes)}分钟{int(seconds)}秒"
        else:
            return f"{int(seconds)}秒"


class CompareDiff(models.Model):
    """比对差异"""
    DIFF_TYPE_CHOICES = (
        ('value', '值不同'),
        ('source_missing', '源端缺失'),
        ('target_missing', '目标端缺失'),
        ('type', '类型不同'),
    )
    
    SEVERITY_CHOICES = (
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    )
    
    id = models.AutoField(primary_key=True, verbose_name='ID')
    task = models.ForeignKey(CompareTask, on_delete=models.CASCADE, related_name='diffs', verbose_name='比对任务')
    table_name = models.CharField(max_length=100, verbose_name='表名')
    field_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='字段名')
    record_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='记录ID')
    diff_type = models.CharField(max_length=20, choices=DIFF_TYPE_CHOICES, verbose_name='差异类型')
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='medium', verbose_name='严重程度')
    source_value = models.JSONField(blank=True, null=True, verbose_name='源值')
    target_value = models.JSONField(blank=True, null=True, verbose_name='目标值')
    context = models.JSONField(blank=True, null=True, verbose_name='上下文')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        verbose_name = '比对差异'
        verbose_name_plural = '比对差异'
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.task.model.name}-{self.table_name}-{self.field_name or '记录'}"


class DataConnection(models.Model):
    """数据库连接"""
    TYPE_CHOICES = (
        ('db2', 'DB2'),
        ('gaussdb', 'GaussDB'),
        ('mysql', 'MySQL'),
        ('postgresql', 'PostgreSQL'),
        ('oracle', 'Oracle'),
        ('sqlserver', 'SQL Server'),
    )
    
    STATUS_CHOICES = (
        ('active', '正常'),
        ('inactive', '禁用'),
    )
    
    id = models.AutoField(primary_key=True, verbose_name='ID')
    name = models.CharField(max_length=100, verbose_name='连接名称')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='db2', verbose_name='数据库类型')
    host = models.CharField(max_length=255, verbose_name='主机')
    port = models.IntegerField(verbose_name='端口')
    username = models.CharField(max_length=100, verbose_name='用户名')
    password = models.CharField(max_length=255, blank=True, null=True, verbose_name='密码')
    database = models.CharField(max_length=100, verbose_name='数据库名')
    parameters = models.JSONField(blank=True, null=True, verbose_name='连接参数')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '数据库连接'
        verbose_name_plural = '数据库连接'
        ordering = ['-create_time']

    def __str__(self):
        return self.name


class SourceOnlyRecord(models.Model):
    """源独有记录"""
    id = models.AutoField(primary_key=True, verbose_name='ID')
    task = models.ForeignKey(CompareTask, on_delete=models.CASCADE, related_name='source_only_data', verbose_name='比对任务')
    table_name = models.CharField(max_length=100, verbose_name='表名')
    record_id = models.CharField(max_length=100, verbose_name='记录ID')
    data = models.JSONField(verbose_name='记录数据')
    reason = models.CharField(max_length=200, blank=True, null=True, verbose_name='原因')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        verbose_name = '源独有记录'
        verbose_name_plural = '源独有记录'
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.task.model.name}-{self.table_name}-{self.record_id}"


class TargetOnlyRecord(models.Model):
    """目标独有记录"""
    id = models.AutoField(primary_key=True, verbose_name='ID')
    task = models.ForeignKey(CompareTask, on_delete=models.CASCADE, related_name='target_only_data', verbose_name='比对任务')
    table_name = models.CharField(max_length=100, verbose_name='表名')
    record_id = models.CharField(max_length=100, verbose_name='记录ID')
    data = models.JSONField(verbose_name='记录数据')
    reason = models.CharField(max_length=200, blank=True, null=True, verbose_name='原因')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        verbose_name = '目标独有记录'
        verbose_name_plural = '目标独有记录'
        ordering = ['-create_time']

    def __str__(self):
        return f"{self.task.model.name}-{self.table_name}-{self.record_id}"
