/**
 * 错误类别和严重程度定义
 * 与后端和DB-Agent保持一致的错误分类和严重程度定义
 */

/**
 * 错误类别枚举
 */
export enum ErrorCategory {
  // 数据库相关错误
  DATABASE = "database",     // 数据库通用错误
  QUERY = "query",           // 查询错误
  COMPARISON = "comparison", // 数据比对错误
  
  // 网络相关错误
  NETWORK = "network",       // 网络通用错误
  CONNECTION = "connection", // 连接错误
  TIMEOUT = "timeout",       // 超时错误
  
  // 认证授权相关错误
  AUTHENTICATION = "authentication", // 认证错误
  AUTHORIZATION = "authorization",   // 授权错误
  PERMISSION = "permission",         // 权限错误
  
  // 数据相关错误
  VALIDATION = "validation", // 数据验证错误
  
  // 资源相关错误
  RESOURCE = "resource",     // 资源错误
  
  // 配置相关错误
  CONFIGURATION = "configuration", // 配置错误
  
  // 服务相关错误
  EXTERNAL = "external",     // 外部服务错误
  INTERNAL = "internal",     // 内部错误
  SYSTEM = "system",         // 系统错误
  
  // 其他
  UNKNOWN = "unknown"        // 未知错误
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  INFO = "info",           // 信息级别，不影响任务执行
  WARNING = "warning",     // 警告级别，可能影响部分功能
  ERROR = "error",         // 错误级别，影响任务正常执行
  CRITICAL = "critical"    // 严重错误，导致任务失败
}

/**
 * 错误对象接口
 */
export interface ErrorReport {
  // 基本信息
  type: string;            // 错误类型
  message: string;         // 错误消息
  timestamp: string;       // 时间戳
  
  // 分类信息
  category: ErrorCategory; // 错误类别
  severity: ErrorSeverity; // 错误严重程度
  is_fatal: boolean;       // 是否为致命错误
  
  // 上下文信息
  task_id?: string;        // 相关任务ID
  context?: any;           // 错误上下文
  traceback?: string;      // 堆栈跟踪
  
  // 其他信息
  report_id?: string;      // 报告ID
  originalError?: any;     // 原始错误对象
}

/**
 * 获取错误类别的显示名称
 * @param category 错误类别
 * @returns 显示名称
 */
export function getCategoryDisplayName(category: ErrorCategory | string): string {
  const categoryMap: Record<string, string> = {
    [ErrorCategory.DATABASE]: "数据库错误",
    [ErrorCategory.QUERY]: "查询错误",
    [ErrorCategory.COMPARISON]: "数据比对错误",
    [ErrorCategory.NETWORK]: "网络错误",
    [ErrorCategory.CONNECTION]: "连接错误",
    [ErrorCategory.TIMEOUT]: "超时错误",
    [ErrorCategory.AUTHENTICATION]: "认证错误",
    [ErrorCategory.AUTHORIZATION]: "授权错误",
    [ErrorCategory.PERMISSION]: "权限错误",
    [ErrorCategory.VALIDATION]: "数据验证错误",
    [ErrorCategory.RESOURCE]: "资源错误",
    [ErrorCategory.CONFIGURATION]: "配置错误",
    [ErrorCategory.EXTERNAL]: "外部服务错误",
    [ErrorCategory.INTERNAL]: "内部错误",
    [ErrorCategory.SYSTEM]: "系统错误",
    [ErrorCategory.UNKNOWN]: "未知错误"
  };
  
  return categoryMap[category] || "未知错误类型";
}

/**
 * 获取错误严重程度的显示名称
 * @param severity 错误严重程度
 * @returns 显示名称
 */
export function getSeverityDisplayName(severity: ErrorSeverity | string): string {
  const severityMap: Record<string, string> = {
    [ErrorSeverity.INFO]: "信息",
    [ErrorSeverity.WARNING]: "警告",
    [ErrorSeverity.ERROR]: "错误",
    [ErrorSeverity.CRITICAL]: "严重错误"
  };
  
  return severityMap[severity] || "未知严重程度";
}

/**
 * 获取错误严重程度对应的UI类型
 * @param severity 错误严重程度
 * @returns UI类型（用于Element UI的Alert等组件）
 */
export function getSeverityType(severity: ErrorSeverity | string): string {
  const severityTypeMap: Record<string, string> = {
    [ErrorSeverity.INFO]: "info",
    [ErrorSeverity.WARNING]: "warning",
    [ErrorSeverity.ERROR]: "error",
    [ErrorSeverity.CRITICAL]: "error"
  };
  
  return severityTypeMap[severity] || "error";
}

/**
 * 获取错误类别对应的图标
 * @param category 错误类别
 * @returns 图标名称
 */
export function getCategoryIcon(category: ErrorCategory | string): string {
  const categoryIconMap: Record<string, string> = {
    [ErrorCategory.DATABASE]: "database",
    [ErrorCategory.QUERY]: "search",
    [ErrorCategory.COMPARISON]: "data-analysis",
    [ErrorCategory.NETWORK]: "connection",
    [ErrorCategory.CONNECTION]: "link",
    [ErrorCategory.TIMEOUT]: "timer",
    [ErrorCategory.AUTHENTICATION]: "user",
    [ErrorCategory.AUTHORIZATION]: "lock",
    [ErrorCategory.PERMISSION]: "key",
    [ErrorCategory.VALIDATION]: "document-checked",
    [ErrorCategory.RESOURCE]: "cpu",
    [ErrorCategory.CONFIGURATION]: "setting",
    [ErrorCategory.EXTERNAL]: "service",
    [ErrorCategory.INTERNAL]: "server",
    [ErrorCategory.SYSTEM]: "monitor",
    [ErrorCategory.UNKNOWN]: "question"
  };
  
  return categoryIconMap[category] || "warning";
}
