"""
基于Agent标准的序列化器
只读取Agent写入的数据，不修改Agent管理的表结构

设计原则：
1. 所有序列化器都是只读的（read_only=True）
2. 不提供create/update方法，避免与Agent冲突
3. 直接读取Agent写入的标准表数据
4. 为前端提供统一的API接口
"""
from rest_framework import serializers
from .models_agent_standard import (
    ComparisonConnection, 
    ComparisonModel, 
    ComparisonTableRule, 
    ComparisonTask, 
    ComparisonResult,
    User
)


class AgentUserSerializer(serializers.ModelSerializer):
    """Agent用户序列化器 - 只读"""
    
    class Meta:
        model = User
        fields = ['user_id', 'username', 'email', 'role', 'is_active', 'create_time', 'update_time']
        read_only_fields = fields  # 所有字段只读


class AgentConnectionSerializer(serializers.ModelSerializer):
    """Agent数据库连接序列化器 - 只读"""
    
    class Meta:
        model = ComparisonConnection
        fields = ['id', 'name', 'type', 'host', 'port', 'username', 'database', 
                 'params', 'status', 'create_time', 'update_time']
        read_only_fields = fields  # 所有字段只读
        extra_kwargs = {
            'password': {'write_only': True}  # 密码不返回
        }

    def to_representation(self, instance):
        """自定义序列化输出，隐藏敏感信息"""
        data = super().to_representation(instance)
        # 移除密码字段
        data.pop('password', None)
        return data


class AgentTableRuleSerializer(serializers.ModelSerializer):
    """Agent表规则序列化器 - 只读"""
    
    class Meta:
        model = ComparisonTableRule
        fields = ['id', 'table_id', 'table_name', 'sql_1', 'sql_2', 'remark',
                 'primary_keys', 'ignore_fields', 'field_mappings', 'is_active',
                 'create_time', 'update_time']
        read_only_fields = fields  # 所有字段只读


class AgentComparisonModelSerializer(serializers.ModelSerializer):
    """Agent比对模型序列化器 - 只读"""
    table_rules = AgentTableRuleSerializer(many=True, read_only=True)
    source_connection_name = serializers.CharField(source='source_connid.name', read_only=True)
    target_connection_name = serializers.CharField(source='target_connid.name', read_only=True)
    source_connection_detail = AgentConnectionSerializer(source='source_connid', read_only=True)
    target_connection_detail = AgentConnectionSerializer(source='target_connid', read_only=True)
    
    class Meta:
        model = ComparisonModel
        fields = ['id', 'name', 'description', 'source_connid', 'target_connid',
                 'source_connection_name', 'target_connection_name', 
                 'source_connection_detail', 'target_connection_detail',
                 'cmp_type', 'global_config', 'status', 'create_time', 'update_time', 
                 'table_rules']
        read_only_fields = fields  # 所有字段只读


class AgentComparisonTaskSerializer(serializers.ModelSerializer):
    """Agent比对任务序列化器 - 只读"""
    model_name = serializers.CharField(source='model_id.name', read_only=True)
    user_name = serializers.CharField(source='user_id.username', read_only=True)
    table_rule_detail = AgentTableRuleSerializer(source='table_rule_id', read_only=True)
    matched_records = serializers.ReadOnlyField()
    duration = serializers.ReadOnlyField()
    
    class Meta:
        model = ComparisonTask
        fields = ['id', 'task_id', 'user_id', 'user_name', 'model_id', 'model_name', 
                 'table_rule_id', 'table_rule_detail', 'task_name', 'description', 
                 'status', 'progress_pct', 'current_step', 'create_time', 'start_time', 
                 'complete_time', 'update_time', 'total_records', 'processed_records', 
                 'diff_records', 'source_only', 'target_only', 'matched_records', 
                 'exec_time', 'duration', 'error_msg', 'error_details', 'retention_days', 
                 'auto_cleanup']
        read_only_fields = fields  # 所有字段只读


class AgentComparisonResultSerializer(serializers.ModelSerializer):
    """Agent比对结果序列化器 - 只读"""
    task_name = serializers.CharField(source='task_id.task_name', read_only=True)
    
    class Meta:
        model = ComparisonResult
        fields = ['id', 'task_id', 'task_name', 'table_name', 'record_key', 'status', 
                 'field_name', 'source_value', 'target_value', 'diff_type', 
                 'partition_key', 'created_at']
        read_only_fields = fields  # 所有字段只读


class AgentTaskSummarySerializer(serializers.Serializer):
    """Agent任务摘要序列化器 - 用于仪表板显示"""
    task_id = serializers.CharField(read_only=True)
    task_name = serializers.CharField(read_only=True)
    model_name = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    progress_pct = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    current_step = serializers.CharField(read_only=True)
    
    # 统计信息
    total_records = serializers.IntegerField(read_only=True)
    processed_records = serializers.IntegerField(read_only=True)
    diff_records = serializers.IntegerField(read_only=True)
    source_only = serializers.IntegerField(read_only=True)
    target_only = serializers.IntegerField(read_only=True)
    matched_records = serializers.IntegerField(read_only=True)
    
    # 时间信息
    create_time = serializers.DateTimeField(read_only=True)
    start_time = serializers.DateTimeField(read_only=True)
    complete_time = serializers.DateTimeField(read_only=True)
    duration = serializers.CharField(read_only=True)
    exec_time = serializers.DecimalField(max_digits=10, decimal_places=3, read_only=True)
    
    # 错误信息
    error_msg = serializers.CharField(read_only=True)


class AgentTaskProgressSerializer(serializers.Serializer):
    """Agent任务进度序列化器 - 用于实时进度更新"""
    task_id = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    progress_pct = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    current_step = serializers.CharField(read_only=True)
    processed_records = serializers.IntegerField(read_only=True)
    total_records = serializers.IntegerField(read_only=True)
    update_time = serializers.DateTimeField(read_only=True)


class AgentDashboardStatsSerializer(serializers.Serializer):
    """Agent仪表板统计序列化器"""
    
    # 连接统计
    total_connections = serializers.IntegerField(read_only=True)
    active_connections = serializers.IntegerField(read_only=True)
    
    # 模型统计
    total_models = serializers.IntegerField(read_only=True)
    active_models = serializers.IntegerField(read_only=True)
    
    # 任务统计
    total_tasks = serializers.IntegerField(read_only=True)
    running_tasks = serializers.IntegerField(read_only=True)
    completed_tasks = serializers.IntegerField(read_only=True)
    failed_tasks = serializers.IntegerField(read_only=True)
    
    # 今日统计
    today_tasks = serializers.IntegerField(read_only=True)
    today_completed = serializers.IntegerField(read_only=True)
    
    # 结果统计
    total_results = serializers.IntegerField(read_only=True)
    total_differences = serializers.IntegerField(read_only=True)


class TaskCreateRequestSerializer(serializers.Serializer):
    """任务创建请求序列化器 - 用于向Agent发送任务请求"""
    model_id = serializers.IntegerField(required=True, help_text="比对模型ID")
    table_rule_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="表规则ID列表，为空则使用模型的所有规则"
    )
    task_name = serializers.CharField(max_length=200, required=False, help_text="任务名称")
    description = serializers.CharField(required=False, help_text="任务描述")
    user_id = serializers.CharField(max_length=50, required=True, help_text="用户ID")
    
    def validate_model_id(self, value):
        """验证模型ID是否存在"""
        try:
            ComparisonModel.objects.get(id=value, status=True)
        except ComparisonModel.DoesNotExist:
            raise serializers.ValidationError("指定的比对模型不存在或已禁用")
        return value
    
    def validate_table_rule_ids(self, value):
        """验证表规则ID是否存在"""
        if value:
            existing_ids = set(ComparisonTableRule.objects.filter(
                id__in=value, is_active=True
            ).values_list('id', flat=True))
            
            invalid_ids = set(value) - existing_ids
            if invalid_ids:
                raise serializers.ValidationError(f"以下表规则ID不存在或已禁用: {invalid_ids}")
        return value


class TaskControlRequestSerializer(serializers.Serializer):
    """任务控制请求序列化器 - 用于控制Agent任务"""
    action = serializers.ChoiceField(
        choices=['start', 'pause', 'resume', 'cancel'],
        required=True,
        help_text="操作类型"
    )
    task_id = serializers.CharField(max_length=50, required=True, help_text="任务ID")
    
    def validate_task_id(self, value):
        """验证任务ID是否存在"""
        try:
            ComparisonTask.objects.get(task_id=value)
        except ComparisonTask.DoesNotExist:
            raise serializers.ValidationError("指定的任务不存在")
        return value


class AgentHealthCheckSerializer(serializers.Serializer):
    """Agent健康检查序列化器"""
    agent_status = serializers.CharField(read_only=True, help_text="Agent状态")
    database_status = serializers.CharField(read_only=True, help_text="数据库状态")
    last_heartbeat = serializers.DateTimeField(read_only=True, help_text="最后心跳时间")
    active_tasks = serializers.IntegerField(read_only=True, help_text="活跃任务数")
    system_load = serializers.DictField(read_only=True, help_text="系统负载信息")
