/**
 * WebSocket工具类型声明文件
 */

export interface WebSocketConfig {
  onMessage?: (data: any, event: MessageEvent) => void;
  onOpen?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (error: any) => void;
  onReconnecting?: (attempt: number, maxAttempts: number) => void;
  onReconnectFailed?: () => void;
  shouldStopReconnect?: () => boolean; // 新增：检查是否应该停止重连
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  debug?: boolean;
}

export interface WebSocketState {
  url: string;
  isConnected: boolean;
  reconnectAttempts: number;
  readyState: number;
  timestamp: string;
}

export interface WebSocketController {
  connect: () => void;
  close: () => void;
  send: (data: any) => boolean;
  getState: () => WebSocketState;
  isConnected: () => boolean;
  getReadyState: () => number;
  getReconnectAttempts: () => number;
  resetReconnectAttempts: () => void;
}

/**
 * 创建并管理WebSocket连接
 */
export function createWebSocket(url: string, options?: WebSocketConfig): WebSocketController;

/**
 * 获取WebSocket连接
 */
export function getWebSocket(url: string): WebSocketController | null;

/**
 * 关闭所有WebSocket连接
 */
export function closeAllWebSockets(): void;

/**
 * 构建WebSocket URL
 */
export function buildWebSocketUrl(path: string): string;

declare const _default: {
  createWebSocket: typeof createWebSocket;
  getWebSocket: typeof getWebSocket;
  closeAllWebSockets: typeof closeAllWebSockets;
  buildWebSocketUrl: typeof buildWebSocketUrl;
};

export default _default;
