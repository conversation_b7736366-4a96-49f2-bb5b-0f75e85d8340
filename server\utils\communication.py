"""
统一通信模块

提供ArkReplay组件间的统一通信接口，支持WebSocket和HTTP通信方式，
实现消息确认、自动重连和状态同步等功能。
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Callable, Optional, List, Union

import httpx
import websockets
from django.conf import settings
from asgiref.sync import async_to_sync

logger = logging.getLogger(__name__)


class AgentCommunicator:
    """统一的Agent通信接口"""
    
    def __init__(self, agent_url: str, use_websocket: bool = True):
        """
        初始化通信器
        
        Args:
            agent_url: Agent服务的URL
            use_websocket: 是否使用WebSocket通信
        """
        self.agent_url = agent_url
        self.use_websocket = use_websocket
        self.ws_connection = None
        self.message_queue = asyncio.Queue()
        self.message_handlers = {}
        self.pending_responses = {}
        self.is_connected = False
        self.reconnect_task = None
        self.connection_lock = asyncio.Lock()
        
    async def connect(self):
        """建立连接"""
        async with self.connection_lock:
            if self.is_connected:
                return
                
            if self.use_websocket:
                try:
                    ws_url = f"ws://{self.agent_url}/ws"
                    logger.info(f"正在连接WebSocket: {ws_url}")
                    self.ws_connection = await websockets.connect(ws_url)
                    self.is_connected = True
                    
                    # 启动消息监听器
                    asyncio.create_task(self._message_listener())
                    logger.info(f"WebSocket连接成功: {ws_url}")
                except Exception as e:
                    logger.error(f"WebSocket连接失败: {str(e)}")
                    self.is_connected = False
                    self.use_websocket = False  # 回退到HTTP
                    
                    # 启动重连任务
                    if not self.reconnect_task:
                        self.reconnect_task = asyncio.create_task(self._reconnect())
        
    async def send_message(self, message_type: str, payload: Dict[str, Any]) -> str:
        """
        发送消息
        
        Args:
            message_type: 消息类型
            payload: 消息内容
            
        Returns:
            消息ID或响应内容
        """
        # 确保已连接
        if not self.is_connected:
            await self.connect()
            
        message = {
            "type": message_type,
            "payload": payload,
            "message_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat()
        }
        
        if self.use_websocket and self.ws_connection and self.is_connected:
            try:
                # 创建Future用于等待响应
                response_future = asyncio.Future()
                self.pending_responses[message["message_id"]] = response_future
                
                # 发送消息
                await self.ws_connection.send(json.dumps(message))
                logger.debug(f"通过WebSocket发送消息: {message_type}, ID: {message['message_id']}")
                
                # 等待响应，设置超时
                try:
                    response = await asyncio.wait_for(response_future, timeout=30.0)
                    return response
                except asyncio.TimeoutError:
                    logger.warning(f"消息响应超时: {message_type}, ID: {message['message_id']}")
                    self.pending_responses.pop(message["message_id"], None)
                    
                    # 回退到HTTP请求
                    return await self._send_http_message(message_type, payload)
            except Exception as e:
                logger.error(f"WebSocket发送消息失败: {str(e)}")
                self.is_connected = False
                
                # 回退到HTTP请求
                return await self._send_http_message(message_type, payload)
        else:
            # 使用HTTP请求
            return await self._send_http_message(message_type, payload)
    
    async def _send_http_message(self, message_type: str, payload: Dict[str, Any]) -> Any:
        """通过HTTP发送消息"""
        try:
            logger.debug(f"通过HTTP发送消息: {message_type}")
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"http://{self.agent_url}/api/{message_type}",
                    json=payload
                )
                return response.json()
        except Exception as e:
            logger.error(f"HTTP发送消息失败: {str(e)}")
            return {"success": False, "message": f"通信失败: {str(e)}"}
    
    async def _message_listener(self):
        """WebSocket消息监听器"""
        while self.is_connected and self.ws_connection:
            try:
                message = await self.ws_connection.recv()
                data = json.loads(message)
                
                # 处理响应消息
                if "response_to" in data:
                    message_id = data["response_to"]
                    if message_id in self.pending_responses:
                        future = self.pending_responses.pop(message_id)
                        future.set_result(data["payload"])
                        continue
                
                # 处理事件消息
                if data["type"] in self.message_handlers:
                    try:
                        await self.message_handlers[data["type"]](data["payload"])
                    except Exception as e:
                        logger.error(f"消息处理器异常: {str(e)}")
                else:
                    # 放入队列
                    await self.message_queue.put(data)
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket连接已关闭")
                self.is_connected = False
                
                # 启动重连任务
                if not self.reconnect_task or self.reconnect_task.done():
                    self.reconnect_task = asyncio.create_task(self._reconnect())
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {str(e)}")
                # 继续监听，不中断循环
    
    async def _reconnect(self):
        """重连逻辑"""
        retry_count = 0
        max_retries = 5
        
        while retry_count < max_retries and not self.is_connected:
            try:
                await asyncio.sleep(2 ** retry_count)  # 指数退避
                logger.info(f"尝试WebSocket重连 ({retry_count+1}/{max_retries})")
                await self.connect()
                
                if self.is_connected:
                    logger.info("WebSocket重连成功")
                    return
            except Exception as e:
                logger.error(f"WebSocket重连失败: {str(e)}")
                retry_count += 1
                
        if not self.is_connected:
            logger.error("WebSocket重连失败，切换到HTTP模式")
            self.use_websocket = False
    
    def register_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
    
    async def close(self):
        """关闭连接"""
        if self.ws_connection:
            await self.ws_connection.close()
            self.is_connected = False
            logger.info("WebSocket连接已关闭")


# 同步包装器，用于在Django视图中使用
class SyncAgentCommunicator:
    """同步Agent通信接口包装器"""
    
    def __init__(self, agent_url: str, use_websocket: bool = True):
        self.async_communicator = AgentCommunicator(agent_url, use_websocket)
    
    def connect(self):
        """建立连接"""
        async_to_sync(self.async_communicator.connect)()
    
    def send_message(self, message_type: str, payload: Dict[str, Any]) -> Any:
        """发送消息"""
        return async_to_sync(self.async_communicator.send_message)(message_type, payload)
    
    def register_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        async_handler = lambda payload: asyncio.create_task(
            asyncio.to_thread(handler, payload)
        )
        self.async_communicator.register_handler(message_type, async_handler)
    
    def close(self):
        """关闭连接"""
        async_to_sync(self.async_communicator.close)()


# 全局通信器实例
_db_agent_communicator = None

def get_db_agent_communicator() -> SyncAgentCommunicator:
    """获取DB-Agent通信器实例"""
    global _db_agent_communicator
    
    if _db_agent_communicator is None:
        agent_url = getattr(settings, 'DB_AGENT_URL', 'localhost:8000')
        use_websocket = getattr(settings, 'DB_AGENT_USE_WEBSOCKET', True)
        _db_agent_communicator = SyncAgentCommunicator(agent_url, use_websocket)
        _db_agent_communicator.connect()
        
    return _db_agent_communicator
