/* Monaco Editor styles */
.monaco-editor-container {
  width: 100%;
  height: 300px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
}

/* 适配深色模式 */
html.dark-mode {
  .monaco-editor-container {
    border-color: var(--el-border-color-darker);
  }
}

/* 编辑器字体调整 */
.monaco-editor {
  .monaco-editor-background,
  .margin-view-overlays {
    font-family: 'JetBrains Mono', Consolas, 'Courier New', monospace;
  }
}

/* 确保没有重复边框和多余圆角 */
.monaco-editor, .monaco-diff-editor {
  border-radius: 0 !important;
  
  .overflow-guard {
    width: 100% !important;
    height: 100% !important;
  }
}

/* 防止边框重叠情况 */
.monaco-editor-container .monaco-editor {
  border: none !important;
}

/* 确保内容区域完全填充 */
.monaco-editor-content, 
.monaco-editor .overflow-guard,
.monaco-diff-editor .overflow-guard {
  width: 100% !important;
  height: 100% !important;
}

/* SQL语法高亮自定义样式 */
.mtk1 { color: var(--el-text-color-primary); } /* 默认文本 */
.mtk7 { color: #0000ff; } /* 关键词 */
.mtk8 { color: #098658; } /* 字符串 */
.mtk5 { color: #af00db; } /* 函数名 */
.mtk6 { color: #ee0000; } /* 运算符 */
.mtk4 { color: #795E26; } /* 数字 */
.mtk9 { color: #267f99; } /* 参数 */
.mtk10 { color: #008000; } /* 注释 */