<template>
  <div class="source-only-table">
    <div class="table-header">
      <span class="title">源独有记录 ({{ total }})</span>
      <div class="actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索记录..."
          style="width: 200px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading"
      :max-height="400"
    >
      <el-table-column prop="id" label="记录ID" width="120" />
      <el-table-column label="数据内容" min-width="300" show-overflow-tooltip>
        <template #default="scope">
          {{ formatData(scope.row.data) }}
        </template>
      </el-table-column>
      <el-table-column label="字段数量" width="100">
        <template #default="scope">
          <el-tag size="small">
            {{ getFieldCount(scope.row.data) }}个字段
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="主要字段" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ getMainFields(scope.row.data) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="源独有记录详情" width="800px">
      <div v-if="currentRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            {{ currentRecord.id }}
          </el-descriptions-item>
          <el-descriptions-item label="字段数量">
            {{ getFieldCount(currentRecord.data) }}个字段
          </el-descriptions-item>
          <el-descriptions-item label="数据大小" :span="2">
            {{ getDataSize(currentRecord.data) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <div class="data-section">
          <h4>完整数据</h4>
          <div class="data-grid">
            <div
              v-for="(value, key) in currentRecord.data"
              :key="key"
              class="data-item"
            >
              <div class="field-name">{{ key }}</div>
              <div class="field-value">{{ formatFieldValue(value) }}</div>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="json-section">
          <h4>JSON格式</h4>
          <pre class="json-content">{{ JSON.stringify(currentRecord.data, null, 2) }}</pre>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportRecord">导出记录</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { databaseApiClient, type SourceOnlyRecord } from '@/api/database';
import { handleDatabaseError, showErrorMessage } from '@/api/database/error';

interface Props {
  taskId: string;
  total: number;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const tableData = ref<SourceOnlyRecord[]>([]);
const currentPage = ref(1);
const pageSize = ref(20);
const searchKeyword = ref('');
const detailVisible = ref(false);
const currentRecord = ref<SourceOnlyRecord | null>(null);

// 加载数据
const loadData = async () => {
  if (!props.taskId) return;

  loading.value = true;
  try {
    const response = await databaseApiClient.getSourceOnlyRecords(
      props.taskId,
      currentPage.value,
      pageSize.value
    );
    tableData.value = response.results;
  } catch (error) {
    const dbError = handleDatabaseError(error);
    showErrorMessage(dbError);
  } finally {
    loading.value = false;
  }
};

// 格式化数据显示
const formatData = (data: Record<string, any>) => {
  if (!data) return '(空)';
  const keys = Object.keys(data);
  if (keys.length === 0) return '(空)';
  
  // 显示前3个字段的值
  const preview = keys.slice(0, 3).map(key => {
    const value = data[key];
    const displayValue = value !== null && value !== undefined ? String(value) : '(null)';
    return `${key}: ${displayValue.length > 20 ? displayValue.substring(0, 20) + '...' : displayValue}`;
  }).join(', ');
  
  return keys.length > 3 ? `${preview}...` : preview;
};

// 获取字段数量
const getFieldCount = (data: Record<string, any>) => {
  return data ? Object.keys(data).length : 0;
};

// 获取主要字段
const getMainFields = (data: Record<string, any>) => {
  if (!data) return '(无)';
  const keys = Object.keys(data);
  return keys.slice(0, 5).join(', ') + (keys.length > 5 ? '...' : '');
};

// 获取数据大小
const getDataSize = (data: Record<string, any>) => {
  const jsonStr = JSON.stringify(data);
  const bytes = new Blob([jsonStr]).size;
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

// 格式化字段值
const formatFieldValue = (value: any) => {
  if (value === null) return '(null)';
  if (value === undefined) return '(undefined)';
  if (typeof value === 'string' && value.length > 100) {
    return value.substring(0, 100) + '...';
  }
  return String(value);
};

// 查看详情
const viewDetail = (record: SourceOnlyRecord) => {
  currentRecord.value = record;
  detailVisible.value = true;
};

// 导出记录
const exportRecord = () => {
  if (!currentRecord.value) return;
  
  const dataStr = JSON.stringify(currentRecord.value.data, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `source_record_${currentRecord.value.id}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
  
  ElMessage.success('记录导出成功');
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 分页处理
const handleSizeChange = () => {
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = () => {
  loadData();
};

// 监听任务ID变化
watch(() => props.taskId, () => {
  if (props.taskId) {
    currentPage.value = 1;
    loadData();
  }
});

// 组件挂载时加载数据
onMounted(() => {
  if (props.taskId) {
    loadData();
  }
});
</script>

<style scoped>
.source-only-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-weight: 600;
  font-size: 16px;
  color: #409eff;
}

.actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.data-section h4,
.json-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.data-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.field-name {
  font-weight: 600;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.field-value {
  font-size: 13px;
  color: #303133;
  word-break: break-all;
}

.json-content {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
