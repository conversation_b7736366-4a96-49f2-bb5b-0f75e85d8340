/**
 * HTTP 请求工具
 * 基于 fetch API 的简单封装
 */

// 请求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
  responseType?: 'json' | 'blob' | 'text'
}

// 响应接口
interface ApiResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
}

class RequestError extends Error {
  status?: number
  code?: string

  constructor(message: string, status?: number, code?: string) {
    super(message)
    this.name = 'RequestError'
    this.status = status
    this.code = code
  }
}

class HttpClient {
  private baseURL: string = ''
  private defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  }
  private defaultTimeout: number = 10000

  /**
   * 构建完整URL
   */
  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`

    if (!params || Object.keys(params).length === 0) {
      return fullURL
    }

    const urlObj = new URL(fullURL, window.location.origin)
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, String(value))
      }
    })

    return urlObj.toString()
  }

  /**
   * 处理响应
   */
  private async handleResponse(response: Response, responseType: string = 'json'): Promise<any> {
    if (!response.ok) {
      let errorMessage = `HTTP Error: ${response.status} ${response.statusText}`

      try {
        const errorData = await response.json()
        errorMessage = errorData.message || errorData.error || errorMessage
      } catch {
        // 如果无法解析错误响应，使用默认错误消息
      }

      throw new RequestError(errorMessage, response.status)
    }

    // 构建响应头对象
    const headers: Record<string, string> = {}
    response.headers.forEach((value, key) => {
      headers[key] = value
    })

    let data: any
    switch (responseType) {
      case 'blob':
        data = await response.blob()
        break
      case 'text':
        data = await response.text()
        break
      case 'json':
      default:
        try {
          data = await response.json()
        } catch {
          data = await response.text()
        }
        break
    }

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config: Omit<RequestConfig, 'method' | 'data'> = {}): Promise<ApiResponse<T>> {
    const { params, headers = {}, timeout = this.defaultTimeout, responseType = 'json' } = config

    const fullURL = this.buildURL(url, params)
    const requestHeaders = { ...this.defaultHeaders, ...headers }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(fullURL, {
        method: 'GET',
        headers: requestHeaders,
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      return await this.handleResponse(response, responseType)
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof RequestError) {
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new RequestError('请求超时', 408, 'TIMEOUT')
        }
        throw new RequestError(error.message, 0, 'NETWORK_ERROR')
      }

      throw new RequestError('未知错误', 0, 'UNKNOWN_ERROR')
    }
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config: Omit<RequestConfig, 'method' | 'data'> = {}): Promise<ApiResponse<T>> {
    const { params, headers = {}, timeout = this.defaultTimeout, responseType = 'json' } = config

    const fullURL = this.buildURL(url, params)
    const requestHeaders = { ...this.defaultHeaders, ...headers }

    const fetchConfig: RequestInit = {
      method: 'POST',
      headers: requestHeaders,
    }

    // 添加请求体
    if (data) {
      if (data instanceof FormData) {
        fetchConfig.body = data
        // 删除 Content-Type，让浏览器自动设置
        delete requestHeaders['Content-Type']
      } else if (typeof data === 'object') {
        fetchConfig.body = JSON.stringify(data)
      } else {
        fetchConfig.body = data
      }
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    fetchConfig.signal = controller.signal

    try {
      const response = await fetch(fullURL, fetchConfig)
      clearTimeout(timeoutId)
      return await this.handleResponse(response, responseType)
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof RequestError) {
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new RequestError('请求超时', 408, 'TIMEOUT')
        }
        throw new RequestError(error.message, 0, 'NETWORK_ERROR')
      }

      throw new RequestError('未知错误', 0, 'UNKNOWN_ERROR')
    }
  }
}

// 创建默认实例
const request = new HttpClient()

export { request }
export type { RequestConfig, ApiResponse }