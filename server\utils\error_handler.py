"""
错误处理工具模块

提供统一的错误处理机制，包括错误分类、错误记录、错误通知等功能。
"""
import logging
import traceback
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Union, Tuple
from django.utils import timezone
try:
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
except ImportError:
    # 如果无法导入，创建简单的替代函数
    def get_channel_layer():
        return None

    def async_to_sync(func):
        def wrapper(*args, **kwargs):
            return None
        return wrapper
from django.conf import settings

# 导入统一的错误常量
from .error_constants import (
    ErrorCategory,
    ErrorSeverity,
    ErrorType,
    categorize_error as const_categorize_error,
    determine_severity as const_determine_severity,
    is_fatal_error as const_is_fatal_error
)

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()

def categorize_error(error: Exception) -> ErrorCategory:
    """
    根据异常类型分类错误

    Args:
        error: 异常对象

    Returns:
        错误类别
    """
    # 使用统一的错误分类函数
    return const_categorize_error(error)

def determine_severity(error: Exception, category: Optional[ErrorCategory] = None) -> ErrorSeverity:
    """
    确定错误的严重程度

    Args:
        error: 异常对象
        category: 错误类别，如果为None则自动判断

    Returns:
        错误严重程度
    """
    # 使用统一的错误严重程度判断函数
    return const_determine_severity(error, category)

def is_fatal_error(error: Exception, category: Optional[ErrorCategory] = None,
                  severity: Optional[ErrorSeverity] = None) -> bool:
    """
    判断错误是否为致命错误

    Args:
        error: 异常对象
        category: 错误类别，如果为None则自动判断
        severity: 错误严重程度，如果为None则自动判断

    Returns:
        是否为致命错误
    """
    # 使用统一的致命错误判断函数
    return const_is_fatal_error(error, category, severity)

def format_error(error: Exception, task_id: Optional[str] = None,
                context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    格式化错误信息，生成标准错误报告

    Args:
        error: 异常对象
        task_id: 相关任务ID
        context: 错误上下文信息

    Returns:
        格式化的错误报告
    """
    # 获取错误类别和严重程度
    category = categorize_error(error)
    severity = determine_severity(error, category)

    # 判断是否为致命错误
    fatal = is_fatal_error(error, category, severity)

    # 构建错误报告
    error_report = {
        "timestamp": timezone.now().isoformat(),
        "type": type(error).__name__,
        "message": str(error),
        "traceback": traceback.format_exc(),
        "category": str(category.value),
        "severity": str(severity.value),
        "is_fatal": fatal,
        "task_id": task_id,
        "context": context or {}
    }

    return error_report

def notify_error(error_report: Dict[str, Any], task_id: Optional[str] = None) -> bool:
    """
    通过WebSocket通知错误

    Args:
        error_report: 错误报告
        task_id: 任务ID

    Returns:
        是否成功发送通知
    """
    try:
        # 如果没有channel_layer，只记录日志
        if channel_layer is None:
            logger.warning("无法发送错误通知: channel_layer未初始化")
            logger.error(f"错误报告: {error_report}")
            return False

        # 如果没有提供task_id，从错误报告中获取
        if task_id is None:
            task_id = error_report.get("task_id")

        # 如果有task_id，发送到特定任务的组
        if task_id:
            try:
                task_group_name = f'task_{task_id}'
                async_to_sync(channel_layer.group_send)(
                    task_group_name,
                    {
                        'type': 'task_status',
                        'content': {
                            'type': 'error',
                            'error': error_report
                        }
                    }
                )
            except Exception as e:
                logger.error(f"发送错误通知到任务组失败: {str(e)}")

        # 发送到全局通知组
        try:
            async_to_sync(channel_layer.group_send)(
                'task_notifications',
                {
                    'type': 'task_notification',
                    'content': {
                        'type': 'error',
                        'error': error_report,
                        'task_id': task_id,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )
        except Exception as e:
            logger.error(f"发送错误通知到全局通知组失败: {str(e)}")

        return True
    except Exception as e:
        logger.error(f"发送错误通知失败: {str(e)}")
        return False
