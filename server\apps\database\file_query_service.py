"""
基于文件的查询服务模块

实现高性能的文件查询机制，包括：
- 4级查询策略：缓存 → 索引 → 扫描 → 数据库备选
- 多维度过滤和分页查询
- 智能缓存管理
- 大数据量优化处理
"""

import json
import asyncio
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from django.conf import settings
from django.core.cache import cache
import redis

from apps.database.index_manager import IndexManager
from apps.database.models import CompareTask
from apps.database.services import DiffRetrievalService

logger = logging.getLogger(__name__)

@dataclass
class QueryParams:
    """查询参数"""
    page: int = 1
    size: int = 20
    diff_type: Optional[str] = None  # source_only, target_only, differences
    keyword: Optional[str] = None
    field_name: Optional[str] = None
    sort_by: Optional[str] = None
    sort_order: str = "asc"  # asc, desc

@dataclass
class QueryResult:
    """查询结果"""
    data: List[Dict[str, Any]]
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool
    query_time_ms: float
    cache_hit: bool = False
    data_source: str = "unknown"  # cache, index, scan, database

class FileBasedQueryService:
    """基于文件的查询服务"""

    def __init__(self):
        """初始化查询服务"""
        self.index_manager = IndexManager()
        self.diff_retrieval_service = DiffRetrievalService()
        self.redis_client = self._get_redis_client()
        self.cache_ttl = 300  # 5分钟缓存TTL
        self.comparison_results_path = Path(getattr(settings, 'COMPARISON_RESULTS_PATH', 'comparison_results'))

    def _get_redis_client(self) -> redis.Redis:
        """获取Redis客户端"""
        try:
            redis_config = getattr(settings, 'REDIS_CONFIG', {
                'host': 'localhost',
                'port': 6379,
                'db': 0
            })
            return redis.Redis(**redis_config, decode_responses=True)
        except Exception as e:
            logger.warning(f"Redis连接失败，将使用Django缓存: {str(e)}")
            return None

    async def query_task_differences(self, task_id: str, query_params: QueryParams) -> QueryResult:
        """
        查询任务差异记录

        Args:
            task_id: 任务ID
            query_params: 查询参数

        Returns:
            QueryResult: 查询结果
        """
        start_time = datetime.now()

        try:
            logger.info(f"开始查询任务差异 - 任务ID: {task_id}, 参数: {query_params}")

            # Level 1: 内存缓存查询
            cache_result = await self._query_from_cache(task_id, query_params)
            if cache_result:
                query_time = (datetime.now() - start_time).total_seconds() * 1000
                cache_result.query_time_ms = query_time
                cache_result.cache_hit = True
                cache_result.data_source = "cache"
                logger.debug(f"缓存命中 - 任务ID: {task_id}, 查询时间: {query_time:.2f}ms")
                return cache_result

            # Level 2: 文件索引查询
            if await self._has_indexes(task_id):
                index_result = await self._query_with_index(task_id, query_params)
                if index_result:
                    # 更新缓存
                    await self._update_cache(task_id, query_params, index_result)
                    query_time = (datetime.now() - start_time).total_seconds() * 1000
                    index_result.query_time_ms = query_time
                    index_result.data_source = "index"
                    logger.debug(f"索引查询成功 - 任务ID: {task_id}, 查询时间: {query_time:.2f}ms")
                    return index_result

            # Level 3: 文件扫描查询
            scan_result = await self._query_with_scan(task_id, query_params)
            if scan_result:
                # 异步构建索引以加速后续查询
                asyncio.create_task(self._build_indexes_async(task_id))

                # 更新缓存
                await self._update_cache(task_id, query_params, scan_result)
                query_time = (datetime.now() - start_time).total_seconds() * 1000
                scan_result.query_time_ms = query_time
                scan_result.data_source = "scan"
                logger.debug(f"文件扫描成功 - 任务ID: {task_id}, 查询时间: {query_time:.2f}ms")
                return scan_result

            # Level 4: 数据库备选查询
            logger.info(f"回退到数据库查询 - 任务ID: {task_id}")
            db_result = await self._query_from_database(task_id, query_params)
            query_time = (datetime.now() - start_time).total_seconds() * 1000
            db_result.query_time_ms = query_time
            db_result.data_source = "database"
            return db_result

        except Exception as e:
            logger.error(f"查询任务差异失败 - 任务ID: {task_id}, 错误: {str(e)}")
            raise

    async def _query_from_cache(self, task_id: str, query_params: QueryParams) -> Optional[QueryResult]:
        """从缓存查询"""
        try:
            cache_key = self._generate_cache_key(task_id, query_params)

            if self.redis_client:
                # 使用Redis缓存
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    result_data = json.loads(cached_data)
                    return QueryResult(**result_data)
            else:
                # 使用Django缓存
                cached_data = cache.get(cache_key)
                if cached_data:
                    return QueryResult(**cached_data)

            return None

        except Exception as e:
            logger.warning(f"缓存查询失败: {str(e)}")
            return None

    def _generate_cache_key(self, task_id: str, query_params: QueryParams) -> str:
        """生成缓存键"""
        params_str = f"{query_params.page}_{query_params.size}_{query_params.diff_type}_{query_params.keyword}_{query_params.field_name}_{query_params.sort_by}_{query_params.sort_order}"
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        return f"task_diff:{task_id}:{params_hash}"

    async def _has_indexes(self, task_id: str) -> bool:
        """检查是否有索引文件"""
        try:
            workspace_path = self.comparison_results_path / task_id
            indexes_dir = workspace_path / 'indexes'

            if not indexes_dir.exists():
                return False

            # 检查关键索引文件是否存在
            key_index_file = indexes_dir / 'key_index.json'
            type_index_file = indexes_dir / 'type_index.json'

            return key_index_file.exists() and type_index_file.exists()

        except Exception as e:
            logger.warning(f"检查索引文件失败: {str(e)}")
            return False

    async def _query_with_index(self, task_id: str, query_params: QueryParams) -> Optional[QueryResult]:
        """使用索引进行查询"""
        try:
            # 加载索引
            indexes = await self.index_manager.load_indexes(task_id)
            if not indexes:
                return None

            # 应用过滤条件
            filtered_keys = await self._apply_filters_with_index(indexes, query_params)

            # 分页处理
            result = await self._paginate_indexed_results(task_id, filtered_keys, query_params)

            return result

        except Exception as e:
            logger.error(f"索引查询失败: {str(e)}")
            return None

    async def _apply_filters_with_index(self, indexes: Dict[str, Any],
                                       query_params: QueryParams) -> List[str]:
        """使用索引应用过滤条件"""
        filtered_keys = set()

        # 差异类型过滤
        if query_params.diff_type:
            type_keys = indexes.get('type_index', {}).get(query_params.diff_type, [])
            filtered_keys.update(type_keys)
        else:
            # 如果没有指定类型，获取所有键
            for type_keys in indexes.get('type_index', {}).values():
                filtered_keys.update(type_keys)

        # 关键字过滤
        if query_params.keyword:
            keyword_keys = await self._search_by_keyword_with_index(
                indexes, query_params.keyword
            )
            if filtered_keys:
                filtered_keys &= set(keyword_keys)
            else:
                filtered_keys = set(keyword_keys)

        # 字段名过滤
        if query_params.field_name:
            field_keys = indexes.get('field_index', {}).get(query_params.field_name, [])
            if filtered_keys:
                filtered_keys &= set(field_keys)
            else:
                filtered_keys = set(field_keys)

        return list(filtered_keys)

    async def _search_by_keyword_with_index(self, indexes: Dict[str, Any],
                                           keyword: str) -> List[str]:
        """使用索引进行关键字搜索"""
        matching_keys = []

        # 在KEY索引中搜索
        key_index = indexes.get('key_index', {})
        for key, key_info in key_index.items():
            if keyword.lower() in key.lower():
                matching_keys.append(key)

        return matching_keys

    async def _paginate_indexed_results(self, task_id: str, filtered_keys: List[str],
                                       query_params: QueryParams) -> QueryResult:
        """对索引结果进行分页"""
        try:
            total = len(filtered_keys)

            # 排序
            if query_params.sort_by == "key":
                filtered_keys.sort(reverse=(query_params.sort_order == "desc"))

            # 分页计算
            start_idx = (query_params.page - 1) * query_params.size
            end_idx = start_idx + query_params.size
            page_keys = filtered_keys[start_idx:end_idx]

            # 获取详细数据
            data = await self._fetch_records_by_keys(task_id, page_keys)

            pages = (total + query_params.size - 1) // query_params.size

            return QueryResult(
                data=data,
                total=total,
                page=query_params.page,
                size=query_params.size,
                pages=pages,
                has_next=query_params.page < pages,
                has_prev=query_params.page > 1,
                query_time_ms=0  # 将在调用处设置
            )

        except Exception as e:
            logger.error(f"索引结果分页失败: {str(e)}")
            raise

    async def _fetch_records_by_keys(self, task_id: str, keys: List[str]) -> List[Dict[str, Any]]:
        """根据键值获取记录详情"""
        try:
            workspace_path = self.comparison_results_path / task_id
            data_dir = workspace_path / 'data'

            records = []

            # 加载索引以获取记录位置信息
            indexes = await self.index_manager.load_indexes(task_id)
            key_index = indexes.get('key_index', {})

            # 按文件分组键值
            file_keys = {}
            for key in keys:
                key_info = key_index.get(key, {})
                file_type = key_info.get('file', 'unknown')
                if file_type not in file_keys:
                    file_keys[file_type] = []
                file_keys[file_type].append((key, key_info))

            # 从各文件读取记录
            for file_type, key_list in file_keys.items():
                file_path = data_dir / f"{file_type}.jsonl"
                if file_path.exists():
                    file_records = await self._read_records_from_file(file_path, key_list)
                    records.extend(file_records)

            return records

        except Exception as e:
            logger.error(f"获取记录详情失败: {str(e)}")
            return []

    async def _read_records_from_file(self, file_path: Path,
                                     key_list: List[Tuple[str, Dict]]) -> List[Dict[str, Any]]:
        """从文件读取指定记录"""
        records = []

        try:
            # 如果有行号信息，使用精确定位
            line_positions = {key: info.get('line', 0) for key, info in key_list}

            with open(file_path, 'r', encoding='utf-8') as f:
                current_line = 1
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            record = json.loads(line)
                            record_key = record.get('key', '')

                            # 检查是否是需要的记录
                            if record_key in [key for key, _ in key_list]:
                                records.append(record)

                        except json.JSONDecodeError:
                            continue

                    current_line += 1

                    # 如果已找到所有记录，提前退出
                    if len(records) >= len(key_list):
                        break

        except Exception as e:
            logger.error(f"读取文件记录失败: {str(e)}")

        return records

    async def _query_with_scan(self, task_id: str, query_params: QueryParams) -> Optional[QueryResult]:
        """文件扫描查询"""
        try:
            workspace_path = self.comparison_results_path / task_id
            data_dir = workspace_path / 'data'

            if not data_dir.exists():
                return None

            # 确定要扫描的文件
            files_to_scan = []
            if query_params.diff_type:
                file_path = data_dir / f"{query_params.diff_type}.jsonl"
                if file_path.exists():
                    files_to_scan.append((query_params.diff_type, file_path))
            else:
                # 扫描所有差异文件
                for file_type in ['source_only', 'target_only', 'differences']:
                    file_path = data_dir / f"{file_type}.jsonl"
                    if file_path.exists():
                        files_to_scan.append((file_type, file_path))

            # 扫描文件并收集匹配记录
            all_records = []
            for file_type, file_path in files_to_scan:
                records = await self._scan_file_for_records(file_path, query_params)
                all_records.extend(records)

            # 排序
            if query_params.sort_by == "key":
                all_records.sort(
                    key=lambda x: x.get('key', ''),
                    reverse=(query_params.sort_order == "desc")
                )

            # 分页
            total = len(all_records)
            start_idx = (query_params.page - 1) * query_params.size
            end_idx = start_idx + query_params.size
            page_records = all_records[start_idx:end_idx]

            pages = (total + query_params.size - 1) // query_params.size

            return QueryResult(
                data=page_records,
                total=total,
                page=query_params.page,
                size=query_params.size,
                pages=pages,
                has_next=query_params.page < pages,
                has_prev=query_params.page > 1,
                query_time_ms=0
            )

        except Exception as e:
            logger.error(f"文件扫描查询失败: {str(e)}")
            return None

    async def _scan_file_for_records(self, file_path: Path,
                                    query_params: QueryParams) -> List[Dict[str, Any]]:
        """扫描文件查找匹配记录"""
        matching_records = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            record = json.loads(line)

                            # 应用过滤条件
                            if self._record_matches_filters(record, query_params):
                                matching_records.append(record)

                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            logger.error(f"扫描文件失败: {str(e)}")

        return matching_records

    def _record_matches_filters(self, record: Dict[str, Any],
                               query_params: QueryParams) -> bool:
        """检查记录是否匹配过滤条件"""
        # 关键字过滤
        if query_params.keyword:
            keyword_lower = query_params.keyword.lower()

            # 在记录的所有字符串值中搜索关键字
            for value in record.values():
                if isinstance(value, str) and keyword_lower in value.lower():
                    break
                elif isinstance(value, dict):
                    # 如果值是字典，递归搜索
                    for sub_value in value.values():
                        if isinstance(sub_value, str) and keyword_lower in sub_value.lower():
                            break
                    else:
                        continue
                    break
            else:
                return False

        # 字段名过滤
        if query_params.field_name:
            # 检查差异字段中是否包含指定字段
            diff_fields = record.get('diff_fields', [])
            if query_params.field_name not in diff_fields:
                # 如果不是差异记录，检查记录本身是否包含该字段
                if query_params.field_name not in record:
                    return False

        return True

    async def _query_from_database(self, task_id: str,
                                  query_params: QueryParams) -> QueryResult:
        """从数据库查询（备选方案）"""
        try:
            logger.info(f"使用数据库备选查询 - 任务ID: {task_id}")

            # 使用现有的DiffRetrievalService
            db_result = await self.diff_retrieval_service.get_task_differences(
                task_id,
                page=query_params.page,
                size=query_params.size,
                diff_type=query_params.diff_type,
                keyword=query_params.keyword
            )

            # 转换为统一的QueryResult格式
            return QueryResult(
                data=db_result.get('data', []),
                total=db_result.get('total', 0),
                page=query_params.page,
                size=query_params.size,
                pages=db_result.get('pages', 0),
                has_next=db_result.get('has_next', False),
                has_prev=db_result.get('has_prev', False),
                query_time_ms=0
            )

        except Exception as e:
            logger.error(f"数据库查询失败: {str(e)}")
            # 返回空结果
            return QueryResult(
                data=[],
                total=0,
                page=query_params.page,
                size=query_params.size,
                pages=0,
                has_next=False,
                has_prev=False,
                query_time_ms=0
            )

    async def _update_cache(self, task_id: str, query_params: QueryParams,
                           result: QueryResult):
        """更新缓存"""
        try:
            cache_key = self._generate_cache_key(task_id, query_params)

            # 准备缓存数据
            cache_data = {
                'data': result.data,
                'total': result.total,
                'page': result.page,
                'size': result.size,
                'pages': result.pages,
                'has_next': result.has_next,
                'has_prev': result.has_prev,
                'query_time_ms': result.query_time_ms,
                'cache_hit': False,
                'data_source': result.data_source
            }

            if self.redis_client:
                # 使用Redis缓存
                self.redis_client.setex(
                    cache_key,
                    self.cache_ttl,
                    json.dumps(cache_data, ensure_ascii=False)
                )
            else:
                # 使用Django缓存
                cache.set(cache_key, cache_data, self.cache_ttl)

            logger.debug(f"缓存已更新 - 键: {cache_key}")

        except Exception as e:
            logger.warning(f"更新缓存失败: {str(e)}")

    async def _build_indexes_async(self, task_id: str):
        """异步构建索引"""
        try:
            logger.info(f"开始异步构建索引 - 任务ID: {task_id}")
            await self.index_manager.build_indexes(task_id)
            logger.info(f"索引构建完成 - 任务ID: {task_id}")
        except Exception as e:
            logger.error(f"异步构建索引失败 - 任务ID: {task_id}, 错误: {str(e)}")

    async def preview_file(self, task_id: str, file_type: str,
                          page: int = 1, size: int = 50) -> Dict[str, Any]:
        """
        预览文件内容

        Args:
            task_id: 任务ID
            file_type: 文件类型
            page: 页码
            size: 每页大小

        Returns:
            Dict: 预览结果
        """
        try:
            workspace_path = self.comparison_results_path / task_id

            if file_type == 'summary':
                # 预览摘要文件
                file_path = workspace_path / 'data' / 'summary.json'
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = json.load(f)
                        return {
                            'data': [content],
                            'total': 1,
                            'page': 1,
                            'size': 1,
                            'pages': 1,
                            'file_type': file_type
                        }
            else:
                # 预览JSONL文件
                file_path = workspace_path / 'data' / f"{file_type}.jsonl"
                if file_path.exists():
                    return await self._preview_jsonl_file(file_path, page, size, file_type)

            return {
                'data': [],
                'total': 0,
                'page': page,
                'size': size,
                'pages': 0,
                'file_type': file_type,
                'error': '文件不存在'
            }

        except Exception as e:
            logger.error(f"预览文件失败 - 任务ID: {task_id}, 文件类型: {file_type}, 错误: {str(e)}")
            return {
                'data': [],
                'total': 0,
                'page': page,
                'size': size,
                'pages': 0,
                'file_type': file_type,
                'error': str(e)
            }

    async def _preview_jsonl_file(self, file_path: Path, page: int, size: int,
                                 file_type: str) -> Dict[str, Any]:
        """预览JSONL文件"""
        try:
            # 首先计算总行数
            total_lines = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                for _ in f:
                    total_lines += 1

            # 计算分页
            start_line = (page - 1) * size
            end_line = start_line + size

            # 读取指定页的数据
            current_line = 0
            data = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if current_line >= start_line and current_line < end_line:
                        line = line.strip()
                        if line:
                            try:
                                record = json.loads(line)
                                data.append(record)
                            except json.JSONDecodeError:
                                continue

                    current_line += 1

                    # 如果已读取足够的行，退出
                    if current_line >= end_line:
                        break

            pages = (total_lines + size - 1) // size

            return {
                'data': data,
                'total': total_lines,
                'page': page,
                'size': size,
                'pages': pages,
                'file_type': file_type
            }

        except Exception as e:
            logger.error(f"预览JSONL文件失败: {str(e)}")
            raise