from rest_framework import serializers
from .models import CompareModel, TableConfig, CompareTask, CompareDiff, DataConnection
import uuid


class TableConfigSerializer(serializers.ModelSerializer):
    """表配置序列化器"""
    table_id = serializers.Char<PERSON>ield(allow_blank=True, required=False)
    remark = serializers.CharField(allow_blank=True, required=False)
    sql_1 = serializers.CharField(required=False, allow_blank=True)
    sql_2 = serializers.CharField(required=False, allow_blank=True)
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()

    class Meta:
        model = TableConfig
        fields = ['table_id', 'remark', 'sql_1', 'sql_2', 'create_time', 'update_time']

    def get_create_time(self, obj):
        """返回前端期望的create_time字段格式"""
        return obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else None

    def get_update_time(self, obj):
        """返回前端期望的update_time字段格式"""
        return obj.update_time.strftime('%Y-%m-%d %H:%M:%S') if obj.update_time else None

    def to_internal_value(self, data):
        """处理前端传来的字段映射"""

        # 确保table_id不为None
        if 'table_id' not in data or data['table_id'] is None:
            # 使用uuid生成唯一ID
            data['table_id'] = str(uuid.uuid4())

        return super().to_internal_value(data)


class CompareModelSerializer(serializers.ModelSerializer):
    """比对模型序列化器"""
    tables = TableConfigSerializer(many=True, read_only=False, required=False)
    source_connection = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    target_connection = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()
    source_type = serializers.SerializerMethodField()
    target_type = serializers.SerializerMethodField()

    class Meta:
        model = CompareModel
        fields = ['id', 'name', 'description', 'source_connection', 'target_connection',
                 'source_type', 'target_type', 'status', 'create_time', 'update_time', 'tables']
        read_only_fields = ['create_time', 'update_time', 'source_type', 'target_type']

    def get_create_time(self, obj):
        """返回前端期望的create_time字段格式"""
        return obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else None

    def get_update_time(self, obj):
        """返回前端期望的update_time字段格式"""
        return obj.update_time.strftime('%Y-%m-%d %H:%M:%S') if obj.update_time else None

    def get_source_type(self, obj):
        """获取源数据库类型"""
        if obj.source_connection:
            try:
                conn = DataConnection.objects.get(id=obj.source_connection)
                return conn.type
            except DataConnection.DoesNotExist:
                pass
        return None

    def get_target_type(self, obj):
        """获取目标数据库类型"""
        if obj.target_connection:
            try:
                conn = DataConnection.objects.get(id=obj.target_connection)
                return conn.type
            except DataConnection.DoesNotExist:
                pass
        return None

    def create(self, validated_data):
        tables_data = validated_data.pop('tables', [])
        model = CompareModel.objects.create(**validated_data)

        for table_data in tables_data:
            TableConfig.objects.create(model=model, **table_data)

        return model

    def update(self, instance, validated_data):
        tables_data = validated_data.pop('tables', None)

        # 更新模型基本信息
        for key, value in validated_data.items():
            setattr(instance, key, value)
        instance.save()

        # 如果提供了表配置数据，则更新表配置
        if tables_data is not None:
            # 删除原有表配置
            instance.tables.all().delete()

            # 创建新的表配置
            for table_data in tables_data:
                TableConfig.objects.create(model=instance, **table_data)

        return instance


class CompareTaskSerializer(serializers.ModelSerializer):
    """比对任务序列化器"""
    model_name = serializers.ReadOnlyField(source='model.name')
    duration = serializers.ReadOnlyField()

    # 添加连接信息字段
    source_connection = serializers.SerializerMethodField()
    target_connection = serializers.SerializerMethodField()
    source_connection_type = serializers.SerializerMethodField()
    target_connection_type = serializers.SerializerMethodField()

    class Meta:
        model = CompareTask
        fields = [
            'id', 'model', 'model_name', 'name', 'status', 'progress',
            'total_records', 'processed_records', 'matched_records',
            'different_records', 'error_records', 'start_time',
            'end_time', 'duration', 'message', 'create_time',
            'source_connection', 'target_connection',
            'source_connection_type', 'target_connection_type'
        ]
        read_only_fields = ['progress', 'total_records', 'processed_records', 'matched_records', 'different_records', 'error_records']

    def get_source_connection(self, obj):
        """获取源连接信息"""
        if obj.model and obj.model.source_connection:
            try:
                from .models import DataConnection
                conn = DataConnection.objects.get(id=obj.model.source_connection)
                return {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database
                }
            except DataConnection.DoesNotExist:
                return None
        return None

    def get_target_connection(self, obj):
        """获取目标连接信息"""
        if obj.model and obj.model.target_connection:
            try:
                from .models import DataConnection
                conn = DataConnection.objects.get(id=obj.model.target_connection)
                return {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database
                }
            except DataConnection.DoesNotExist:
                return None
        return None

    def get_source_connection_type(self, obj):
        """获取源连接类型"""
        if obj.model and obj.model.source_connection:
            try:
                from .models import DataConnection
                conn = DataConnection.objects.get(id=obj.model.source_connection)
                return conn.type
            except DataConnection.DoesNotExist:
                return None
        return None

    def get_target_connection_type(self, obj):
        """获取目标连接类型"""
        if obj.model and obj.model.target_connection:
            try:
                from .models import DataConnection
                conn = DataConnection.objects.get(id=obj.model.target_connection)
                return conn.type
            except DataConnection.DoesNotExist:
                return None
        return None


class CompareDiffSerializer(serializers.ModelSerializer):
    """比对差异序列化器"""
    task_id = serializers.ReadOnlyField(source='task.id')
    diff_type_display = serializers.ReadOnlyField(source='get_diff_type_display')
    severity_display = serializers.ReadOnlyField(source='get_severity_display')

    class Meta:
        model = CompareDiff
        fields = [
            'id', 'task_id', 'table_name', 'field_name', 'record_id',
            'diff_type', 'diff_type_display', 'severity', 'severity_display',
            'source_value', 'target_value', 'context', 'create_time'
        ]


class DataConnectionSerializer(serializers.ModelSerializer):
    """数据库连接序列化器"""
    type_display = serializers.ReadOnlyField(source='get_type_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    create_time = serializers.SerializerMethodField()
    update_time = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    class Meta:
        model = DataConnection
        fields = [
            'id', 'name', 'type', 'type_display', 'host', 'port',
            'username', 'password', 'database', 'parameters',
            'status', 'status_display', 'create_time', 'update_time'
        ]
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def get_create_time(self, obj):
        """返回前端期望的create_time字段格式"""
        return obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else None

    def get_update_time(self, obj):
        """返回前端期望的update_time字段格式"""
        return obj.update_time.strftime('%Y-%m-%d %H:%M:%S') if obj.update_time else None

    def get_status(self, obj):
        """将数据库状态值映射为前端期望的格式"""
        # 将'active'映射为'online'，其他状态映射为'offline'
        status_map = {
            'active': 'online',
            'normal': 'online',
            'error': 'offline',
            'inactive': 'offline'
        }
        return status_map.get(obj.status, 'offline')

    def update(self, instance, validated_data):
        """更新连接时，如果没有提供密码或密码为空，则保留现有密码"""
        # 如果没有提供密码或密码为空，从validated_data中移除密码字段
        if 'password' not in validated_data or not validated_data['password']:
            # 记录日志便于调试
            validated_data.pop('password', None)

        # 调用父类的update方法更新其他字段
        return super().update(instance, validated_data)