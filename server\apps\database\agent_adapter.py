"""
Agent适配器 - 极简实现
在现有代码基础上添加Agent标准支持，保持向后兼容
"""
import logging
import requests
from django.conf import settings
from typing import Dict, Any, Optional
from .models import CompareModel, CompareTask, TableConfig, DataConnection

logger = logging.getLogger(__name__)


class AgentAdapter:
    """Agent适配器 - 极简实现，复用现有代码模式"""
    
    def __init__(self):
        self.agent_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
        self.use_agent = getattr(settings, 'USE_AGENT_STANDARD', False)
        self.timeout = 30
    
    def is_agent_enabled(self) -> bool:
        """检查是否启用Agent标准"""
        return self.use_agent
    
    def create_task_via_agent(self, task_data: Dict[str, Any]) -> Optional[str]:
        """通过Agent创建任务 - 复用现有任务创建逻辑"""
        if not self.is_agent_enabled():
            return None
            
        try:
            # 转换为Agent格式
            agent_request = self._convert_to_agent_format(task_data)
            
            # 发送给Agent
            response = requests.post(
                f"{self.agent_url}/api/v1/tasks/create",
                json=agent_request,
                timeout=self.timeout
            )
            
            if response.status_code == 201:
                result = response.json()
                logger.info(f"Agent任务创建成功: {result.get('task_id')}")
                return result.get('task_id')
            else:
                logger.error(f"Agent任务创建失败: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Agent通信失败: {e}")
            return None
    
    def _convert_to_agent_format(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换任务数据为Agent格式 - 复用现有数据结构"""
        model_id = task_data.get('model')
        
        if not model_id:
            raise ValueError("模型ID不能为空")
        
        try:
            # 获取模型信息 - 复用现有模型
            model = CompareModel.objects.get(id=model_id)
            
            # 获取表规则 - 复用现有表配置
            table_rules = list(model.tables.values_list('id', flat=True))
            
            return {
                'model_id': model_id,
                'table_rule_ids': table_rules,
                'task_name': task_data.get('name', f"{model.name}_任务"),
                'user_id': 'system'  # 简化用户管理
            }
            
        except CompareModel.DoesNotExist:
            raise ValueError(f"模型不存在: {model_id}")
    
    def get_task_from_agent(self, task_id: str) -> Optional[Dict[str, Any]]:
        """从Agent获取任务状态 - 如果Agent不可用则返回None"""
        if not self.is_agent_enabled():
            return None
            
        try:
            response = requests.get(
                f"{self.agent_url}/api/v1/tasks/{task_id}",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            logger.warning(f"Agent任务查询失败: {e}")
            return None
    
    def sync_task_status(self, task: CompareTask) -> bool:
        """同步任务状态到Agent - 如果有external_id"""
        if not self.is_agent_enabled() or not task.external_id:
            return False
            
        agent_task = self.get_task_from_agent(task.external_id)
        if not agent_task:
            return False
        
        # 状态映射
        status_mapping = {
            'pending': 'waiting',
            'running': 'running', 
            'completed': 'success',
            'failed': 'failed',
            'cancelled': 'stopped'
        }
        
        agent_status = agent_task.get('status')
        django_status = status_mapping.get(agent_status)
        
        if django_status and django_status != task.status:
            # 更新Django任务状态
            task.status = django_status
            task.progress = agent_task.get('progress_pct', 0)
            task.total_records = agent_task.get('total_records', 0)
            task.processed_records = agent_task.get('processed_records', 0)
            task.different_records = agent_task.get('diff_records', 0)
            task.source_only_records = agent_task.get('source_only', 0)
            task.target_only_records = agent_task.get('target_only', 0)
            task.save()
            
            logger.info(f"任务状态已同步: {task.id} -> {django_status}")
            return True
        
        return False


# 全局适配器实例
agent_adapter = AgentAdapter()


def get_agent_adapter() -> AgentAdapter:
    """获取Agent适配器实例"""
    return agent_adapter


def create_task_with_agent_support(task_data: Dict[str, Any]) -> CompareTask:
    """创建任务（支持Agent） - 扩展现有任务创建逻辑"""
    
    # 创建Django任务（保持现有逻辑）
    model_id = task_data.get('model')
    model = CompareModel.objects.get(id=model_id)
    
    task = CompareTask.objects.create(
        model=model,
        name=task_data.get('name', ''),
        status='waiting'
    )
    
    # 尝试通过Agent创建任务
    adapter = get_agent_adapter()
    if adapter.is_agent_enabled():
        agent_task_id = adapter.create_task_via_agent(task_data)
        if agent_task_id:
            # 保存Agent任务ID
            task.external_id = agent_task_id
            task.save()
            logger.info(f"任务已关联Agent: {task.id} -> {agent_task_id}")
    
    return task


def get_task_status_with_agent_support(task: CompareTask) -> Dict[str, Any]:
    """获取任务状态（支持Agent） - 扩展现有状态查询逻辑"""
    
    # 尝试从Agent同步状态
    adapter = get_agent_adapter()
    if adapter.is_agent_enabled():
        adapter.sync_task_status(task)
    
    # 返回Django任务状态（现有格式）
    return {
        'id': task.id,
        'name': task.name,
        'status': task.status,
        'progress': task.progress,
        'total_records': task.total_records,
        'processed_records': task.processed_records,
        'matched_records': task.matched_records,
        'different_records': task.different_records,
        'source_only_records': task.source_only_records,
        'target_only_records': task.target_only_records,
        'execution_time': task.execution_time,
        'start_time': task.start_time,
        'end_time': task.end_time,
        'message': task.message,
        'external_id': task.external_id,  # Agent任务ID
        'duration': task.duration
    }
