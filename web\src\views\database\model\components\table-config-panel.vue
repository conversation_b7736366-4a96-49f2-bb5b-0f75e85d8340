<template>
  <div class="table-config-panel">
    <el-card class="form-card table-config-card" shadow="never">
      <template #header>
        <div class="table-config-top-row">
          <span class="card-title">数据表配置</span>
          <div class="card-header-actions">
            <el-button 
              type="primary" 
              size="default" 
              plain 
              @click="handleAddTableConfig"
              :icon="Plus"
            >
              添加表配置
            </el-button>
            <el-button 
              type="primary" 
              size="default" 
              plain 
              @click="handleImportXml"
              :icon="Upload"
            >
              导入XML
            </el-button>
            <el-button 
              type="primary" 
              size="default" 
              plain 
              @click="handleDownloadSampleXml"
              :icon="Download"
            >
              下载示例XML
            </el-button>
            <el-tooltip content="表配置可以通过拖拽排序" placement="top">
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </div>
      </template>
      
      <input 
        ref="xmlFileInput" 
        type="file" 
        accept=".xml" 
        style="display: none;"
        @change="handleXmlFileSelected"
      />
      
      <div class="table-config-container">
        <el-collapse v-model="activeNames">
          <div 
            ref="sortableContainer" 
            class="sortable-list"
          >
            <table-config-item
              v-for="(element, index) in localTables"
              :key="element.id"
              :table="element"
              :index="index"
              :collapseName="element.id"
              @update:table="(value) => updateTableConfig(index, value)"
              @remove="(idx) => removeTableConfig(idx)"
            />
          </div>
        </el-collapse>
        
        <div v-if="!localTables.length" class="empty-state">
          <el-empty description="暂无表配置，请点击添加表配置或导入XML">
            <el-button type="primary" size="default" @click="handleAddTableConfig">添加表配置</el-button>
          </el-empty>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Upload, Download, InfoFilled } from '@element-plus/icons-vue';
import Sortable from 'sortablejs';
import TableConfigItem from './table-config-item.vue';
import { v4 as uuidv4 } from 'uuid';
import { parseXmlToTables, createSampleXml as generateSampleXml } from './xml-utils.js';
import { getCurrentTheme } from '@/utils/monaco-theme';

// 移除别名设置，不再需要
// const draggable = VueDraggableNext;
// 更新为定义一个ref来保存sortable实例
const sortableInstance = ref(null);

// 定义表格配置接口
interface TableConfig {
  id: string;
  name: string;
  sql_1: string;
  sql_2: string;
  remark?: string;
  diffEnabled?: boolean;
  diffInfo?: {
    matchCount: number;
    missingCount: number;
    diffCount: number;
  };
}

const props = defineProps<{
  tables: TableConfig[];
}>();

const emit = defineEmits<{
  (e: 'update:tables', value: TableConfig[]): void;
}>();

// 本地状态
const localTables = ref<TableConfig[]>([]);
const activeNames = ref<string[]>([]);
const xmlFileInput = ref<HTMLInputElement | null>(null);

// 编辑器主题
const monacoTheme = computed(() => getCurrentTheme());

// 初始化本地表配置
watch(() => props.tables, (newTables) => {
  localTables.value = JSON.parse(JSON.stringify(newTables));
  
  // 修改此处逻辑：默认所有面板都是收起状态，只保留当前已打开的面板
  // 获取当前已打开的面板ID
  const currentActiveIds = [...activeNames.value];
  
  // 只保留已经存在的有效ID
  activeNames.value = currentActiveIds.filter(id => 
    newTables.some(table => table.id === id)
  );
}, { deep: true, immediate: true });

// 添加表配置
const handleAddTableConfig = () => {
  const newTable: TableConfig = {
    id: uuidv4(),
    name: '',
    sql_1: '',
    sql_2: '',
    remark: `配置 ${localTables.value.length + 1}`,
    diffEnabled: false
  };
  
  localTables.value.push(newTable);
  emit('update:tables', localTables.value);
  
  // 只展开新添加的配置面板，而不影响其他面板
  nextTick(() => {
    activeNames.value = [newTable.id];
  });
};

// 更新表配置
const updateTableConfig = (index: number, updatedTable: TableConfig) => {
  if (index >= 0 && index < localTables.value.length) {
    localTables.value[index] = updatedTable;
    emit('update:tables', localTables.value);
  }
};

// 删除表配置
const removeTableConfig = (index: number) => {
  if (index >= 0 && index < localTables.value.length) {
    const removedId = localTables.value[index].id;
    localTables.value.splice(index, 1);
    emit('update:tables', localTables.value);
    
    // 从activeNames中删除对应ID
    const nameIndex = activeNames.value.indexOf(removedId);
    if (nameIndex !== -1) {
      activeNames.value.splice(nameIndex, 1);
    }
  }
};

// 处理拖拽结束事件
const handleDragEnd = (evt) => {
  // 获取新的排序顺序
  const { oldIndex, newIndex } = evt;
  if (oldIndex !== newIndex) {
    // 创建新数组以避免直接修改响应式数据
    const itemsCopy = [...localTables.value];
    
    // 移除拖动项
    const movedItem = itemsCopy.splice(oldIndex, 1)[0];
    
    // 在新位置插入
    itemsCopy.splice(newIndex, 0, movedItem);
    
    // 更新数据
    localTables.value = itemsCopy;
    emit('update:tables', localTables.value);
  }
};

// 触发XML文件选择
const handleImportXml = () => {
  if (xmlFileInput.value) {
    xmlFileInput.value.click();
  }
};

// XML文件选择后的处理
const handleXmlFileSelected = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  if (files && files.length > 0) {
    const file = files[0];
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const xmlContent = e.target?.result as string;
      
      try {
        // 解析XML并获取表配置
        const importedTables = parseXmlToTables(xmlContent);
        
        // 确认是否导入
        ElMessageBox.confirm(
          `确定要导入 ${importedTables.length} 个表配置吗？这将覆盖现有配置。`,
          '导入确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          // 为导入的配置生成新的ID
          const tablesWithId = importedTables.map(table => ({
            ...table,
            id: uuidv4()
          }));
          
          localTables.value = tablesWithId;
          emit('update:tables', localTables.value);
          
          // 导入后默认所有面板为收起状态，不自动展开
          activeNames.value = [];
          
          ElMessage.success(`成功导入 ${importedTables.length} 个表配置`);
        }).catch(() => {
          ElMessage.info('已取消导入');
        });
      } catch (error) {
        ElMessage.error('XML解析失败，请检查文件格式');
        console.error('XML解析错误:', error);
      }
      
      // 重置文件输入，以便于再次选择同一文件
      if (xmlFileInput.value) {
        xmlFileInput.value.value = '';
      }
    };
    
    reader.readAsText(file);
  }
};

// 下载示例XML
const handleDownloadSampleXml = () => {
  try {
    // 生成示例XML
    const xmlContent = generateSampleXml();
    
    // 创建Blob对象
    const blob = new Blob([xmlContent], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = 'table_config_sample.xml';
    link.click();
    
    // 释放URL对象
    URL.revokeObjectURL(url);
    
    ElMessage.success('示例XML下载成功');
  } catch (error) {
    ElMessage.error('下载示例XML失败');
    console.error('下载示例XML错误:', error);
  }
};

// 初始化Sortable的函数
const initSortable = () => {
  nextTick(() => {
    const el = document.querySelector('.sortable-list');
    if (!el || sortableInstance.value) return;
    
    sortableInstance.value = Sortable.create(el as HTMLElement, {
      animation: 150,
      handle: '.el-collapse-item__header',
      ghostClass: 'ghost',
      chosenClass: 'chosen',
      onEnd: handleDragEnd
    });
  });
};

// 添加onMounted钩子
onMounted(() => {
  initSortable();
});

// 当表格配置变化时重新初始化Sortable
watch(() => localTables.value.length, () => {
  if (sortableInstance.value) {
    sortableInstance.value.destroy();
    sortableInstance.value = null;
  }
  initSortable();
});
</script>

<style lang="scss" scoped>
.table-config-panel {
  width: 100%;
}

.form-card.table-config-card {
  border: none !important;
  
  :deep(.el-card__body) {
    padding: 0px 0px;
  }
}

.table-config-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .card-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.table-config-container {
  :deep(.el-collapse) {
    border-top: none;
    border-bottom: none;
    
    .el-collapse-item__header {
      background-color: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color-light);
      border-radius: 4px;
      padding: 0 16px;
      margin-bottom: 8px;
      height: 48px;
      line-height: 48px;
      font-size: 14px;
      
      &.is-active {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: none;
        margin-bottom: 0;
      }
    }
    
    .el-collapse-item__wrap {
      border: 1px solid var(--el-border-color-light);
      border-top: none;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      margin-bottom: 0px;
      background-color: var(--el-bg-color);
      
      .el-collapse-item__content {
        padding: 12px; // 减少内部内边距
      }
    }
  }
}

// 添加拖拽列表样式
.sortable-list {
  width: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  padding: 20px 0; // 减少空状态的内边距
}

// 拖拽样式
.ghost {
  opacity: 0.5;
  background: var(--el-color-primary-light-9);
}

.chosen {
  background: var(--el-color-primary-light-8);
}

// 适配暗黑模式
:deep(.dark) {
  .el-collapse-item__header {
    background-color: #2d2d2d;
  }
}
</style> 