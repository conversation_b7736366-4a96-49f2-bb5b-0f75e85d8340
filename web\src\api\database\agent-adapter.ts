/**
 * Agent适配器 - 前端兼容层
 * 在现有API基础上添加Agent支持，保持向后兼容
 */

import { request } from '@/utils/request'
import type { CompareTask, CompareModel } from './types'

// Agent配置
const AGENT_CONFIG = {
  enabled: false, // 默认关闭，可通过环境变量或配置控制
  baseUrl: '/api/v1/agent', // Agent API基础路径
  fallback: true // 是否启用回退机制
}

/**
 * 检查是否启用Agent标准架构
 */
export function isAgentEnabled(): boolean {
  return AGENT_CONFIG.enabled
}

/**
 * 启用Agent标准架构
 */
export function enableAgent(): void {
  AGENT_CONFIG.enabled = true
  console.log('🚀 Agent标准架构已启用')
}

/**
 * 禁用Agent标准架构
 */
export function disableAgent(): void {
  AGENT_CONFIG.enabled = false
  console.log('📋 切换到原有架构')
}

/**
 * 创建比对任务 - 兼容Agent和原有方式
 */
export async function createCompareTask(data: {
  model: number
  name?: string
  limit?: number
  options?: any
}): Promise<CompareTask> {
  if (isAgentEnabled()) {
    try {
      // 尝试使用Agent标准API
      const response = await request.post(`${AGENT_CONFIG.baseUrl}/tasks/create_task/`, {
        model_id: data.model,
        task_name: data.name,
        user_id: 'current_user' // 简化用户管理
      })
      
      if (response.success) {
        console.log('✅ Agent任务创建成功:', response.task_id)
        // 转换为原有格式以保持兼容性
        return {
          id: response.task_id,
          external_id: response.task_id,
          name: data.name || '',
          status: 'waiting',
          model: data.model,
          progress: 0,
          create_time: new Date().toISOString()
        } as CompareTask
      }
    } catch (error) {
      console.warn('⚠️ Agent任务创建失败，回退到原有方式:', error)
      if (!AGENT_CONFIG.fallback) {
        throw error
      }
    }
  }
  
  // 使用原有API
  return request.post('/api/database/tasks/', data)
}

/**
 * 获取任务详情 - 兼容Agent和原有方式
 */
export async function getTaskDetail(taskId: number | string): Promise<CompareTask> {
  // 直接使用原有API，后端会自动同步Agent状态
  return request.get(`/api/database/tasks/${taskId}/`)
}

/**
 * 获取任务列表 - 兼容Agent和原有方式
 */
export async function getTaskList(params?: {
  model?: number
  status?: string
  page?: number
  page_size?: number
}): Promise<{
  results: CompareTask[]
  total: number
  page: number
  page_size: number
}> {
  // 直接使用原有API，后端会自动同步Agent状态
  return request.get('/api/database/tasks/', { params })
}

/**
 * 启动任务 - 兼容Agent和原有方式
 */
export async function startTask(taskId: number, options?: {
  name?: string
  limit?: number
  options?: any
}): Promise<any> {
  // 使用原有API，后端会自动判断是否使用Agent
  return request.post(`/api/database/tasks/${taskId}/start/`, options)
}

/**
 * 停止任务 - 兼容Agent和原有方式
 */
export async function stopTask(taskId: number): Promise<any> {
  if (isAgentEnabled()) {
    try {
      // 尝试通过Agent停止任务
      const task = await getTaskDetail(taskId)
      if (task.external_id) {
        const response = await request.post(`${AGENT_CONFIG.baseUrl}/tasks/${task.external_id}/control_task/`, {
          action: 'cancel',
          task_id: task.external_id
        })
        
        if (response.success) {
          console.log('✅ Agent任务停止成功')
          return response
        }
      }
    } catch (error) {
      console.warn('⚠️ Agent任务停止失败，回退到原有方式:', error)
      if (!AGENT_CONFIG.fallback) {
        throw error
      }
    }
  }
  
  // 使用原有API
  return request.post(`/api/database/tasks/${taskId}/stop/`)
}

/**
 * 获取任务进度 - 实时更新
 */
export async function getTaskProgress(taskId: number): Promise<{
  status: string
  progress: number
  current_step?: string
  total_records?: number
  processed_records?: number
}> {
  if (isAgentEnabled()) {
    try {
      // 尝试从Agent获取实时进度
      const task = await getTaskDetail(taskId)
      if (task.external_id) {
        const response = await request.get(`${AGENT_CONFIG.baseUrl}/tasks/${task.external_id}/progress/`)
        return response
      }
    } catch (error) {
      console.warn('⚠️ Agent进度查询失败，使用原有方式:', error)
    }
  }
  
  // 使用原有方式
  const task = await getTaskDetail(taskId)
  return {
    status: task.status,
    progress: task.progress || 0,
    total_records: task.total_records,
    processed_records: task.processed_records
  }
}

/**
 * Agent健康检查
 */
export async function checkAgentHealth(): Promise<{
  available: boolean
  status?: string
  error?: string
}> {
  if (!isAgentEnabled()) {
    return { available: false, status: 'disabled' }
  }
  
  try {
    const response = await request.get(`${AGENT_CONFIG.baseUrl}/health/check/`)
    return {
      available: true,
      status: response.agent_status
    }
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 配置Agent设置
 */
export function configureAgent(config: {
  enabled?: boolean
  baseUrl?: string
  fallback?: boolean
}): void {
  if (config.enabled !== undefined) {
    AGENT_CONFIG.enabled = config.enabled
  }
  if (config.baseUrl) {
    AGENT_CONFIG.baseUrl = config.baseUrl
  }
  if (config.fallback !== undefined) {
    AGENT_CONFIG.fallback = config.fallback
  }
  
  console.log('🔧 Agent配置已更新:', AGENT_CONFIG)
}

// 导出配置供外部使用
export { AGENT_CONFIG }
