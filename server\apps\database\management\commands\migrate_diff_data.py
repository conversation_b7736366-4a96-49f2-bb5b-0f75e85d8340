"""
数据迁移管理命令：将Agent结果中的差异数据持久化到数据库

使用方法：
python manage.py migrate_diff_data --all                    # 迁移所有已完成的任务
python manage.py migrate_diff_data --task-ids 1,2,3        # 迁移指定任务
python manage.py migrate_diff_data --force-refresh         # 强制刷新已存在的记录
python manage.py migrate_diff_data --dry-run               # 预览模式，不实际执行
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.database.models import CompareTask, CompareDiff
from apps.database.services import DiffPersistenceService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '将Agent结果中的差异数据持久化到数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='迁移所有已完成的任务'
        )
        
        parser.add_argument(
            '--task-ids',
            type=str,
            help='指定要迁移的任务ID，用逗号分隔，例如：1,2,3'
        )
        
        parser.add_argument(
            '--force-refresh',
            action='store_true',
            help='强制刷新已存在的差异记录'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='预览模式，不实际执行迁移'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=10,
            help='批处理大小，默认10个任务一批'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始数据迁移...'))
        
        # 获取要处理的任务
        tasks = self.get_tasks_to_migrate(options)
        
        if not tasks:
            self.stdout.write(self.style.WARNING('没有找到需要迁移的任务'))
            return
        
        self.stdout.write(f'找到 {len(tasks)} 个任务需要处理')
        
        # 统计信息
        stats = {
            'total_tasks': len(tasks),
            'success_count': 0,
            'skip_count': 0,
            'error_count': 0,
            'total_diffs_migrated': 0
        }
        
        # 批量处理
        batch_size = options['batch_size']
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            self.process_batch(batch, options, stats)
        
        # 输出最终统计
        self.print_final_stats(stats)

    def get_tasks_to_migrate(self, options):
        """获取要迁移的任务列表"""
        if options['all']:
            # 获取所有已完成的任务
            tasks = CompareTask.objects.filter(status__in=['success', 'failed']).order_by('id')
            self.stdout.write(f'选择所有已完成的任务: {tasks.count()} 个')
        elif options['task_ids']:
            # 获取指定的任务
            try:
                task_ids = [int(id.strip()) for id in options['task_ids'].split(',')]
                tasks = CompareTask.objects.filter(id__in=task_ids).order_by('id')
                self.stdout.write(f'选择指定的任务: {list(task_ids)}')
            except ValueError:
                raise CommandError('任务ID格式错误，请使用逗号分隔的数字，例如：1,2,3')
        else:
            raise CommandError('请指定 --all 或 --task-ids 参数')
        
        return list(tasks)

    def process_batch(self, tasks, options, stats):
        """处理一批任务"""
        self.stdout.write(f'\n处理批次: 任务 {tasks[0].id} - {tasks[-1].id}')
        
        for task in tasks:
            try:
                self.process_single_task(task, options, stats)
            except Exception as e:
                stats['error_count'] += 1
                self.stdout.write(
                    self.style.ERROR(f'任务 {task.id} 处理失败: {str(e)}')
                )
                logger.exception(f"迁移任务{task.id}时发生异常")

    def process_single_task(self, task, options, stats):
        """处理单个任务"""
        self.stdout.write(f'处理任务 {task.id} (状态: {task.status})', ending='')
        
        # 检查是否已有差异记录
        existing_count = CompareDiff.objects.filter(task=task).count()
        
        if existing_count > 0 and not options['force_refresh']:
            self.stdout.write(self.style.WARNING(f' - 跳过 (已有 {existing_count} 条记录)'))
            stats['skip_count'] += 1
            return
        
        if options['dry_run']:
            # 预览模式：只检查数据，不实际执行
            result = self.preview_task_migration(task)
            if result['has_diffs']:
                self.stdout.write(
                    self.style.SUCCESS(f' - 预览: 将迁移 {result["diff_count"]} 条差异记录')
                )
                stats['total_diffs_migrated'] += result['diff_count']
            else:
                self.stdout.write(self.style.WARNING(' - 预览: 无差异记录'))
            stats['success_count'] += 1
            return
        
        # 实际执行迁移
        result = DiffPersistenceService.persist_task_diffs(
            task, 
            force_refresh=options['force_refresh']
        )
        
        if result['success']:
            migrated_count = result['stats']['persisted_count']
            if migrated_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f' - 成功迁移 {migrated_count} 条差异记录')
                )
                stats['total_diffs_migrated'] += migrated_count
            else:
                self.stdout.write(self.style.WARNING(' - 无差异记录需要迁移'))
            stats['success_count'] += 1
        else:
            self.stdout.write(self.style.ERROR(f' - 失败: {result["message"]}'))
            stats['error_count'] += 1

    def preview_task_migration(self, task):
        """预览任务迁移（不实际执行）"""
        try:
            # 尝试从Agent获取数据
            agent_data = DiffPersistenceService._get_agent_diff_data(task)
            if agent_data['success']:
                return {
                    'has_diffs': len(agent_data['items']) > 0,
                    'diff_count': len(agent_data['items']),
                    'source': 'agent'
                }
            
            # 尝试从任务结果获取数据
            task_data = DiffPersistenceService._get_task_result_data(task)
            if task_data['success']:
                return {
                    'has_diffs': len(task_data['items']) > 0,
                    'diff_count': len(task_data['items']),
                    'source': 'task_result'
                }
            
            return {'has_diffs': False, 'diff_count': 0, 'source': 'none'}
            
        except Exception as e:
            logger.exception(f"预览任务{task.id}时发生异常")
            return {'has_diffs': False, 'diff_count': 0, 'source': 'error'}

    def print_final_stats(self, stats):
        """输出最终统计信息"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('数据迁移完成'))
        self.stdout.write('='*50)
        self.stdout.write(f'总任务数: {stats["total_tasks"]}')
        self.stdout.write(f'成功处理: {stats["success_count"]}')
        self.stdout.write(f'跳过任务: {stats["skip_count"]}')
        self.stdout.write(f'失败任务: {stats["error_count"]}')
        self.stdout.write(f'迁移差异记录总数: {stats["total_diffs_migrated"]}')
        
        if stats['error_count'] > 0:
            self.stdout.write(
                self.style.WARNING(f'有 {stats["error_count"]} 个任务处理失败，请检查日志')
            )
        else:
            self.stdout.write(self.style.SUCCESS('所有任务处理成功！'))
