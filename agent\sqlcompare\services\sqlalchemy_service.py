"""
SQLAlchemy业务逻辑层
本模块专注于业务逻辑实现，实现关注点分离的架构设计。
"""
import sys
import uuid
import time
import base64
import logging
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from sqlalchemy.orm import joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, inspect, desc, func, text

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from models.sqlalchemy_models import (
    User, UserSession, ComparisonConnection, ComparisonModel,
    ComparisonTableRule, ComparisonTask, ComparisonResult,
    TaskStatus, DifferenceStatus, build_conn_engine,
    create_database_engine, create_tables, get_session_factory
)

logger = logging.getLogger(__name__)


class SQLAlchemyComparisonService:
    """SQLAlchemy比对服务 - 单例模式"""
    _instance = None
    _instance_url = None
    _lock = threading.Lock()

    def __new__(cls, database_url: str = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, database_url: str = None):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        # 使用默认数据库URL如果未提供
        if database_url is None:
            database_url = self._get_default_database_url()

        # 存储数据库URL用于调试和日志
        self.database_url = database_url
        self.database_type = self._detect_database_type(database_url)

        # 使用标准的create_database_engine函数（已包含高性能优化）
        self.engine = create_database_engine(database_url)
        self.SessionLocal = get_session_factory(self.engine)

        self._ensure_tables_initialized()

        # 标记已初始化
        self._initialized = True
        # 进度跟踪控制
        self._last_progress_update = {}
        self._progress_update_interval = 3.0
        logger.info(f"SQLAlchemy服务初始化完成: {database_url} (类型: {self.database_type})")

    @classmethod
    def get_instance(cls, database_url: str = None) -> 'SQLAlchemyComparisonService':
        """获取单例实例"""
        if database_url is None:
            database_url = cls._get_default_database_url()

        if cls._instance is None or cls._instance_url != database_url:
            cls._instance = cls(database_url)
            cls._instance_url = database_url

        return cls._instance

    @staticmethod
    def _get_default_database_url() -> str:
        """获取默认数据库URL，支持环境变量配置"""
        import os

        # 优先使用环境变量
        env_url = os.getenv('SQLCOMPARE_DATABASE_URL')
        if env_url:
            logger.info(f"使用环境变量数据库URL: {env_url}")
            return env_url

        # 检查是否有PostgreSQL环境变量
        pg_host = os.getenv('POSTGRES_HOST')
        pg_port = os.getenv('POSTGRES_PORT', '5432')
        pg_db = os.getenv('POSTGRES_DB')
        pg_user = os.getenv('POSTGRES_USER')
        pg_password = os.getenv('POSTGRES_PASSWORD')

        if all([pg_host, pg_db, pg_user, pg_password]):
            pg_url = f"postgresql+psycopg2://{pg_user}:{pg_password}@{pg_host}:{pg_port}/{pg_db}"
            logger.info(f"使用PostgreSQL环境变量构建URL: {pg_host}:{pg_port}/{pg_db}")
            return pg_url

        # 默认使用SQLite
        default_url = "sqlite:///sqlcompare_v4.db"
        logger.info(f"使用默认SQLite数据库: {default_url}")
        return default_url

    @staticmethod
    def _detect_database_type(database_url: str) -> str:
        """检测数据库类型"""
        if database_url.startswith('postgresql'):
            return 'postgresql'
        elif database_url.startswith('mysql'):
            return 'mysql'
        elif database_url.startswith('sqlite'):
            return 'sqlite'
        elif database_url.startswith('oracle'):
            return 'oracle'
        elif database_url.startswith('db2'):
            return 'db2'
        else:
            return 'unknown'

    def _ensure_tables_initialized(self):
        """确保数据库表已初始化"""
        try:           
            inspector = inspect(self.engine)
            existing_tables = inspector.get_table_names()

            # 检查关键表是否存在
            required_tables = ['comparison_models', 'comparison_results']
            tables_initialized = all(table in existing_tables for table in required_tables)

            if not tables_initialized:
                create_tables(self.engine)

        except Exception as e:
            logger.error(f"数据库表初始化失败: {e}")
            raise

    def get_task_details(self, task_id: str) -> Optional[ComparisonTask]:
        """获取任务详细信息"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

            if task:
                # 使用expunge将对象从会话中分离，但保留已加载的属性
                session.expunge(task)

            return task

    def get_task_execution_info(self, task_id: str):
        """
        获取任务执行信息 - 优化版本，使用预加载避免N+1查询

        Args:
            task_id: 任务ID

        Returns:
            TaskExecutionInfo: 任务执行信息，如果任务不存在则返回None

        Raises:
            ValueError: 当task_id为空时
            SQLAlchemyError: 数据库操作错误
        """
        if not task_id:
            raise ValueError("任务ID不能为空")

        try:
            # 性能监控：记录查询开始时间
            import time
            query_start_time = time.perf_counter()

            with self.get_db_session() as session:
                # 一次性预加载所有相关数据，避免N+1查询问题
                task = session.query(ComparisonTask).options(
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.source_connection, innerjoin=False),
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.target_connection, innerjoin=False),
                    # 新增：预加载表规则，避免N+1查询
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.table_rules, innerjoin=False)
                ).filter(ComparisonTask.task_id == task_id).first()

                # 记录查询耗时
                query_time = time.perf_counter() - query_start_time
                logger.info(f"任务执行信息查询耗时: {query_time:.3f}秒 (任务ID: {task_id})")

                if not task:
                    logger.warning(f"任务不存在: {task_id}")
                    return None

                # 处理表规则信息 - 使用预加载的数据
                table_rules = []
                if task.model_id and task.model and hasattr(task.model, 'table_rules') and task.model.table_rules:
                    # 使用预加载的表规则数据，避免额外查询
                    logger.debug(f"使用预加载的表规则数据，共 {len(task.model.table_rules)} 条规则")
                    for rule in task.model.table_rules:
                        # 只处理活跃的规则
                        if rule.is_active:
                            table_rules.append({
                                'id': rule.id,
                                'table_id': rule.table_id,
                                'table_name': rule.table_name or rule.table_id,
                                'remark': rule.remark,
                                'source_sql': rule.sql_1,
                                'target_sql': rule.sql_2,
                                'is_active': rule.is_active,
                                'create_time': rule.create_time,
                                'update_time': rule.update_time
                            })
                    logger.debug(f"过滤后的活跃表规则数量: {len(table_rules)}")
                else:
                    # 任务没有关联模型或表规则时，返回空列表并记录警告
                    if task.model_id:
                        logger.warning(f"任务 {task_id} 关联的模型 {task.model_id} 没有表规则")
                    else:
                        logger.warning(f"任务 {task_id} 没有关联的比对模型")
                    table_rules = []

                # 计算匹配记录数
                matched_records = max(0, (task.total_records or 0) - (task.diff_records or 0) -
                                    (task.source_only or 0) - (task.target_only or 0))

                # 计算执行持续时间
                duration = None
                if task.start_time and task.complete_time:
                    duration = (task.complete_time - task.start_time).total_seconds()
                elif task.start_time and not task.complete_time and task.status == TaskStatus.RUNNING.value:
                    # 正在运行的任务，计算到当前时间的持续时间
                    from datetime import datetime
                    duration = (datetime.now() - task.start_time).total_seconds()

                # 构建配置信息概要（兼容模式处理）
                config_summary = {}
                if task.model:
                    # 标准模式：从关联表获取配置
                    config_summary = {
                        'model_name': task.model.name,
                        'comparison_type': task.model.cmp_type,
                        'source_connection_name': task.model.source_connection.name if task.model.source_connection else None,
                        'target_connection_name': task.model.target_connection.name if task.model.target_connection else None,
                        'global_config': task.model.global_config
                    }
                else:
                    # 兼容模式：提供默认配置
                    config_summary = {
                        'model_name': task.task_name or '兼容模式任务',
                        'comparison_type': 'content',
                        'source_connection_name': '兼容模式源连接',
                        'target_connection_name': '兼容模式目标连接',
                        'global_config': None
                    }
                    logger.warning(f"任务 {task_id} 运行在兼容模式下，使用默认配置")

                # 构建模型响应信息
                model_response = None
                if task.model:
                    model_response = {
                        'id': task.model.id,
                        'name': task.model.name,
                        'description': task.model.description,
                        'source_connid': task.model.source_connid,
                        'target_connid': task.model.target_connid,
                        'cmp_type': task.model.cmp_type,
                        'global_config': task.model.global_config,
                        'status': task.model.status,
                        'create_time': task.model.create_time,
                        'update_time': task.model.update_time
                    }

                # 构建任务执行信息
                from models.pydantic_models import TaskExecutionInfo
                execution_info = TaskExecutionInfo(
                    task_id=task.task_id,
                    task_name=task.task_name,
                    description=task.description,
                    user_id=task.user_id,
                    model_id=task.model_id,
                    table_rule_id=task.table_rule_id,
                    status=task.status,
                    progress_pct=task.progress_pct or 0,
                    current_step=task.current_step,
                    create_time=task.create_time,
                    start_time=task.start_time,
                    complete_time=task.complete_time,
                    update_time=task.update_time,
                    duration=duration,
                    total_records=task.total_records or 0,
                    processed_records=task.processed_records or 0,
                    matched_records=matched_records,
                    diff_records=task.diff_records or 0,
                    source_only=task.source_only or 0,
                    target_only=task.target_only or 0,
                    exec_time=task.exec_time or 0,
                    error_msg=task.error_msg,
                    error_details=task.error_details,
                    config_summary=config_summary,
                    model=model_response,
                    table_rules=table_rules
                )

                logger.info(f"成功获取任务执行信息: {task_id}")
                return execution_info

        except SQLAlchemyError as e:
            logger.error(f"获取任务执行信息时数据库错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取任务执行信息时发生未知错误: {e}")
            raise

    @contextmanager
    def get_db_session(self):
        """数据库会话上下文管理器"""

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"未知错误: {e}")
            raise
        finally:
            session.close()

    # ==================== 用户管理 ====================

    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> str:
        """创建用户"""
        with self.get_db_session() as session:
            # 生成唯一的user_id
            user_id = f"u_{hash(username) % 1000000:06d}"

            user = User(
                user_id=user_id,
                username=username,
                email=email,
                password=password,
                role=role
            )
            session.add(user)
            session.flush()
            return user.user_id
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """用户认证"""
        with self.get_db_session() as session:
            user = session.query(User).filter(
                and_(User.email == email, User.is_active == True)
            ).first()
            
            if user and user.password == password:
                # 更新最后登录时间
                user.last_login_at = datetime.now()
                return user
            return None
    
    def create_user_session(self, user_id: str, ip_address: str, user_agent: str) -> str:
        """创建用户会话"""
        with self.get_db_session() as session:
            session_id = str(uuid.uuid4())
            expires_at = datetime.now() + timedelta(hours=24)  # 24小时过期
            
            user_session = UserSession(
                session_id=session_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )
            session.add(user_session)
            return session_id
    
    # ==================== 连接管理 ====================
    
    def create_database_connection(self, name: str, type: str, host: str,
                                 port: int, username: str, password: str,
                                 database: str, **kwargs) -> int:
        """创建数据库连接配置"""
        # 输入验证
        if not all([name, type, host, username, password, database]):
            raise ValueError("所有必需参数都不能为空")
        if port <= 0 or port > 65535:
            raise ValueError("端口号必须在1-65535范围内")

        with self.get_db_session() as session:
            # 检查连接名称是否已存在
            existing = session.query(ComparisonConnection).filter_by(name=name).first()
            if existing:
                raise ValueError(f"连接名称 '{name}' 已存在")

            connection = ComparisonConnection(
                name=name,
                type=type.lower(),
                host=host,
                port=port,
                username=username,
                password=base64.b64encode(password.encode('utf-8')),
                database=database,
                params=kwargs.get('params')
            )
            session.add(connection)
            session.flush()

            return connection.id
    
    def get_connection_by_name(self, name: str) -> Optional[ComparisonConnection]:
        """ 根据名称查询数据库连接 """
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(name=name).first()
            if connection:
                session.expunge(connection)
            return connection

    def test_database_connection(self, connection_id: int) -> Dict[str, Any]:
        """ 使用SQLAlchemy直接测试数据库连接"""
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(id=connection_id).first()
            if not connection:
                return {'success': False, 'error': f'连接配置不存在: {connection_id}'}

            try:
                from sqlalchemy import text
                from sqlalchemy.exc import SQLAlchemyError
                
                test_success = False
                error_message = None
                start_time = time.time()

                # 构建数据库连接引擎
                engine = build_conn_engine(
                    db_type=connection.type,
                    host=connection.host,
                    port=connection.port,
                    username=connection.username,
                    password=base64.b64decode(connection.password),
                    database=connection.database
                )
                if not engine:
                    return { 'success': False, 'error': '不支持的数据库类型', 'connection_id': connection_id}

                try:
                    # 获取连接并执行测试查询
                    with engine.connect() as conn:
                        test_query = self._get_test_query(connection.type)
                        result = conn.execute(text(test_query))

                        row = result.fetchone()
                        if row is not None:
                            test_success = True
                        else:
                            error_message = "测试查询未返回结果"

                except SQLAlchemyError as e:
                    error_message = f"SQLAlchemy错误: {str(e)}"
                    logger.error(f"数据库连接测试失败 [{connection.name}]: {error_message}")

                except Exception as e:
                    error_message = f"连接错误: {str(e)}"
                    logger.error(f"数据库连接测试异常 [{connection.name}]: {error_message}")

                finally:
                    # 确保引擎资源被释放
                    try:
                        engine.dispose()
                    except Exception as e:
                        logger.warning(f"释放数据库引擎资源时出错: {e}")

                response_time = time.time() - start_time
                # 更新连接状态
                connection.status = 'active' if test_success else 'error'

                if test_success:
                    return {'success': True, 'connection_id': connection_id,'response_time': round(response_time, 3)}
                else:
                    return {'success': False, 'error': error_message or '未知连接错误', 'connection_id': connection_id}

            except Exception as e:
                connection.status = 'error'
                error_msg = f"连接测试异常: {str(e)}"
                return {'success': False, 'error': error_msg, 'connection_id': connection_id}

    def _get_test_query(self, db_type: str) -> str:
        """获取数据库测试查询语句"""
        db_type_lower = db_type.lower()

        if db_type_lower == 'mysql':
            return "SELECT 1 as test_result"
        elif db_type_lower == 'db2':
            return "SELECT 1 as test_result FROM SYSIBM.SYSDUMMY1"
        elif db_type_lower in ['postgresql', 'postgres']:
            return "SELECT 1 as test_result"
        elif db_type_lower == 'sqlite':
            return "SELECT 1 as test_result"
        elif db_type_lower in ['oracle', 'ora']:
            return "SELECT 1 as test_result FROM DUAL"
        elif db_type_lower in ['sqlserver', 'mssql']:
            return "SELECT 1 as test_result"
        else:
            return "SELECT 1 as test_result"

    # ==================== 任务管理 ====================

    def _generate_standard_task_name(self) -> str:
        """生成标准化的任务名称"""
        with self.get_db_session() as session:
            # 查询所有符合 task_xxx 格式的任务名称
            result = session.execute(text("""
                SELECT task_name FROM comparison_tasks
                WHERE task_name LIKE 'task_%'
                AND LENGTH(task_name) = 8
                AND SUBSTR(task_name, 6) GLOB '[0-9][0-9][0-9]'
                ORDER BY task_name DESC
                LIMIT 1
            """)).fetchone()

            if result and result[0]:
                # 提取序列号并加1
                last_task_name = result[0]
                last_sequence = int(last_task_name.split('_')[1])
                next_sequence = last_sequence + 1
            else:
                # 如果没有找到符合格式的任务，从001开始
                next_sequence = 1

            # 生成新的任务名称，序列号补零到3位
            new_task_name = f"task_{next_sequence:03d}"

            # 确保生成的任务名称在数据库中是唯一的
            while session.query(ComparisonTask).filter_by(task_name=new_task_name).first():
                next_sequence += 1
                new_task_name = f"task_{next_sequence:03d}"

            return new_task_name

    def create_comparison_task(self, user_id: str, name: str = None, model_id: int = None,
                             table_rule_id: int = None, **kwargs) -> str:
        """创建比对任务 - 支持标准化任务命名"""
        with self.get_db_session() as session:
            # 验证用户存在，如果不存在则自动创建
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                # 自动创建用户
                logger.info(f"用户 {user_id} 不存在，自动创建")
                user = User(
                    user_id=user_id,
                    username=user_id,
                    email=f"{user_id}@system.local",
                    password="auto_created"
                )
                session.add(user)
                session.flush()
                logger.info(f"自动创建用户成功: {user_id}")

            # 如果提供了model_id，验证模型存在
            if model_id:
                model = session.query(ComparisonModel).filter_by(id=model_id).first()
                if not model:
                    raise ValueError(f"比对模型 {model_id} 不存在")

            # 如果未指定任务名称或为空，则自动生成
            if not name or name.strip() == "":
                task_name = self._generate_standard_task_name()
            else:
                task_name = name.strip()

            # 生成任务ID
            task_id = str(uuid.uuid4())

            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                model_id=model_id,
                table_rule_id=table_rule_id,
                task_name=task_name,
                description=kwargs.get('description'),
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"任务创建成功: {task_name}, task_id: {task_id}")
            return task_id
    
    def get_user_tasks(self, user_name: str, status_filter: Optional[str] = None,
                      limit: int = 20, offset: int = 0) -> List[ComparisonTask]:
        """获取用户任务列表 - 展示复杂查询功能"""
        with self.get_db_session() as session:
            query = session.query(ComparisonTask).filter_by(user_name=user_name)
            
            # 状态过滤
            if status_filter:
                query = query.filter(ComparisonTask.status == status_filter)
            
            # 预加载关联数据
            query = query.options(
                joinedload(ComparisonTask.user),
                joinedload(ComparisonTask.results)
            )
            
            # 排序和分页
            tasks = query.order_by(desc(ComparisonTask.create_time)).offset(offset).limit(limit).all()
            return tasks
    
    def update_task_status(self, task_id: str, status: TaskStatus, **kwargs) -> bool:
        """更新任务状态"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return False
            
            # 更新状态
            task.status = status.value
            
            # 更新其他字段
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            
            # 设置时间戳（修复字段名）
            if status == TaskStatus.RUNNING and not task.start_time:
                task.start_time = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.complete_time = datetime.now()
            
            return True
    
    # ==================== 结果管理 ====================
    
    def save_comparison_results(self, task_id: str, results: List[Dict[str, Any]]) -> int:
        """批量保存比对结果 - 展示批量操作"""
        with self.get_db_session() as session:
            # 验证任务存在
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            result_objects = []
            for result_data in results:
                result = ComparisonResult(
                    task_id=task_id,
                    table_name=result_data.get('table_name'),
                    record_key=result_data.get('record_key'),
                    difference_type=result_data.get('difference_type'),
                    field_name=result_data.get('field_name'),
                    source_value=result_data.get('source_value'),
                    target_value=result_data.get('target_value'),
                    status='new'
                )
                result_objects.append(result)
            
            # 批量插入
            session.add_all(result_objects)
            
            # 更新任务统计
            task.diff_records = len(result_objects)
            
            return len(result_objects)
    
    def get_comparison_results(self, task_id: str, difference_type: Optional[str] = None,
                             limit: int = 100, offset: int = 0) -> List[ComparisonResult]:
        """获取比对结果 - 展示复杂查询和分页"""
        with self.get_db_session() as session:
            query = session.query(ComparisonResult).filter_by(task_id=task_id)
            
            if difference_type:
                query = query.filter(ComparisonResult.difference_type == difference_type)
            
            results = query.order_by(ComparisonResult.created_at).offset(offset).limit(limit).all()
            return results
    
    def get_task_statistics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务统计 - 展示聚合查询"""
        with self.get_db_session() as session:
            # 使用SQLAlchemy的聚合函数
            stats = session.query(
                func.count(ComparisonResult.id).label('total_differences'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.DIFFERENT.value
                ).label('different_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.SOURCE_ONLY.value
                ).label('source_only_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DifferenceStatus.TARGET_ONLY.value
                ).label('target_only_records')
            ).filter(ComparisonResult.task_id == task_id).first()
            
            if stats:
                return {
                    'total_differences': stats.total_differences,
                    'different_records': stats.different_records,
                    'source_only_records': stats.source_only_records,
                    'target_only_records': stats.target_only_records
                }
            return None
    
    # ==================== 高级查询示例 ====================
    
    def get_user_task_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户任务摘要 - 展示复杂的关联查询"""
        with self.get_db_session() as session:
            # 复杂的关联查询
            summary = session.query(
                func.count(ComparisonTask.id).label('total_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.COMPLETED.value
                ).label('completed_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.RUNNING.value
                ).label('running_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.FAILED.value
                ).label('failed_tasks'),
                func.avg(ComparisonTask.exec_time).label('avg_execution_time'),
                func.sum(ComparisonTask.diff_records).label('total_differences')
            ).filter(ComparisonTask.user_id == user_id).first()
            
            return {
                'total_tasks': summary.total_tasks or 0,
                'completed_tasks': summary.completed_tasks or 0,
                'running_tasks': summary.running_tasks or 0,
                'failed_tasks': summary.failed_tasks or 0,
                'avg_execution_time': float(summary.avg_execution_time or 0),
                'total_differences': summary.total_differences or 0
            }

    # ==================== 任务管理 ====================

    def create_task(self, user_id: str, model_id: int, task_name: str = None,
                   description: str = None, table_rule_id: int = None) -> str:
        """
        创建比对任务 - 支持标准化任务命名

        Args:
            user_id: 用户ID
            model_id: 模型ID
            task_name: 任务名称
            description: 任务描述
            table_rule_id: 表规则ID

        Returns:
            str: 任务ID
        """
        with self.get_db_session() as session:
            # 验证用户存在，如果不存在则自动创建
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                # 自动创建用户
                logger.info(f"用户 {user_id} 不存在，自动创建")
                user = User(
                    user_id=user_id,
                    username=user_id,
                    email=f"{user_id}@system.local",
                    password="auto_created"
                )
                session.add(user)
                session.flush()
                logger.info(f"自动创建用户成功: {user_id}")

            # 如果未指定任务名称或为空，则自动生成
            if not task_name or task_name.strip() == "":
                final_task_name = self._generate_standard_task_name()
                logger.info(f"自动生成标准任务名称: {final_task_name}")
            else:
                final_task_name = task_name.strip()
                logger.info(f"使用用户指定的任务名称: {final_task_name}")

            task_id = str(uuid.uuid4())
            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                model_id=model_id,
                table_rule_id=table_rule_id,
                task_name=final_task_name,
                description=description,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"创建比对任务成功: {final_task_name} ({task_id})")
            return task_id

    def create_task_direct(self, user_name: str, task_data) -> str:
        """直接创建任务"""
        # 提取任务名称，支持标准化命名
        if not task_data.task_name or task_data.task_name.strip() == "":
            task_name = self._generate_standard_task_name()

        # 提取其他参数
        if hasattr(task_data, 'description'):
            description = task_data.description
        else:
            description = task_data.get('description')
        
        user_id = f"u_{hash(user_name) % 1000000:06d}"

        # 使用简化的任务创建逻辑
        with self.get_db_session() as session:
            # 验证用户存在，如果不存在则自动创建
            user = session.query(User).filter_by(username=user_name).first()
            if not user:
                # 自动创建用户
                user = User(
                    user_id=user_id,
                    username=user_name,
                    email=f"{user_name}@system.local",
                    password="auto_created"  # 自动创建的用户使用默认密码
                )
                session.add(user)
                session.flush()  # 确保用户被创建
                logger.info(f"自动创建用户成功: {user_id}")

            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                model_id=None,
                table_rule_id=None,
                task_name=task_name,
                description=description,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"TaskCreateDirect任务创建成功: {task_name} ({task_id})")
            return task_id

    def get_task_summary(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务汇总信息（从service_models.py迁移的业务逻辑）"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return None

            # 计算匹配记录数
            matched_records = (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0)

            return {
                'task_id': task_id,
                'task_name': task.task_name,
                'description': task.description,
                'user_id': task.user_id,
                'model_id': task.model_id,
                'status': task.status,
                'progress_pct': float(task.progress_pct or 0),
                'current_step': task.current_step,
                'create_time': task.create_time,
                'start_time': task.start_time,
                'complete_time': task.complete_time,
                'update_time': task.update_time,
                'total_records': task.total_records or 0,
                'processed_records': task.processed_records or 0,
                'matched_records': max(0, matched_records),
                'diff_records': task.diff_records or 0,
                'source_only': task.source_only or 0,
                'target_only': task.target_only or 0,
                'exec_time': float(task.exec_time or 0),
                'error_msg': task.error_msg,
                'error_details': task.error_details
            }

    def list_connection_configs(self, type: str = None, status: str = None) -> List[Dict[str, Any]]:
        """获取连接配置列表"""
        with self.get_db_session() as session:
            query = session.query(ComparisonConnection)

            if type:
                query = query.filter(ComparisonConnection.type == type.lower())
            if status:
                query = query.filter(ComparisonConnection.status == status)

            connections = query.order_by(desc(ComparisonConnection.create_time)).all()

            return [
                {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database,
                    'status': conn.status,
                    'created_at': conn.create_time.isoformat() if conn.create_time else None
                }
                for conn in connections
            ]

    # ==================== 任务管理功能（整合TaskManager） ====================

    def create_simple_task(self, user_id: str, task_name: str, description: str = None) -> str:
        """
        创建简单任务
        整合TaskManager功能，但使用SQLAlchemy模型
        """
        import uuid

        with self.get_db_session() as session:
            # 创建默认模型和规则（简化处理）
            default_conn = session.query(ComparisonConnection).first()
            if not default_conn:
                # 创建默认连接
                default_conn = ComparisonConnection(
                    name="默认连接",
                    type="db2",
                    host="localhost",
                    port=50000,
                    username="db2inst1",
                    password="password",
                    database="sample",
                    status="active"
                )
                session.add(default_conn)
                session.flush()

            # 创建默认模型
            model = ComparisonModel(
                name=f"模型_{task_name}",
                source_connid=default_conn.id,
                target_connid=default_conn.id,
                cmp_type="content",
                user_id=user_id
            )
            session.add(model)
            session.flush()

            # 创建默认表规则
            table_rule = ComparisonTableRule(
                model_id=model.id,
                table_name=task_name,
                sql_1="SELECT 1",
                sql_2="SELECT 1"
            )
            session.add(table_rule)
            session.flush()

            # 创建任务
            task_id = str(uuid.uuid4())
            task = ComparisonTask(
                task_id=task_id,
                user_id=user_id,
                model_id=model.id,
                table_rule_id=table_rule.id,
                task_name=task_name,
                description=description,
                status=TaskStatus.PENDING.value,
                progress_pct=0.0,
                current_step="任务已创建"
            )

            session.add(task)
            session.flush()

            logger.info(f"创建简单任务成功: {task_name} ({task_id})")
            return task_id

    def update_task_progress_simple(self, task_id: str, progress_pct: float = None,
                                  current_step: str = None, processed_records: int = None,
                                  total_records: int = None) -> bool:
        """
        更新任务进度（简化版，带频率控制）
        整合TaskManager的进度更新功能
        """
        current_time = time.time()

        # 简单的频率控制
        last_update = self._last_progress_update.get(task_id, 0)
        if current_time - last_update < self._progress_update_interval:
            return True  # 跳过更新但返回成功

        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter(
                ComparisonTask.task_id == task_id
            ).first()

            if not task:
                logger.warning(f"任务不存在: {task_id}")
                return False

            # 更新进度字段
            if progress_pct is not None:
                task.progress_pct = min(100.0, max(0.0, progress_pct))
            if current_step is not None:
                task.current_step = current_step
            if processed_records is not None:
                task.processed_records = processed_records
            if total_records is not None:
                task.total_records = total_records

            # 自动计算进度百分比
            if (processed_records is not None and total_records is not None and
                total_records > 0 and progress_pct is None):
                task.progress_pct = min(100.0, (processed_records / total_records) * 100)

            task.update_time = datetime.now()
            session.commit()

            self._last_progress_update[task_id] = current_time
            return True

    def get_task_progress_simple(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度信息（简化版）
        整合TaskManager的任务查询功能
        """
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter(
                ComparisonTask.task_id == task_id
            ).first()

            if not task:
                return None

            return {
                'task_id': task.task_id,
                'task_name': task.task_name,
                'status': task.status,
                'progress_pct': float(task.progress_pct or 0),
                'current_step': task.current_step,
                'processed_records': task.processed_records or 0,
                'total_records': task.total_records or 0,
                'created_at': task.create_time.isoformat() if task.create_time else None,
                'updated_at': task.update_time.isoformat() if task.update_time else None
            }

    def list_tasks_simple(self, user_id: str = None, status: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取任务列表"""
        with self.get_db_session() as session:
            query = session.query(ComparisonTask)

            if user_id:
                query = query.filter(ComparisonTask.user_id == user_id)
            if status:
                query = query.filter(ComparisonTask.status == status)

            tasks = query.order_by(desc(ComparisonTask.create_time)).limit(limit).all()

            return [
                {
                    'task_id': task.task_id,
                    'task_name': task.task_name,
                    'status': task.status,
                    'progress_pct': float(task.progress_pct or 0),
                    'current_step': task.current_step,
                    'created_at': task.create_time.isoformat() if task.create_time else None
                }
                for task in tasks
            ]

    # ==================== 模型管理方法 ====================

    def create_comparison_model(self, name: str, description: str = None,
                               source_connid: int = None, target_connid: int = None,
                               cmp_type: str = "content", global_config: Dict[str, Any] = None) -> int:
        """创建比对模型"""
        try:
            with self.get_db_session() as session:
                # 检查模型名称是否已存在
                existing_model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()
                if existing_model:
                    return existing_model.id

                # 验证连接是否存在
                if source_connid:
                    source_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == source_connid).first()
                    if not source_conn:
                        raise ValueError(f"源连接不存在: {source_connid}")

                if target_connid:
                    target_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == target_connid).first()
                    if not target_conn:
                        raise ValueError(f"目标连接不存在: {target_connid}")

                # 创建比对模型
                model = ComparisonModel(
                    name=name,
                    description=description,
                    source_connid=source_connid,
                    target_connid=target_connid,
                    cmp_type=cmp_type,
                    global_config=global_config or {}
                )

                session.add(model)
                session.commit()

                logger.info(f"比对模型创建成功: {name}, ID: {model.id}")
                return model.id

        except Exception as e:
            logger.error(f"创建比对模型失败: {e}", exc_info=True)
            raise

    def get_comparison_model(self, model_id: int) -> Optional[Dict[str, Any]]:
        """获取比对模型信息"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    return None

                return {
                    'id': model.id,
                    'name': model.name,
                    'description': model.description,
                    'source_connid': model.source_connid,
                    'target_connid': model.target_connid,
                    'cmp_type': model.cmp_type,
                    'global_config': model.global_config,
                    'create_time': model.create_time.isoformat() if model.create_time else None,
                    'update_time': model.update_time.isoformat() if model.update_time else None
                }

        except Exception as e:
            logger.error(f"获取比对模型失败: {model_id}, 错误: {e}")
            return None

    def get_comparison_model_by_name(self, name: str) -> Optional[ComparisonModel]:
        """根据名称查询比对模型"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()

                if model:
                    # 将对象从会话中分离，避免会话关闭后访问问题
                    session.expunge(model)

                return model

        except Exception as e:
            logger.error(f"根据名称查询比对模型失败: {name}, 错误: {e}")
            return None

    def create_table_rule(self, model_id: int, table_id: str = None, table_name: str = None,
                         sql_1: str = None, sql_2: str = None, remark: str = None,
                         primary_keys: List[str] = None, ignore_fields: List[str] = None,
                         field_mappings: Dict[str, str] = None) -> int:
        """创建表规则"""
        try:
            with self.get_db_session() as session:
                # 验证模型是否存在
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()
                if not model:
                    raise ValueError(f"比对模型不存在: {model_id}")

                # 如果没有提供table_id，根据表名和remark生成哈希ID
                if not table_id:
                    table_id = self._generate_table_id(model_id, table_name, remark)

                # 检查table_id在同一模型内是否已存在
                existing_rule = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.table_id == table_id
                ).first()

                if existing_rule:
                    logger.info(f"表规则已存在: {table_id}, ID: {existing_rule.id}")
                    return existing_rule.id

                # 创建表规则
                table_rule = ComparisonTableRule(
                    model_id=model_id,
                    table_id=table_id,
                    table_name=table_name or table_id,
                    sql_1=sql_1,
                    sql_2=sql_2,
                    remark=remark,
                    primary_keys=primary_keys or [],
                    ignore_fields=ignore_fields or [],
                    field_mappings=field_mappings or {}
                )

                session.add(table_rule)
                session.commit()

                logger.info(f"表规则创建成功: {table_name or table_id}, ID: {table_rule.id}")
                return table_rule.id

        except Exception as e:
            logger.error(f"创建表规则失败: {e}", exc_info=True)
            raise

    def _generate_table_id(self, model_id: int, table_name: str, remark: str = None) -> str:
        """根据表名和备注生成唯一的table_id"""
        import hashlib

        # 构建哈希输入字符串
        hash_input = f"{table_name or 'unknown'}"
        if remark:
            hash_input += f"_{remark}"

        # 计算MD5哈希
        hash_object = hashlib.md5(hash_input.encode('utf-8'))
        hash_hex = hash_object.hexdigest()

        # 取前8位作为table_id，并添加模型ID前缀确保跨模型唯一性
        table_id = f"m{model_id}_{hash_hex[:8]}"

        logger.debug(f"生成table_id: '{hash_input}' -> '{table_id}'")
        return table_id

    def get_table_rule(self, rule_id: int) -> Optional[Dict[str, Any]]:
        """获取表规则信息"""
        try:
            with self.get_db_session() as session:
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    return None

                return {
                    'id': rule.id,
                    'model_id': rule.model_id,
                    'table_id': rule.table_id,
                    'table_name': rule.table_name,
                    'sql_1': rule.sql_1,
                    'sql_2': rule.sql_2,
                    'remark': rule.remark,
                    'primary_keys': rule.primary_keys,
                    'ignore_fields': rule.ignore_fields,
                    'field_mappings': rule.field_mappings,
                    'is_active': rule.is_active,
                    'create_time': rule.create_time.isoformat() if rule.create_time else None,
                    'update_time': rule.update_time.isoformat() if rule.update_time else None
                }

        except Exception as e:
            logger.error(f"获取表规则失败: {rule_id}, 错误: {e}")
            return None

    def get_model_table_rules(self, model_id: int) -> List[Dict[str, Any]]:
        """
        获取模型下的所有表规则

        Args:
            model_id: 模型ID

        Returns:
            List[Dict[str, Any]]: 表规则列表
        """
        try:
            with self.get_db_session() as session:
                rules = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.is_active == True
                ).all()

                rules_list = []
                for rule in rules:
                    rules_list.append({
                        'id': rule.id,
                        'table_id': rule.table_id,
                        'table_name': rule.table_name,
                        'sql_1': rule.sql_1,
                        'sql_2': rule.sql_2,
                        'remark': rule.remark,
                        'primary_keys': rule.primary_keys,
                        'ignore_fields': rule.ignore_fields,
                        'field_mappings': rule.field_mappings,
                        'is_active': rule.is_active,
                        'create_time': rule.create_time.isoformat() if rule.create_time else None,
                        'update_time': rule.update_time.isoformat() if rule.update_time else None
                    })

                return rules_list

        except Exception as e:
            logger.error(f"获取模型表规则失败: {model_id}, 错误: {e}")
            return []

    # ==================== 删除操作方法 ====================

    def delete_task(self, task_id: str) -> bool:
        """删除任务及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找任务
                task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

                if not task:
                    logger.warning(f"任务不存在: {task_id}")
                    return False

                # 如果任务正在运行，先更新状态为取消
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
                    task.update_time = datetime.now()
                    session.commit()
                    logger.info(f"任务 {task_id} 已取消")

                # 级联删除会自动删除相关的ComparisonResult记录
                session.delete(task)
                session.commit()

                logger.info(f"任务删除成功: {task_id}")
                return True

        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {e}", exc_info=True)
            return False

    def delete_user(self, user_id: str) -> bool:
        """删除用户及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找用户
                user = session.query(User).filter(User.user_id == user_id).first()

                if not user:
                    logger.warning(f"用户不存在: {user_id}")
                    return False

                # 级联删除会自动删除相关的任务、会话等记录
                session.delete(user)
                session.commit()

                logger.info(f"用户删除成功: {user_id}")
                return True

        except Exception as e:
            logger.error(f"删除用户失败: {user_id}, 错误: {e}", exc_info=True)
            return False

    def delete_comparison_model(self, model_id: int) -> bool:
        """删除比对模型及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找模型
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    logger.warning(f"比对模型不存在: {model_id}")
                    return False

                # 删除模型（级联删除会自动删除相关的表规则和任务）
                session.delete(model)
                session.commit()

                logger.info(f"比对模型删除成功: {model_id}")
                return True

        except Exception as e:
            logger.error(f"删除比对模型失败: {model_id}, 错误: {e}", exc_info=True)
            return False

    def delete_database_connection(self, connection_id: int) -> bool:
        """删除数据库连接配置"""
        try:
            with self.get_db_session() as session:
                # 查找连接
                connection = session.query(ComparisonConnection).filter(
                    ComparisonConnection.id == connection_id
                ).first()

                if not connection:
                    logger.warning(f"数据库连接不存在: {connection_id}")
                    return False

                # 检查是否有模型在使用此连接
                models_using_connection = session.query(ComparisonModel).filter(
                    (ComparisonModel.source_connid == connection_id) |
                    (ComparisonModel.target_connid == connection_id)
                ).count()

                if models_using_connection > 0:
                    logger.warning(f"无法删除连接 {connection_id}: 仍有 {models_using_connection} 个模型在使用")
                    return False

                # 删除连接
                session.delete(connection)
                session.commit()

                logger.info(f"数据库连接删除成功: {connection_id}")
                return True

        except Exception as e:
            logger.error(f"删除数据库连接失败: {connection_id}, 错误: {e}", exc_info=True)
            return False

    def delete_table_rule(self, rule_id: int) -> bool:
        """删除表规则"""
        try:
            with self.get_db_session() as session:
                # 查找规则
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    logger.warning(f"表规则不存在: {rule_id}")
                    return False

                # 级联删除会自动处理相关任务
                session.delete(rule)
                session.commit()

                logger.info(f"表规则删除成功: {rule_id}")
                return True

        except Exception as e:
            logger.error(f"删除表规则失败: {rule_id}, 错误: {e}", exc_info=True)
            return False

    def cleanup_old_tasks(self, days_old: int = 30, status_filter: List[str] = None) -> int:
        """清理旧任务数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            with self.get_db_session() as session:
                query = session.query(ComparisonTask).filter(ComparisonTask.create_time < cutoff_date)

                # 应用状态过滤器
                if status_filter:
                    query = query.filter(ComparisonTask.status.in_(status_filter))
                else:
                    # 默认只删除已完成、失败或取消的任务
                    query = query.filter(ComparisonTask.status.in_([
                        TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED
                    ]))

                # 获取要删除的任务
                old_tasks = query.all()
                deleted_count = len(old_tasks)

                if deleted_count == 0:
                    logger.info("没有找到需要清理的旧任务")
                    return 0

                # 批量删除
                for task in old_tasks:
                    session.delete(task)

                session.commit()

                logger.info(f"清理旧任务完成: 删除了 {deleted_count} 个任务")
                return deleted_count

        except Exception as e:
            logger.error(f"清理旧任务失败: {e}", exc_info=True)
            return 0
