/**
 * 数据库元数据查询API
 *
 * 提供数据库元数据查询功能，包括数据库列表、表列表、表结构、SQL执行等
 * 重构版本 - 统一使用DatabaseApiClient和标准化端点
 */

import { databaseApiClient, withDatabaseErrorHandling } from './client';
import { METADATA_ENDPOINTS } from './endpoints';
import type { BaseApiResponse } from './types';
import type { SqlExecuteParams, TableDataParams } from "@/types/database";
import {
  DatabaseErrorCode,
  DatabaseApiError,
  createDatabaseError
} from './error';

/**
 * 获取数据源列表
 */
export const getDatasourceList = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<Array<{
    id: string;
    name: string;
    type: string;
    status: string;
    createTime: string;
  }>>> => {
    return databaseApiClient.get(METADATA_ENDPOINTS.DATASOURCES, {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取数据源详情
 * @param datasourceId - 数据源ID
 */
export const getDatasourceDetail = withDatabaseErrorHandling(
  async (datasourceId: string): Promise<BaseApiResponse<{
    id: string;
    name: string;
    type: string;
    host: string;
    port: number;
    database?: string;
    status: string;
    createTime: string;
    updateTime: string;
  }>> => {
    if (!datasourceId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID不能为空'
      );
    }

    return databaseApiClient.get(METADATA_ENDPOINTS.DATASOURCE_DETAIL(datasourceId), {
      useCache: true,
      cacheTTL: 600000 // 10分钟缓存
    });
  }
);

/**
 * 获取数据库列表
 * @param datasourceId - 数据源ID
 */
export const getDatabaseList = withDatabaseErrorHandling(
  async (datasourceId: string): Promise<BaseApiResponse<string[]>> => {
    if (!datasourceId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID不能为空'
      );
    }

    return databaseApiClient.get<string[]>(METADATA_ENDPOINTS.DATABASES(datasourceId), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取表列表
 * @param datasourceId - 数据源ID
 * @param database - 数据库名称
 * @param params - 查询参数
 */
export const getTableList = withDatabaseErrorHandling(
  async (
    datasourceId: string,
    database: string,
    params?: {
      search?: string;
      type?: string;
      page?: number;
      size?: number;
    }
  ): Promise<BaseApiResponse<Array<{
    name: string;
    type: string;
    comment?: string;
    rowCount?: number;
    createTime?: string;
    updateTime?: string;
  }>>> => {
    if (!datasourceId || !database) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID和数据库名称不能为空'
      );
    }

    return databaseApiClient.get(METADATA_ENDPOINTS.TABLES(datasourceId, database), {
      params,
      useCache: true,
      cacheTTL: 180000 // 3分钟缓存
    });
  }
);

/**
 * 获取表结构
 * @param datasourceId - 数据源ID
 * @param database - 数据库名称
 * @param table - 表名称
 */
export const getTableSchema = withDatabaseErrorHandling(
  async (
    datasourceId: string,
    database: string,
    table: string
  ): Promise<BaseApiResponse<{
    columns: Array<{
      name: string;
      type: string;
      nullable: boolean;
      primaryKey: boolean;
      defaultValue?: any;
      comment?: string;
    }>;
    indexes: Array<{
      name: string;
      columns: string[];
      unique: boolean;
    }>;
    primaryKey?: string[];
  }>> => {
    if (!datasourceId || !database || !table) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID、数据库名称和表名称不能为空'
      );
    }

    return databaseApiClient.get(METADATA_ENDPOINTS.TABLE_SCHEMA(datasourceId, database, table), {
      useCache: true,
      cacheTTL: 600000 // 10分钟缓存，表结构变化较少
    });
  }
);

/**
 * 获取表数据
 * @param datasourceId - 数据源ID
 * @param database - 数据库名称
 * @param table - 表名称
 * @param params - 查询参数
 */
export const getTableData = withDatabaseErrorHandling(
  async (
    datasourceId: string,
    database: string,
    table: string,
    params?: TableDataParams
  ): Promise<BaseApiResponse<{
    columns: Array<{
      name: string;
      type: string;
    }>;
    rows: any[][];
    total?: number;
    page?: number;
    pageSize?: number;
  }>> => {
    if (!datasourceId || !database || !table) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID、数据库名称和表名称不能为空'
      );
    }

    return databaseApiClient.get(METADATA_ENDPOINTS.TABLE_DATA(datasourceId, database, table), {
      params
    });
  }
);

/**
 * 获取表行数统计
 * @param datasourceId - 数据源ID
 * @param database - 数据库名称
 * @param table - 表名称
 */
export const getTableCount = withDatabaseErrorHandling(
  async (
    datasourceId: string,
    database: string,
    table: string
  ): Promise<BaseApiResponse<{
    count: number;
    estimatedCount?: number;
    isExact: boolean;
  }>> => {
    if (!datasourceId || !database || !table) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID、数据库名称和表名称不能为空'
      );
    }

    return databaseApiClient.get(METADATA_ENDPOINTS.TABLE_COUNT(datasourceId, database, table), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 执行SQL查询
 * @param datasourceId - 数据源ID
 * @param data - SQL查询数据
 */
export const executeSql = withDatabaseErrorHandling(
  async (
    datasourceId: string,
    data: SqlExecuteParams
  ): Promise<BaseApiResponse<{
    columns: Array<{
      name: string;
      type: string;
    }>;
    rows: any[][];
    affectedRows?: number;
    executionTime?: number;
    queryId?: string;
  }>> => {
    if (!datasourceId || !data.sql) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID和SQL语句不能为空'
      );
    }

    if (data.sql.trim().length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        'SQL语句不能为空'
      );
    }

    return databaseApiClient.post(`/database/meta/${datasourceId}/execute/`, data, {
      timeout: data.timeout || 30000,
      showErrorMessage: true
    });
  }
);

/**
 * 获取数据库对象类型
 * @param datasourceId - 数据源ID
 */
export const getObjectTypes = withDatabaseErrorHandling(
  async (datasourceId: string): Promise<BaseApiResponse<Array<{
    id: string;
    name: string;
    description?: string;
  }>>> => {
    if (!datasourceId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID不能为空'
      );
    }

    return databaseApiClient.get(`/database/meta/${datasourceId}/object-types/`, {
      useCache: true,
      cacheTTL: 3600000 // 1小时缓存，对象类型变化很少
    });
  }
);

/**
 * 获取可用的过滤条件类型
 */
export const getFilterTypes = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<Array<{
    id: string;
    name: string;
    applicableTypes: string[];
    operators: string[];
  }>>> => {
    return databaseApiClient.get('/database/meta/filter-types/', {
      useCache: true,
      cacheTTL: 3600000 // 1小时缓存
    });
  }
);

/**
 * 验证SQL语句
 * @param data - SQL语句和数据源信息
 */
export const validateSql = withDatabaseErrorHandling(
  async (data: {
    datasourceId: string;
    sql: string;
    database?: string;
  }): Promise<BaseApiResponse<{
    valid: boolean;
    message?: string;
    suggestions?: string[];
    errors?: Array<{
      line: number;
      column: number;
      message: string;
    }>;
  }>> => {
    if (!data.datasourceId || !data.sql) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID和SQL语句不能为空'
      );
    }

    if (data.sql.trim().length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        'SQL语句不能为空'
      );
    }

    return databaseApiClient.post('/database/meta/validate-sql/', data);
  }
);

/**
 * 执行SQL预览查询
 * @param data - SQL语句和数据源信息
 */
export const previewSql = withDatabaseErrorHandling(
  async (data: {
    datasourceId: string;
    sql: string;
    database?: string;
    limit?: number;
  }): Promise<BaseApiResponse<{
    columns: Array<{
      name: string;
      type: string;
    }>;
    rows: any[][];
    totalRows?: number;
    executionTime?: number;
  }>> => {
    if (!data.datasourceId || !data.sql) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID和SQL语句不能为空'
      );
    }

    if (data.sql.trim().length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        'SQL语句不能为空'
      );
    }

    // 设置默认预览限制
    const requestData = {
      ...data,
      limit: data.limit || 100 // 默认预览100行
    };

    return databaseApiClient.post('/database/meta/preview-sql/', requestData, {
      timeout: 15000 // 预览查询超时15秒
    });
  }
);

/**
 * 获取表之间的关联信息
 * @param datasourceId - 数据源ID
 * @param databaseName - 数据库名
 */
export const getTableRelations = withDatabaseErrorHandling(
  async (datasourceId: string, databaseName: string): Promise<BaseApiResponse<Array<{
    sourceTable: string;
    sourceColumn: string;
    targetTable: string;
    targetColumn: string;
    relationName?: string;
    relationType: 'FOREIGN_KEY' | 'REFERENCE' | 'DEPENDENCY';
  }>>> => {
    if (!datasourceId || !databaseName) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '数据源ID和数据库名称不能为空'
      );
    }

    return databaseApiClient.get('/database/meta/relations/', {
      params: { datasourceId, databaseName },
      useCache: true,
      cacheTTL: 600000 // 10分钟缓存
    });
  }
);

/**
 * 获取支持的数据类型映射关系
 */
export const getDataTypeMappings = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<Record<string, {
    targetTypes: Record<string, {
      conversion: string;
      compatible: boolean;
      precision?: boolean;
      scale?: boolean;
    }>;
  }>>> => {
    return databaseApiClient.get('/database/meta/type-mappings/', {
      useCache: true,
      cacheTTL: 3600000 // 1小时缓存，类型映射变化很少
    });
  }
);



// ===== 批量操作和优化功能 =====

/**
 * 批量获取表结构
 * @param datasourceId - 数据源ID
 * @param database - 数据库名称
 * @param tables - 表名列表
 */
export const batchGetTableSchemas = withDatabaseErrorHandling(
  async (datasourceId: string, database: string, tables: string[]): Promise<BaseApiResponse<{
    schemas: Record<string, any>;
    errors: Record<string, string>;
    total: number;
    successCount: number;
    errorCount: number;
  }>> => {
    if (!tables || tables.length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '表名列表不能为空'
      );
    }

    if (tables.length > 100) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '批量获取表结构数量不能超过100个'
      );
    }

    const results = await Promise.allSettled(
      tables.map(table => getTableSchema(datasourceId, database, table))
    );

    const schemas: Record<string, any> = {};
    const errors: Record<string, string> = {};

    results.forEach((result, index) => {
      const tableName = tables[index];
      if (result.status === 'fulfilled') {
        schemas[tableName] = result.value.data;
      } else {
        errors[tableName] = result.reason?.message || '获取失败';
      }
    });

    return {
      success: true,
      message: '批量获取表结构完成',
      code: 200,
      timestamp: new Date().toISOString(),
      data: {
        schemas,
        errors,
        total: tables.length,
        successCount: Object.keys(schemas).length,
        errorCount: Object.keys(errors).length
      }
    };
  }
);