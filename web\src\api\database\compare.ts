/**
 * 比较任务API
 *
 * 提供数据库比较任务的创建、管理、状态查询等功能
 * 重构版本 - 统一使用DatabaseApiClient和标准化端点
 */

import { databaseApiClient, withDatabaseErrorHandling } from './client';
import { COMPARE_ENDPOINTS, RESULT_ENDPOINTS } from './endpoints';
import type { BaseApiResponse } from './types';
import type {
  CompareModel,
  CompareModelQuery,
  CompareTask,
  PreCheckParams,
  StartTaskParams,
  CompareResult,
  CompareTaskQuery,
  CompareTaskRequest
} from "@/types/database";

/**
 * 获取比较模型列表
 * @param params - 查询参数
 */
export const getCompareModels = withDatabaseErrorHandling(
  async (params?: CompareModelQuery): Promise<BaseApiResponse<CompareModel[]>> => {
    console.log('🌐 getCompareModels API被调用');
    console.log('📋 请求参数:', params);
    console.log('🔗 请求端点:', COMPARE_ENDPOINTS.MODELS);
    console.log('⏰ 调用时间:', new Date().toISOString());
    console.log('📍 调用栈:', new Error().stack);

    const result = await databaseApiClient.get<CompareModel[]>(COMPARE_ENDPOINTS.MODELS, {
      params,
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });

    console.log('✅ getCompareModels API响应:', result);
    return result;
  }
);

/**
 * 获取比较模型详情
 * @param id - 模型ID
 */
export const getCompareModel = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<CompareModel>> => {
    if (!id) {
      throw new Error('模型ID不能为空');
    }

    return databaseApiClient.get<CompareModel>(COMPARE_ENDPOINTS.MODEL_DETAIL(id), {
      useCache: true,
      cacheTTL: 600000 // 10分钟缓存
    });
  }
);

/**
 * 创建比较模型
 * @param data - 模型数据
 */
export const createCompareModel = withDatabaseErrorHandling(
  async (data: CompareModel): Promise<BaseApiResponse<CompareModel>> => {
    if (!data.name) {
      throw new Error('模型名称不能为空');
    }

    const response = await databaseApiClient.post<CompareModel>(COMPARE_ENDPOINTS.MODELS, data, {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除模型列表缓存
    databaseApiClient.clearCache('model');

    return response;
  }
);

/**
 * 更新比较模型
 * @param data - 模型数据
 */
export const updateCompareModel = withDatabaseErrorHandling(
  async (data: CompareModel): Promise<BaseApiResponse<CompareModel>> => {
    if (!data.id) {
      throw new Error('模型ID不能为空');
    }

    const response = await databaseApiClient.put<CompareModel>(
      COMPARE_ENDPOINTS.MODEL_DETAIL(data.id),
      data,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除相关缓存
    databaseApiClient.clearCache('model');

    return response;
  }
);

/**
 * 删除比较模型
 * @param id - 模型ID
 */
export const deleteCompareModel = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<null>> => {
    if (!id) {
      throw new Error('模型ID不能为空');
    }

    const response = await databaseApiClient.delete<null>(COMPARE_ENDPOINTS.MODEL_DETAIL(id), {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除相关缓存
    databaseApiClient.clearCache('model');

    return response;
  }
);

/**
 * 复制比较模型
 * @param id - 模型ID
 * @param data - 复制数据
 */
export const cloneCompareModel = withDatabaseErrorHandling(
  async (id: string, data?: {
    name?: string;
    description?: string;
  }): Promise<BaseApiResponse<CompareModel>> => {
    if (!id) {
      throw new Error('模型ID不能为空');
    }

    const response = await databaseApiClient.post<CompareModel>(
      `${COMPARE_ENDPOINTS.MODEL_DETAIL(id)}clone/`,
      data,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除模型列表缓存
    databaseApiClient.clearCache('model');

    return response;
  }
);

/**
 * 创建比较任务
 * @param data - 任务数据
 */
export const createCompareTask = withDatabaseErrorHandling(
  async (data: CompareTaskRequest): Promise<BaseApiResponse<CompareTask>> => {
    if (!data.model) {
      throw new Error('比较模型不能为空');
    }

    const response = await databaseApiClient.post<CompareTask>(COMPARE_ENDPOINTS.TASKS, data, {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除任务列表缓存
    databaseApiClient.clearCache('task');

    return response;
  }
);

/**
 * 预检查比对任务配置
 * @param params - 预检查参数
 */
export const preCheckCompareTask = withDatabaseErrorHandling(
  async (params: PreCheckParams): Promise<BaseApiResponse<{
    valid: boolean;
    messages: string[];
  }>> => {
    return databaseApiClient.post(COMPARE_ENDPOINTS.PRECHECK, params, {
      showErrorMessage: true
    });
  }
);

/**
 * 直接执行数据比对
 * @param data - 比对请求数据
 */
export const executeDirectComparison = withDatabaseErrorHandling(
  async (data: CompareTaskRequest): Promise<BaseApiResponse<any>> => {
    if (!data.model) {
      throw new Error('比较模型不能为空');
    }

    return databaseApiClient.post(COMPARE_ENDPOINTS.DIRECT_COMPARISON, data, {
      showSuccessMessage: true,
      showErrorMessage: true,
      timeout: 60000 // 直接比对可能需要更长时间
    });
  }
);

/**
 * 获取任务状态
 * @param taskId - 任务ID
 */
export const getTaskStatus = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<any>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get(COMPARE_ENDPOINTS.TASK_STATUS(taskId), {
      showErrorMessage: false // 状态查询失败不显示错误消息
    });
  }
);

/**
 * 获取任务结果
 * @param taskId - 任务ID
 * @param params - 查询参数
 */
export const getTaskResult = withDatabaseErrorHandling(
  async (
    taskId: string,
    params?: {
      page?: number;
      page_size?: number;
      include_details?: boolean;
    }
  ): Promise<BaseApiResponse<CompareResult>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get(`${COMPARE_ENDPOINTS.TASK_DETAIL(taskId)}/result`, {
      params,
      showErrorMessage: true,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 开始比对任务
 * @param params - 启动参数
 */
export const startCompareTask = withDatabaseErrorHandling(
  async (params: StartTaskParams): Promise<BaseApiResponse<CompareTask>> => {
    if (!params.model) {
      throw new Error('比较模型不能为空');
    }

    const response = await databaseApiClient.post<CompareTask>(
      `${COMPARE_ENDPOINTS.TASKS}/start`,
      params,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除任务相关缓存
    databaseApiClient.clearCache('task');

    return response;
  }
);

/**
 * @deprecated 使用 getTaskStatus 替代
 * 获取比对任务状态（旧版本，保持向后兼容）
 */
export function getCompareTaskStatus(taskId: string) {
  return getTaskStatus(taskId);
}

/**
 * 控制比对任务
 * @param taskId - 任务ID
 * @param action - 控制动作
 */
export const controlCompareTask = withDatabaseErrorHandling(
  async (taskId: string, action: 'pause' | 'resume' | 'stop'): Promise<BaseApiResponse<CompareTask>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    if (!['pause', 'resume', 'stop'].includes(action)) {
      throw new Error('无效的控制动作');
    }

    const response = await databaseApiClient.post<CompareTask>(
      COMPARE_ENDPOINTS.TASK_CONTROL(taskId),
      { action },
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除任务状态缓存
    databaseApiClient.clearCache(`task_${taskId}`);

    return response;
  }
);

/**
 * 停止比对任务
 * @param taskId - 任务ID
 */
export const stopCompareTask = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<CompareTask>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    const response = await databaseApiClient.post<CompareTask>(
      `${COMPARE_ENDPOINTS.TASK_DETAIL(taskId)}/stop`,
      {},
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除任务状态缓存
    databaseApiClient.clearCache(`task_${taskId}`);

    return response;
  }
);

/**
 * 创建并立即启动比较任务
 * @param data - 任务数据
 */
export const launchCompareTask = withDatabaseErrorHandling(
  async (data: CompareTaskRequest): Promise<BaseApiResponse<CompareTask>> => {
    if (!data.model) {
      throw new Error('比较模型不能为空');
    }

    const response = await databaseApiClient.post<CompareTask>(
      COMPARE_ENDPOINTS.LAUNCH_TASK,
      data,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除任务列表缓存
    databaseApiClient.clearCache('task');

    return response;
  }
);

/**
 * 取消比较任务
 * @param id - 任务ID
 */
export const cancelCompareTask = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<null>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    const response = await databaseApiClient.post<null>(
      `${COMPARE_ENDPOINTS.TASK_DETAIL(id)}/cancel`,
      {},
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除任务相关缓存
    databaseApiClient.clearCache('task');

    return response;
  }
);

/**
 * @deprecated 使用 getTaskResult 替代
 * 获取比较任务结果（旧版本，保持向后兼容）
 * @param id - 任务ID
 */
export function getCompareTaskResult(id: string, params?: any) {
  return getTaskResult(id, params);
}

/**
 * 获取比较任务详情
 * @param id - 任务ID
 */
export const getCompareTask = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<CompareTask>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get<CompareTask>(COMPARE_ENDPOINTS.TASK_DETAIL(id), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取比较任务历史列表
 * @param params - 查询参数
 */
export const getCompareTaskHistory = withDatabaseErrorHandling(
  async (params?: CompareTaskQuery): Promise<BaseApiResponse<CompareTask[]>> => {
    return databaseApiClient.get<CompareTask[]>(COMPARE_ENDPOINTS.TASKS, {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 删除比较任务
 * @param id - 任务ID
 */
export const deleteCompareTask = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<null>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    const response = await databaseApiClient.delete<null>(COMPARE_ENDPOINTS.TASK_DETAIL(id), {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除任务相关缓存
    databaseApiClient.clearCache('task');

    return response;
  }
);

/**
 * 获取比较任务日志
 * @param id - 任务ID
 * @param params - 查询参数
 */
export const getCompareTaskLogs = withDatabaseErrorHandling(
  async (id: string, params?: {
    page?: number;
    size?: number;
  }): Promise<BaseApiResponse<{
    logs: string[];
    total: number;
  }>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get(`${COMPARE_ENDPOINTS.TASK_DETAIL(id)}/logs`, {
      params
    });
  }
);

/**
 * 获取比较任务结果摘要
 * @param id - 任务ID
 */
export const getCompareTaskSummary = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<{
    total: number;
    matched: number;
    mismatched: number;
    details: any;
  }>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get(`${COMPARE_ENDPOINTS.TASK_DETAIL(id)}/summary`, {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取比较任务结果详情
 * @param id - 任务ID
 * @param params - 查询参数
 */
export const getCompareTaskResults = withDatabaseErrorHandling(
  async (id: string, params?: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
  }): Promise<BaseApiResponse<{
    results: any[];
    total: number;
  }>> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get(`${COMPARE_ENDPOINTS.TASK_DETAIL(id)}/results`, {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 获取最近的比较任务列表
 * @param params - 查询参数
 */
export const getRecentCompareTasks = withDatabaseErrorHandling(
  async (params?: {
    limit?: number;
  }): Promise<BaseApiResponse<CompareTask[]>> => {
    return databaseApiClient.get(`${COMPARE_ENDPOINTS.TASKS}/recent`, {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 导出比较任务结果
 * @param id - 任务ID
 * @param format - 导出格式
 */
export const exportCompareTaskResult = withDatabaseErrorHandling(
  async (id: string, format: string = "json"): Promise<Blob> => {
    if (!id) {
      throw new Error('任务ID不能为空');
    }

    const response = await databaseApiClient.get(`${COMPARE_ENDPOINTS.TASK_DETAIL(id)}/export`, {
      params: { format },
      responseType: "blob"
    });

    return response as any; // Blob类型特殊处理
  }
);

/**
 * 终止任务状态轮询
 * 通知服务器不再轮询指定任务的状态
 * @param taskId - 任务ID
 */
export const stopTaskPolling = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<any>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.post(`${COMPARE_ENDPOINTS.TASK_DETAIL(taskId)}/stop_polling`, {});
  }
);

// ===== 新增的比对结果API函数 =====

// 比对结果摘要类型
export interface ComparisonResultSummary {
  taskId: string;
  name: string;
  status: string;
  summary: {
    totalRecords: number;
    matchedRecords: number;
    diffRecords: number;
    sourceOnlyRecords: number;
    targetOnlyRecords: number;
    errorRecords: number;
    executionTime: number;
  };
  startTime: string | null;
  endTime: string | null;
}

// 差异记录类型
export interface DifferenceRecord {
  id: number;
  tableName: string;
  fieldName: string;
  recordId: string;
  diffType: string;
  severity: string;
  sourceValue: any;
  targetValue: any;
  context: any;
  createTime: string;
}

// 源独有记录类型
export interface SourceOnlyRecord {
  id: number;
  tableName: string;
  recordId: string;
  data: any;
  reason: string;
  createTime: string;
}

// 目标独有记录类型
export interface TargetOnlyRecord {
  id: number;
  tableName: string;
  recordId: string;
  data: any;
  reason: string;
  createTime: string;
}

// 分页查询参数
export interface PaginationParams {
  page?: number;
  size?: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  results: T[];
  total: number;
  page: number;
  size: number;
}

/**
 * 获取比对结果摘要
 * @param taskId - 任务ID
 * @returns 比对结果摘要
 */
export const getComparisonResult = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<ComparisonResultSummary>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get<ComparisonResultSummary>(RESULT_ENDPOINTS.RESULT(taskId), {
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 获取差异记录列表
 * @param taskId - 任务ID
 * @param params - 分页参数
 * @returns 差异记录列表
 */
export const getDifferences = withDatabaseErrorHandling(
  async (taskId: string, params?: PaginationParams): Promise<BaseApiResponse<PaginatedResponse<DifferenceRecord>>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get<PaginatedResponse<DifferenceRecord>>(RESULT_ENDPOINTS.DIFFERENCES(taskId), {
      params,
      useCache: true,
      cacheTTL: 30000 // 30秒缓存
    });
  }
);

/**
 * 获取源独有记录列表
 * @param taskId - 任务ID
 * @param params - 分页参数
 * @returns 源独有记录列表
 */
export const getSourceOnlyRecords = withDatabaseErrorHandling(
  async (taskId: string, params?: PaginationParams): Promise<BaseApiResponse<PaginatedResponse<SourceOnlyRecord>>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get<PaginatedResponse<SourceOnlyRecord>>(RESULT_ENDPOINTS.SOURCE_ONLY(taskId), {
      params,
      useCache: true,
      cacheTTL: 30000 // 30秒缓存
    });
  }
);

/**
 * 获取目标独有记录列表
 * @param taskId - 任务ID
 * @param params - 分页参数
 * @returns 目标独有记录列表
 */
export const getTargetOnlyRecords = withDatabaseErrorHandling(
  async (taskId: string, params?: PaginationParams): Promise<BaseApiResponse<PaginatedResponse<TargetOnlyRecord>>> => {
    if (!taskId) {
      throw new Error('任务ID不能为空');
    }

    return databaseApiClient.get<PaginatedResponse<TargetOnlyRecord>>(RESULT_ENDPOINTS.TARGET_ONLY(taskId), {
      params,
      useCache: true,
      cacheTTL: 30000 // 30秒缓存
    });
  }
);