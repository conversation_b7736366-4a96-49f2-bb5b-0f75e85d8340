"""
WebSocket消费者(Consumer)

处理WebSocket连接和消息，实现任务状态的实时推送。
"""
import json
import logging
import asyncio
import traceback
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from .models import CompareTask
from .serializers import CompareTaskSerializer
from utils.db_utils import get_db_agent_client
from utils.error_report import ErrorReport
from utils.error_stats import error_stats
from utils.websocket_manager import websocket_manager

logger = logging.getLogger(__name__)

class TaskStatusConsumer(AsyncWebsocketConsumer):
    """
    任务状态WebSocket消费者

    处理单个任务的状态更新，当任务状态变化时向客户端推送最新状态。
    """

    async def connect(self):
        """处理WebSocket连接请求"""
        try:
            # 获取任务ID
            self.task_id = self.scope['url_route']['kwargs']['task_id']
            self.task_group_name = f'task_{self.task_id}'

            # 获取用户信息（可能是匿名用户）
            user = self.scope.get("user")
            logger.debug(f"WebSocket连接用户: {user}, 任务ID: {self.task_id}")

            # 验证任务是否存在
            try:
                task = await self.get_task(self.task_id)
                if not task:
                    logger.warning(f"任务不存在，拒绝WebSocket连接: task_id={self.task_id}")
                    await self.close()
                    return
            except Exception as e:
                logger.error(f"获取任务失败: {str(e)}")
                # 记录错误
                error_report = ErrorReport.from_exception(e, self.task_id, {
                    "consumer": "TaskStatusConsumer",
                    "method": "connect",
                    "channel_name": self.channel_name
                })
                error_stats.add_error(error_report)
                await self.close()
                return

            # 将当前连接加入到任务组
            await self.channel_layer.group_add(
                self.task_group_name,
                self.channel_name
            )

            # 注册WebSocket连接
            websocket_manager.register_connection(
                self.channel_name,
                self.task_group_name,
                self.task_id
            )

            # 接受WebSocket连接
            await self.accept()

            # 发送当前任务状态
            await self.send_task_status(task)

            # 如果任务正在运行，启动状态轮询
            if task.status in ['waiting', 'running', 'paused']:
                self.polling_task = asyncio.create_task(self.poll_task_status())

            logger.info(f"WebSocket连接已建立: task_id={self.task_id}, channel={self.channel_name}, user={user}")
        except Exception as e:
            logger.error(f"WebSocket连接处理异常: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, getattr(self, 'task_id', None), {
                "consumer": "TaskStatusConsumer",
                "method": "connect",
                "channel_name": getattr(self, 'channel_name', 'unknown')
            })
            error_stats.add_error(error_report)
            await self.close()

    async def disconnect(self, close_code):
        """处理WebSocket断开连接"""
        try:
            # 取消轮询任务
            if hasattr(self, 'polling_task') and not self.polling_task.done():
                self.polling_task.cancel()
                try:
                    await self.polling_task
                except asyncio.CancelledError:
                    pass

            # 将当前连接从任务组中移除
            await self.channel_layer.group_discard(
                self.task_group_name,
                self.channel_name
            )

            # 注销WebSocket连接
            websocket_manager.unregister_connection(self.channel_name)

            logger.info(f"WebSocket连接已断开: task_id={self.task_id}, channel={self.channel_name}, code={close_code}")
        except Exception as e:
            logger.error(f"WebSocket断开连接处理异常: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, getattr(self, 'task_id', None), {
                "consumer": "TaskStatusConsumer",
                "method": "disconnect",
                "channel_name": getattr(self, 'channel_name', 'unknown'),
                "close_code": close_code
            })
            error_stats.add_error(error_report)

    async def receive(self, text_data):
        """处理从客户端接收的消息"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'ping':
                # 处理心跳消息
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': data.get('timestamp') or timezone.now().isoformat()
                }))
            elif message_type == 'get_status':
                # 处理获取状态请求
                task = await self.get_task(self.task_id)
                if task:
                    await self.send_task_status(task)
            elif message_type == 'stop_polling':
                # 处理停止轮询请求
                if hasattr(self, 'polling_task') and not self.polling_task.done():
                    self.polling_task.cancel()
                    try:
                        await self.polling_task
                    except asyncio.CancelledError:
                        pass
                    await self.send(text_data=json.dumps({
                        'type': 'polling_stopped',
                        'message': '已停止轮询',
                        'timestamp': timezone.now().isoformat()
                    }))
            else:
                # 未知消息类型
                logger.warning(f"收到未知类型的消息: {message_type}, task_id={self.task_id}")
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'未知消息类型: {message_type}',
                    'timestamp': timezone.now().isoformat()
                }))
        except json.JSONDecodeError as e:
            logger.warning(f"收到无效的JSON消息: {text_data[:100]}, task_id={self.task_id}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, self.task_id, {
                "consumer": "TaskStatusConsumer",
                "method": "receive",
                "channel_name": self.channel_name,
                "text_data": text_data[:200]
            })
            error_stats.add_error(error_report)
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': '无效的JSON数据',
                'timestamp': timezone.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, self.task_id, {
                "consumer": "TaskStatusConsumer",
                "method": "receive",
                "channel_name": self.channel_name,
                "text_data": text_data[:200] if 'text_data' in locals() else None
            })
            error_stats.add_error(error_report)
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'处理消息时发生错误: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def task_status(self, event):
        """处理任务状态更新事件"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            # 将任务状态发送给WebSocket客户端
            await self.send(text_data=json.dumps(event['content']))
        except Exception as e:
            logger.error(f"发送任务状态更新时发生错误: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, self.task_id, {
                "consumer": "TaskStatusConsumer",
                "method": "task_status",
                "channel_name": self.channel_name,
                "event_type": event.get('type', 'unknown')
            })
            error_stats.add_error(error_report)

    async def poll_task_status(self):
        """轮询任务状态"""
        try:
            while True:
                # 获取最新任务状态
                task = await self.get_task(self.task_id)
                if not task:
                    logger.warning(f"任务不存在，停止轮询: task_id={self.task_id}")
                    break

                # 检查任务是否已完成（终态）
                terminal_states = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
                if task.status in terminal_states:
                    logger.info(f"任务已完成，停止轮询: task_id={self.task_id}, status={task.status}")
                    await self.send_task_status(task)
                    # 广播任务完成通知到所有客户端
                    await self.broadcast_task_completion(task)
                    break

                # 如果任务有外部ID并且状态是运行中，从DB-Agent获取最新状态
                if task.external_id and task.status in ['waiting', 'running', 'paused']:
                    updated = await self.update_task_from_agent(task)
                    if updated:
                        # 获取更新后的任务
                        task = await self.get_task(self.task_id)
                        # 再次检查是否完成
                        if task.status in terminal_states:
                            logger.info(f"Agent更新后任务已完成: task_id={self.task_id}, status={task.status}")
                            await self.send_task_status(task)
                            await self.broadcast_task_completion(task)
                            break

                # 发送任务状态
                await self.send_task_status(task)

                # 等待3秒
                await asyncio.sleep(3)
        except asyncio.CancelledError:
            # 轮询被取消
            logger.info(f"任务状态轮询已取消: task_id={self.task_id}")
            raise
        except Exception as e:
            logger.error(f"轮询任务状态时出错: {str(e)}")

    async def send_task_status(self, task):
        """发送任务状态到客户端"""
        serializer = CompareTaskSerializer(task)
        await self.send(text_data=json.dumps({
            'type': 'task_status',
            'task': serializer.data
        }))

    async def broadcast_task_completion(self, task):
        """广播任务完成通知到所有客户端"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()
            if channel_layer:
                # 序列化任务数据
                serializer = CompareTaskSerializer(task)
                task_data = serializer.data

                # 广播到全局通知组
                await channel_layer.group_send(
                    'task_notifications',
                    {
                        'type': 'task_completion',
                        'content': {
                            'type': 'task_completion',
                            'task': task_data,
                            'message': f'任务 {task.name or task.id} 已完成'
                        }
                    }
                )

                # 广播到特定任务组
                task_group_name = f'task_{task.id}'
                await channel_layer.group_send(
                    task_group_name,
                    {
                        'type': 'task_completion',
                        'content': {
                            'type': 'task_completion',
                            'task': task_data,
                            'message': f'任务已完成'
                        }
                    }
                )

                logger.info(f"已广播任务完成通知: task_id={task.id}, status={task.status}")
        except Exception as e:
            logger.error(f"广播任务完成通知失败: {str(e)}")

    @database_sync_to_async
    def get_task(self, task_id):
        """获取任务"""
        try:
            return CompareTask.objects.get(id=task_id)
        except ObjectDoesNotExist:
            logger.warning(f"任务不存在: {task_id}")
            return None

    @database_sync_to_async
    def update_task_from_agent(self, task):
        """从DB-Agent更新任务状态"""
        try:
            # 获取DB-Agent客户端
            db_agent_client = get_db_agent_client()

            # 查询DB-Agent任务状态
            agent_status = db_agent_client.get_task_status(task.external_id)

            # 检查是否成功获取状态
            if not agent_status.get('success', False):
                logger.warning(f"从DB-Agent获取任务状态失败: {agent_status.get('message', '未知错误')}")
                return False

            # 获取状态数据
            status_data = agent_status.get('data', {})

            # 更新任务状态
            updated = False
            if status_data:
                # 获取状态和进度
                status = status_data.get('status', '').lower()
                progress = status_data.get('progress', 0)
                message = status_data.get('message', '')

                # 映射状态
                if status == 'completed':
                    task.status = 'success'
                    updated = True
                elif status in ['failed', 'error', 'canceled']:
                    task.status = 'failed' if status in ['failed', 'error'] else 'stopped'
                    updated = True
                elif status != task.status:
                    task.status = status
                    updated = True

                # 更新进度
                if progress != task.progress:
                    task.progress = progress
                    updated = True

                # 更新消息
                if message and message != task.message:
                    task.message = message
                    updated = True

                # 更新详情
                if 'details' in status_data and status_data['details']:
                    if not task.details:
                        task.details = {}
                    task.details.update(status_data['details'])
                    updated = True

                # 保存更新
                if updated:
                    task.save()

            return updated
        except Exception as e:
            logger.error(f"更新任务状态时出错: {str(e)}")
            return False


class TaskNotificationConsumer(AsyncWebsocketConsumer):
    """
    任务通知WebSocket消费者

    处理全局任务通知，当有任务状态变化时向所有客户端推送通知。
    """

    async def connect(self):
        """处理WebSocket连接请求"""
        try:
            # 将当前连接加入到通知组
            self.notification_group_name = 'task_notifications'
            await self.channel_layer.group_add(
                self.notification_group_name,
                self.channel_name
            )

            # 注册WebSocket连接
            websocket_manager.register_connection(
                self.channel_name,
                self.notification_group_name
            )

            # 接受WebSocket连接
            await self.accept()

            logger.info(f"任务通知WebSocket连接已建立: channel={self.channel_name}")
        except Exception as e:
            logger.error(f"任务通知WebSocket连接处理异常: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, None, {
                "consumer": "TaskNotificationConsumer",
                "method": "connect",
                "channel_name": getattr(self, 'channel_name', 'unknown')
            })
            error_stats.add_error(error_report)
            await self.close()

    async def disconnect(self, close_code):
        """处理WebSocket断开连接"""
        try:
            # 将当前连接从通知组中移除
            await self.channel_layer.group_discard(
                self.notification_group_name,
                self.channel_name
            )

            # 注销WebSocket连接
            websocket_manager.unregister_connection(self.channel_name)

            logger.info(f"任务通知WebSocket连接已断开: channel={self.channel_name}, code={close_code}")
        except Exception as e:
            logger.error(f"任务通知WebSocket断开连接处理异常: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, None, {
                "consumer": "TaskNotificationConsumer",
                "method": "disconnect",
                "channel_name": getattr(self, 'channel_name', 'unknown'),
                "close_code": close_code
            })
            error_stats.add_error(error_report)

    async def receive(self, text_data):
        """处理从客户端接收的消息"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'ping':
                # 处理心跳消息
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': data.get('timestamp') or timezone.now().isoformat()
                }))
            elif message_type == 'subscribe':
                # 处理订阅请求
                task_id = data.get('task_id')
                if task_id:
                    task_group_name = f'task_{task_id}'
                    await self.channel_layer.group_add(
                        task_group_name,
                        self.channel_name
                    )
                    # 更新连接信息
                    websocket_manager.register_connection(
                        self.channel_name,
                        task_group_name,
                        task_id
                    )
                    await self.send(text_data=json.dumps({
                        'type': 'subscribed',
                        'task_id': task_id,
                        'timestamp': timezone.now().isoformat()
                    }))
            elif message_type == 'unsubscribe':
                # 处理取消订阅请求
                task_id = data.get('task_id')
                if task_id:
                    task_group_name = f'task_{task_id}'
                    await self.channel_layer.group_discard(
                        task_group_name,
                        self.channel_name
                    )
                    await self.send(text_data=json.dumps({
                        'type': 'unsubscribed',
                        'task_id': task_id,
                        'timestamp': timezone.now().isoformat()
                    }))
            else:
                # 未知消息类型
                logger.warning(f"收到未知类型的消息: {message_type}")
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'未知消息类型: {message_type}',
                    'timestamp': timezone.now().isoformat()
                }))
        except json.JSONDecodeError as e:
            logger.warning(f"收到无效的JSON消息: {text_data[:100]}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, None, {
                "consumer": "TaskNotificationConsumer",
                "method": "receive",
                "channel_name": self.channel_name,
                "text_data": text_data[:200]
            })
            error_stats.add_error(error_report)
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': '无效的JSON数据',
                'timestamp': timezone.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, None, {
                "consumer": "TaskNotificationConsumer",
                "method": "receive",
                "channel_name": self.channel_name,
                "text_data": text_data[:200] if 'text_data' in locals() else None
            })
            error_stats.add_error(error_report)
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'处理消息时发生错误: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }))

    async def task_notification(self, event):
        """处理任务通知事件"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            # 将任务通知发送给WebSocket客户端
            await self.send(text_data=json.dumps(event['content']))
        except Exception as e:
            logger.error(f"发送任务通知时发生错误: {str(e)}")
            # 记录错误
            error_report = ErrorReport.from_exception(e, None, {
                "consumer": "TaskNotificationConsumer",
                "method": "task_notification",
                "channel_name": self.channel_name,
                "event_type": event.get('type', 'unknown')
            })
            error_stats.add_error(error_report)

    async def task_status(self, event):
        """处理任务状态消息"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            # 发送任务状态更新
            await self.send(text_data=json.dumps(event['content']))
        except Exception as e:
            logger.error(f"发送任务状态时发生错误: {str(e)}")

    async def task_completion(self, event):
        """处理任务完成消息"""
        try:
            # 更新连接活动时间
            websocket_manager.update_connection_activity(self.channel_name)

            # 发送任务完成通知
            await self.send(text_data=json.dumps(event['content']))
            logger.info(f"已发送任务完成通知: task_id={event['content'].get('task', {}).get('id')}")
        except Exception as e:
            logger.error(f"发送任务完成通知时发生错误: {str(e)}")
