<template>
  <el-card class="form-card compact-card same-height-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="card-title">基本信息</span>
      </div>
    </template>
    <el-form-item label="模型名称" prop="name">
      <el-input v-model="modelName" placeholder="请输入比对模型名称" @update:modelValue="updateModelName" />
    </el-form-item>
    
    <el-form-item label="模型描述" prop="description">
      <el-input
        v-model="modelDescription"
        type="textarea"
        :rows="2"
        placeholder="请输入模型描述"
        @update:modelValue="updateModelDescription"
      />
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  name: string;
  description: string;
}>();

const emit = defineEmits<{
  (e: 'update:name', value: string): void;
  (e: 'update:description', value: string): void;
}>();

const modelName = ref(props.name);
const modelDescription = ref(props.description);

// 监听props变化，更新本地状态
watch(() => props.name, (newVal) => {
  modelName.value = newVal;
});

watch(() => props.description, (newVal) => {
  modelDescription.value = newVal;
});

// 更新事件处理
const updateModelName = (value: string) => {
  emit('update:name', value);
};

const updateModelDescription = (value: string) => {
  emit('update:description', value);
};
</script>

<style lang="scss" scoped>
.form-card {
  border-radius: 0 !important;
  width: 100%; 
  box-sizing: border-box; 
  display: flex; 
  flex-direction: column; 
  border: none;
  background-color: var(--el-bg-color);
  
  &.compact-card {
     :deep(.el-card__body) {
      padding: 15px;
      flex-grow: 1;
    }
     :deep(.el-card__header) {
      padding: 10px 15px;
    }
     :deep(.el-form-item) {
      margin-bottom: 15px;
      font-size: 14px;
    }
  }

  &.same-height-card {
    height: 100%; 
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}

.el-form-item:last-child {
  margin-bottom: 5px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
  font-size: 14px;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-textarea__inner) {
  font-size: 14px;
}
</style> 