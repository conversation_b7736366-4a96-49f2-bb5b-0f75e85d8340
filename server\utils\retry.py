"""
重试机制工具模块

提供函数重试装饰器，用于在遇到非致命错误时自动重试。
"""
import time
import logging
import functools
import traceback
from typing import Callable, List, Type, Optional, Any, Dict, Union
from .error_handler import is_fatal_error

logger = logging.getLogger(__name__)

def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: List[Type[Exception]] = None,
    fatal_exceptions: List[Type[Exception]] = None,
    on_retry: Optional[Callable[[Exception, int, int], None]] = None,
    on_failure: Optional[Callable[[Exception, int], None]] = None,
    retry_message: str = "重试操作 (尝试 {attempt}/{max_attempts}): {func_name}",
    failure_message: str = "操作失败，已达到最大重试次数: {func_name}"
):
    """
    函数重试装饰器
    
    Args:
        max_attempts: 最大尝试次数
        delay: 初始延迟时间(秒)
        backoff_factor: 退避因子，每次重试延迟时间会乘以这个因子
        exceptions: 需要重试的异常类型列表，默认为所有异常
        fatal_exceptions: 致命异常类型列表，遇到这些异常不会重试
        on_retry: 重试前回调函数，接收异常、当前尝试次数和最大尝试次数
        on_failure: 失败回调函数，接收异常和最大尝试次数
        retry_message: 重试日志消息模板
        failure_message: 失败日志消息模板
        
    Returns:
        装饰器函数
    """
    if exceptions is None:
        exceptions = [Exception]
    
    if fatal_exceptions is None:
        fatal_exceptions = []
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    
                    # 检查是否为致命异常
                    if any(isinstance(e, exc) for exc in fatal_exceptions) or is_fatal_error(e):
                        logger.error(f"遇到致命异常，不再重试: {str(e)}")
                        if on_failure:
                            on_failure(e, attempt)
                        raise
                    
                    # 如果是最后一次尝试，则不再重试
                    if attempt == max_attempts:
                        logger.error(
                            failure_message.format(
                                func_name=func.__name__,
                                exception=str(e),
                                traceback=traceback.format_exc()
                            )
                        )
                        if on_failure:
                            on_failure(e, attempt)
                        raise
                    
                    # 记录重试信息
                    logger.warning(
                        retry_message.format(
                            attempt=attempt,
                            max_attempts=max_attempts,
                            func_name=func.__name__,
                            exception=str(e),
                            delay=current_delay
                        )
                    )
                    
                    # 调用重试回调
                    if on_retry:
                        on_retry(e, attempt, max_attempts)
                    
                    # 等待一段时间后重试
                    time.sleep(current_delay)
                    
                    # 增加延迟时间
                    current_delay *= backoff_factor
            
            # 如果所有尝试都失败，抛出最后一个异常
            if last_exception:
                raise last_exception
            
            # 这里应该永远不会执行到，因为要么成功返回，要么抛出异常
            return None
        
        return wrapper
    
    return decorator

def async_retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: List[Type[Exception]] = None,
    fatal_exceptions: List[Type[Exception]] = None,
    on_retry: Optional[Callable[[Exception, int, int], None]] = None,
    on_failure: Optional[Callable[[Exception, int], None]] = None,
    retry_message: str = "重试异步操作 (尝试 {attempt}/{max_attempts}): {func_name}",
    failure_message: str = "异步操作失败，已达到最大重试次数: {func_name}"
):
    """
    异步函数重试装饰器
    
    Args:
        max_attempts: 最大尝试次数
        delay: 初始延迟时间(秒)
        backoff_factor: 退避因子，每次重试延迟时间会乘以这个因子
        exceptions: 需要重试的异常类型列表，默认为所有异常
        fatal_exceptions: 致命异常类型列表，遇到这些异常不会重试
        on_retry: 重试前回调函数，接收异常、当前尝试次数和最大尝试次数
        on_failure: 失败回调函数，接收异常和最大尝试次数
        retry_message: 重试日志消息模板
        failure_message: 失败日志消息模板
        
    Returns:
        异步装饰器函数
    """
    if exceptions is None:
        exceptions = [Exception]
    
    if fatal_exceptions is None:
        fatal_exceptions = []
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            import asyncio
            
            current_delay = delay
            last_exception = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    
                    # 检查是否为致命异常
                    if any(isinstance(e, exc) for exc in fatal_exceptions) or is_fatal_error(e):
                        logger.error(f"遇到致命异常，不再重试: {str(e)}")
                        if on_failure:
                            on_failure(e, attempt)
                        raise
                    
                    # 如果是最后一次尝试，则不再重试
                    if attempt == max_attempts:
                        logger.error(
                            failure_message.format(
                                func_name=func.__name__,
                                exception=str(e),
                                traceback=traceback.format_exc()
                            )
                        )
                        if on_failure:
                            on_failure(e, attempt)
                        raise
                    
                    # 记录重试信息
                    logger.warning(
                        retry_message.format(
                            attempt=attempt,
                            max_attempts=max_attempts,
                            func_name=func.__name__,
                            exception=str(e),
                            delay=current_delay
                        )
                    )
                    
                    # 调用重试回调
                    if on_retry:
                        on_retry(e, attempt, max_attempts)
                    
                    # 等待一段时间后重试
                    await asyncio.sleep(current_delay)
                    
                    # 增加延迟时间
                    current_delay *= backoff_factor
            
            # 如果所有尝试都失败，抛出最后一个异常
            if last_exception:
                raise last_exception
            
            # 这里应该永远不会执行到，因为要么成功返回，要么抛出异常
            return None
        
        return wrapper
    
    return decorator
