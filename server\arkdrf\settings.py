"""
Django settings for arkdrf project.

Generated by 'django-admin startproject' using Django 5.0.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
from datetime import timedelta
import os
import environ
import sys
# import pymysql
# pymysql.install_as_MySQLdb()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 读取环境文件
env = environ.Env()
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env.str("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG", default=False)

# nginx反向代理设置
USE_X_FORWARDED_HOST = env.bool("USE_X_FORWARDED_HOST", default=False)
if USE_X_FORWARDED_HOST:
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["localhost", "127.0.0.1", "0.0.0.0"])


# Application definition

INSTALLED_APPS = [
    "rest_framework",
    "django.contrib.auth",
    "django.contrib.admin",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "django_filters",  # 过滤器
    "channels",        # WebSocket支持
    # 自定义app
    "apps.user",
    "apps.system",
    "apps.functiontest",
    "apps.monitor",
    "apps.database",     # 添加数据库比对应用
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "utils.middleware.ApiLoggingMiddleware",  # 自定义日志中间件
]

ROOT_URLCONF = "arkdrf.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "arkdrf.wsgi.application"


# ================================================= #
# ********************** 数据库 ******************** #
# ================================================= #
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases
# 配置数据库
if env.str('DB_ENGINE') == 'mysql':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': env.str('DB_NAME'),
            'USER': env.str('DB_USER'),
            'PASSWORD': env.str('DB_PASSWORD'),
            'HOST': env.str('DB_HOST', default='localhost'),
            'PORT': env.str('DB_PORT', default='3306'),
        }
    }
elif env.str('DB_ENGINE') == 'plsql' or env.str('DB_ENGINE') == 'postgresql':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': env.str('DB_NAME'),
            'USER': env.str('DB_USER'),
            'PASSWORD': env.str('DB_PASSWORD'),
            'HOST': env.str('DB_HOST', default='localhost'),
            'PORT': env.str('DB_PORT', default='5432'),
        }
    }
elif env.str('DB_ENGINE') == 'sqlite3':
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }
else:
    raise ValueError("Unsupported database engine in .env file")


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# auth鉴权设置
AUTH_USER_MODEL = "user.User"  # 指定自定义用户数据表(应用名.模型名)

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": ("rest_framework_simplejwt.authentication.JWTAuthentication",),
    "DEFAULT_PERMISSION_CLASSES": ["rest_framework.permissions.IsAuthenticated", "utils.permissions.ActiveAndPermission"],  # 默认权限
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],  # 默认过滤器
    "EXCEPTION_HANDLER": "utils.exception.CustomExceptionHandler",  # 使用自定义异常处理
}

# jwt设置
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=2),  # 设置访问 token 的过期时间
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),  # 设置刷新 token 的过期时间
    "ROTATE_REFRESH_TOKENS": True,  # 是否在使用刷新 token 时旋转
    "BLACKLIST_AFTER_ROTATION": True,  # 是否在刷新后将旧的刷新 token 加入黑名单
    "ALGORITHM": "HS256",  # 使用的算法
    "SIGNING_KEY": "dev",  # 用于签名的密钥
    "VERIFYING_KEY": None,  # 验证 token 时使用的密钥（可选）
    "AUTH_HEADER_TYPES": ("Bearer",),  # 设置授权头类型
    "USER_ID_FIELD": "id",  # 存入jwt的user数据字段
    "USER_ID_CLAIM": "user_id",  # jwt中存储的数据标签
}

SESSION_ENGINE = "django.contrib.sessions.backends.db"

# ================================================= #
# ********************* 跨域设置 ******************* #
# ================================================= #
# 允许跨域
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_HEADERS = "*"
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:5173",
# ]

# ================================================= #
# ****************** Redis缓存设置 ***************** #
# ================================================= #
USE_REDIS = env.bool("USE_REDIS", default=False)
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env.str("REDIS_HOST"),  # 指定 Redis 服务器地址和数据库编号
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # 'PASSWORD': env.str('REDIS_PASSWORD', default=''),
        },
    }
}

CACHES_TTL = 60 * 60 * 1  # 缓存时间

# ================================================= #
# ********************* 日志设置 ******************* #
# ================================================= #
API_LOG_ENABLE = env.bool("API_LOG_ENABLE", default=False)
API_LOG_METHODS = env.list("API_LOG_METHODS", default=["GET", "POST", "PUT", "DELETE"])
# 日志记录显示的请求模块中文名映射
API_MODEL_MAP = {
    "/api/user/login/": "登录模块",
    "/api/system/asyncroutes/": "动态路由模块",
}
# log 配置部分BEGIN #
system_version = sys.platform.lower()
if 'linux' in system_version:
    LOG_BASE_PATH = '/data'
    SERVER_LOGS_FILE = os.path.join(LOG_BASE_PATH, 'logs', 'server.log')
    ERROR_LOGS_FILE = os.path.join(LOG_BASE_PATH, 'logs', 'error.log')
    if not os.path.exists(os.path.join(LOG_BASE_PATH, 'logs')):
        try:
            os.makedirs(os.path.join(LOG_BASE_PATH, 'logs'))
        except FileExistsError:
            pass  # Directory already exists
else:
    SERVER_LOGS_FILE = os.path.join(BASE_DIR, 'logs', 'server.log')
    ERROR_LOGS_FILE = os.path.join(BASE_DIR, 'logs', 'error.log')
    if not os.path.exists(os.path.join(BASE_DIR, 'logs')):
        try:
            os.makedirs(os.path.join(BASE_DIR, 'logs'))
        except FileExistsError:
            pass

# 格式:[2023-12-28 20:45:03][basehttp.py:187:log_message] [INFO] 这是一条日志:
# 格式:[日期][文件名:行号:函数] [级别] 信息
STANDARD_LOG_FORMAT = '[%(asctime)s][%(filename)s:%(lineno)d:%(funcName)s] [%(levelname)s] %(message)s'

def skip_health_check_requests(record):
    # 过滤 GET / HTTP 的请求
    try:
        return not record.args[0].startswith('GET / HTTP')
    except Exception:
        return True

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'filters': {
        'skip_health_check_requests': {
            '()': 'django.utils.log.CallbackFilter',
            'callback': skip_health_check_requests,
        },
    },
    'formatters': {
        'standard': {
            'format': "{levelname} {asctime} {filename} - line {lineno}: {message}",
            'datefmt': '%Y-%m-%d %H:%M:%S',
            "style": "{",
        },
        'console': {
            'format': "{levelname} {asctime} {filename} - line {lineno}: {message}",
            "style": "{",
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'file': {
            'format': "{levelname} {asctime} {filename} - line {lineno}: {message}",
            "style": "{",
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        # 以json格式呈现日志, 日志的显示格式是自定义的, 通过JSONFormatter类指定
        # "json_ensure_ascii": False 可以将中文正常显示
        "json": {
            "()": "utils.logsFormat.JSONFormatter",
            "json_ensure_ascii": False
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': SERVER_LOGS_FILE,
            'maxBytes': 1024 * 1024 * 10,  # 10 MB
            'backupCount': 10,  # 最多备份10个
            'formatter': 'json',
            'encoding': 'utf-8',
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': ERROR_LOGS_FILE,
            'maxBytes': 1024 * 1024 * 10,  # 10 MB
            'backupCount': 10,  # 最多备份10个
            'formatter': 'json',
            'encoding': 'utf-8',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'console',
            'filters': ['skip_health_check_requests'],
        }
    },
    'loggers': {
        # default日志
        '': {
            'handlers': ['console', 'error', 'file'],
            'level': 'INFO',
        },
        'django': {
            'handlers': ['console', 'error', 'file'],
            'level': 'INFO',
            "propagate": False,# 不向上级 logger 传播
        },
        'scripts': {
            'handlers': ['console', 'error', 'file'],
            'level': 'INFO',
        },
        # 数据库相关日志
        'django.db.backends': {
            'handlers': ["console", "error", "file"],
            'propagate': False,
            'level': 'INFO',
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console", "error", "file"],
        },
        "uvicorn.access": {
            "handlers": ["console", "error", "file"],
            "level": "INFO"
        },
    }
}

# 用于DB-Agent连接的设置
DB_AGENT_URL = os.environ.get('DB_AGENT_URL', 'http://localhost:8001')
DB_AGENT_API_KEY = os.environ.get('DB_AGENT_API_KEY', 'DB-DUMMY-AGENT-KEY')

# ================================================= #
# ***************** Channels设置 ****************** #
# ================================================= #
# 指定ASGI应用
ASGI_APPLICATION = "arkdrf.asgi.application"

# 配置Channel Layers - 使用内存通道层（无需Redis）
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
    },
}