// Monaco Editor预加载模块
import type * as Monaco from 'monaco-editor';
import { registerMonacoThemes, getCurrentTheme } from './monaco-theme';
import { format } from 'sql-formatter'; // 导入 sql-formatter
import { ElMessage } from 'element-plus'; // 导入 ElMessage

// --- Start: 导入 Workers ---
// 使用 Vite 的 ?worker 语法导入，让 Vite 处理路径和打包
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import JsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import CssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import HtmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import TsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';
// --- End: 导入 Workers ---

// 声明全局monaco类型
declare global {
  interface Window {
    monaco: any;
    _ORIGINAL_AMD_LOADER_: any;
  }
}

// 编辑器实例
let monacoInstance: typeof Monaco | null = null;
// 加载状态
let isLoading = false;
// 加载完成的Promise
let loadingPromise: Promise<typeof Monaco> | null = null;

/**
 * 禁用源映射，防止加载不存在的.map文件
 */
const disableSourceMaps = () => {
  try {
    // 覆盖原始sourcemap加载函数
    if (window._ORIGINAL_AMD_LOADER_ && window._ORIGINAL_AMD_LOADER_.load) {
      console.log('[Monaco] 找到 AMD 加载器，准备替换 load 函数。');
      const originalLoad = window._ORIGINAL_AMD_LOADER_.load;
      window._ORIGINAL_AMD_LOADER_.load = function(moduleName: string, ...args: any[]) {
        // 跳过.map文件的加载
        if (typeof moduleName === 'string' && moduleName.endsWith('.map')) {
          console.log(`[Monaco] [Patched Load] 跳过加载源映射: ${moduleName}`);
          return Promise.resolve();
        }
        // console.log(`[Monaco] [Patched Load] 正常加载: ${moduleName}`); // 可选：调试正常加载
        return originalLoad.call(this, moduleName, ...args);
      };
      console.log('[Monaco] AMD 加载器的 load 函数已替换。');
    } else {
      // console.warn('[Monaco] 无法禁用源映射: AMD加载器 (window._ORIGINAL_AMD_LOADER_) 不可用或缺少 load 方法。');
    }
  } catch (error) {
    console.error('[Monaco] 禁用源映射时发生异常:', error);
  }
};

/**
 * 预加载Monaco编辑器
 */
export function preloadMonaco(): Promise<typeof Monaco> {
  if (monacoInstance) {
    return Promise.resolve(monacoInstance);
  }

  if (loadingPromise) {
    return loadingPromise;
  }

  // 先禁用源映射加载
  disableSourceMaps();

  isLoading = true;
  loadingPromise = new Promise<typeof Monaco>(async (resolve, reject) => {
    try {
      console.time('Monaco加载时间');
      
      // 动态导入Monaco Editor
      const monaco = await import('monaco-editor');
      
      if (!monaco) {
        throw new Error('Monaco导入失败，返回undefined');
      }
            
      // --- Start: 配置 MonacoEnvironment --- 
      self.MonacoEnvironment = {
        getWorker: function (moduleId, label) {
          // --- Start: 返回导入的 Worker 实例 ---
          if (label === 'json') {
            return new JsonWorker();
          }
          if (label === 'css' || label === 'scss' || label === 'less') {
            return new CssWorker();
          }
          if (label === 'html' || label === 'handlebars' || label === 'razor') {
            return new HtmlWorker();
          }
          if (label === 'typescript' || label === 'javascript') {
            return new TsWorker();
          }
          // 默认为 editor worker
          return new EditorWorker();
          // --- End: 返回导入的 Worker 实例 ---
        }
      };
      // --- End: 配置 MonacoEnvironment ---
      
      // --- Start: 注册 SQL 格式化提供程序 ---
      monaco.languages.registerDocumentFormattingEditProvider('sql', {
        provideDocumentFormattingEdits(model) {
          try {
            const formatted = format(model.getValue()); // 使用 sql-formatter 格式化
            return [
              {
                range: model.getFullModelRange(), // 替换整个文档
                text: formatted,
              },
            ];
          } catch (error) {
            console.error('[Monaco] SQL 格式化失败:', error);
            ElMessage.error(`SQL 格式化失败: ${error.message}`);
            return []; // 返回空数组表示无法格式化或出错
          }
        },
      });
      console.log('[Monaco] SQL 格式化提供程序已注册');
      // --- End: 注册 SQL 格式化提供程序 ---
      
      // 加载SQL语言配置
      try {
        const { registerSqlLanguage } = await import('@/components/MonacoEditor/src/sql-language');
        registerSqlLanguage(monaco);
      } catch (error) {
        console.warn('[Monaco] SQL语言注册失败:', error);
      }
      
      // 注册主题
      try {
        registerMonacoThemes(monaco);
      } catch (error) {
        console.warn('[Monaco] 主题注册失败:', error);
      }
      
      // 设置默认主题
      try {
        const currentTheme = getCurrentTheme();
        monaco.editor.setTheme(currentTheme);
      } catch (error) {
        console.warn('[Monaco] 设置主题失败:', error);
      }
      
      monacoInstance = monaco;
      console.timeEnd('Monaco加载时间');
      resolve(monaco);
    } catch (error) {
      console.error('[Monaco] 加载失败:', error);
      // 出错时允许重试
      loadingPromise = null;
      isLoading = false;
      reject(error);
    }
  });

  return loadingPromise;
}

/**
 * 获取Monaco实例
 */
export function getMonaco(): Promise<typeof Monaco> {
  return preloadMonaco();
}

/**
 * 设置当前编辑器主题
 */
export function setEditorTheme(theme?: string): void {
  if (!monacoInstance) {
    console.warn('[Monaco] 无法设置主题: Monaco实例未加载');
    return;
  }
  
  try {
    const themeName = theme || getCurrentTheme();
    console.log(`[Monaco] 设置编辑器主题: ${themeName}`);
    monacoInstance.editor.setTheme(themeName);
  } catch (error) {
    console.error('[Monaco] 设置主题失败:', error);
  }
}

// 应用启动时预加载Monaco（非阻塞）
setTimeout(() => {
  if (!monacoInstance && !isLoading) {
    preloadMonaco().catch(e => console.warn('[Monaco] 预加载失败，将在使用时再次尝试', e));
  }
}, 1000);

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  darkModeMediaQuery.addEventListener('change', () => {
    console.log('[Monaco] 检测到系统主题变化');
    setEditorTheme(getCurrentTheme());
  });
}

export default {
  preloadMonaco,
  getMonaco,
  setEditorTheme
}; 