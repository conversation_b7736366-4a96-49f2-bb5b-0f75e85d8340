#!/usr/bin/env python
"""
用户密码管理工具
用于重置、修改和验证ArkReplay系统用户密码
python manage.py dumpdata --natural-foreign --natural-primary --exclude=contenttypes --exclude=auth.permission --exclude=sessions --exclude=admin.logentry --output postgresql_backup.json
"""
import os
import sys
import getpass
import argparse
from pathlib import Path

# 添加Django设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arkdrf.settings')

# 初始化Django
import django
django.setup()

from apps.user.models import User

def list_users():
    """列出所有用户"""
    print("=== 系统用户列表 ===")
    users = User.objects.all()
    
    if not users:
        print("系统中没有用户")
        return
    
    for user in users:
        print(f"ID: {user.id}")
        print(f"用户名: {user.username}")
        print(f"邮箱: {user.email}")
        print(f"超级用户: {'是' if user.is_superuser else '否'}")
        print(f"状态: {'激活' if user.status else '禁用'}")
        print(f"部门: {user.dept.name if user.dept else '无'}")
        print(f"角色数量: {user.role.count()}")
        print("---")

def change_password(username, new_password=None, interactive=True):
    """修改用户密码"""
    try:
        # 查找用户
        user = User.objects.get(username=username)
        print(f"找到用户: {user.username} ({user.email})")
        
        # 获取新密码
        if not new_password:
            if interactive:
                new_password = getpass.getpass("请输入新密码: ")
                confirm_password = getpass.getpass("请确认新密码: ")
                
                if new_password != confirm_password:
                    print("❌ 两次输入的密码不一致")
                    return False
            else:
                print("❌ 必须提供密码")
                return False
        
        # 密码强度检查
        if len(new_password) < 6:
            print("❌ 密码长度至少6位")
            return False
        
        # 设置新密码
        old_hash = user.password[:20] + "..."
        user.set_password(new_password)
        user.save()
        
        # 验证密码
        if user.check_password(new_password):
            print("✅ 密码修改成功！")
            print(f"用户: {user.username}")
            print(f"旧密码哈希: {old_hash}")
            print(f"新密码哈希: {user.password[:20]}...")
            return True
        else:
            print("❌ 密码验证失败")
            return False
            
    except User.DoesNotExist:
        print(f"❌ 用户 '{username}' 不存在")
        return False
    except Exception as e:
        print(f"❌ 修改失败: {str(e)}")
        return False

def verify_password(username, password=None):
    """验证用户密码"""
    try:
        user = User.objects.get(username=username)
        
        if not password:
            password = getpass.getpass(f"请输入用户 {username} 的密码: ")
        
        if user.check_password(password):
            print("✅ 密码验证成功")
            return True
        else:
            print("❌ 密码验证失败")
            return False
            
    except User.DoesNotExist:
        print(f"❌ 用户 '{username}' 不存在")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def reset_admin_password():
    """重置管理员密码为默认值"""
    try:
        admin_user = User.objects.get(username='admin')
        default_password = 'admin123'
        
        admin_user.set_password(default_password)
        admin_user.save()
        
        if admin_user.check_password(default_password):
            print("✅ 管理员密码重置成功！")
            print(f"用户名: admin")
            print(f"邮箱: {admin_user.email}")
            print(f"新密码: {default_password}")
            return True
        else:
            print("❌ 密码重置失败")
            return False
            
    except User.DoesNotExist:
        print("❌ 管理员用户不存在")
        return False
    except Exception as e:
        print(f"❌ 重置失败: {str(e)}")
        return False

def create_user(username, email, password=None, is_superuser=False):
    """创建新用户"""
    try:
        # 检查用户是否已存在
        if User.objects.filter(username=username).exists():
            print(f"❌ 用户名 '{username}' 已存在")
            return False
            
        if User.objects.filter(email=email).exists():
            print(f"❌ 邮箱 '{email}' 已存在")
            return False
        
        # 获取密码
        if not password:
            password = getpass.getpass("请输入密码: ")
            confirm_password = getpass.getpass("请确认密码: ")
            
            if password != confirm_password:
                print("❌ 两次输入的密码不一致")
                return False
        
        # 创建用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )
        
        if is_superuser:
            user.is_superuser = True
            user.is_staff = True
            user.save()
        
        print("✅ 用户创建成功！")
        print(f"用户名: {user.username}")
        print(f"邮箱: {user.email}")
        print(f"超级用户: {'是' if user.is_superuser else '否'}")
        return True
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="ArkReplay用户密码管理工具")
    parser.add_argument("action", choices=["list", "change", "verify", "reset-admin", "create"],
                       help="操作类型")
    parser.add_argument("--username", help="用户名")
    parser.add_argument("--email", help="邮箱（创建用户时使用）")
    parser.add_argument("--password", help="密码（非交互模式）")
    parser.add_argument("--superuser", action="store_true", help="创建超级用户")
    
    args = parser.parse_args()
    
    if args.action == "list":
        list_users()
    elif args.action == "change":
        if not args.username:
            print("❌ 请指定用户名 --username")
            return
        change_password(args.username, args.password, interactive=not args.password)
    elif args.action == "verify":
        if not args.username:
            print("❌ 请指定用户名 --username")
            return
        verify_password(args.username, args.password)
    elif args.action == "reset-admin":
        reset_admin_password()
    elif args.action == "create":
        if not args.username or not args.email:
            print("❌ 请指定用户名 --username 和邮箱 --email")
            return
        create_user(args.username, args.email, args.password, args.superuser)

if __name__ == "__main__":
    main()
