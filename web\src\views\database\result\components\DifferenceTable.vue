<template>
  <div class="difference-table">
    <div class="table-header">
      <span class="title">差异记录 ({{ total }})</span>
      <div class="actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索记录..."
          style="width: 200px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading"
      :max-height="400"
    >
      <el-table-column prop="id" label="记录ID" width="150" show-overflow-tooltip />
      <el-table-column label="差异字段" width="150">
        <template #default="scope">
          <div v-if="scope.row.diffFields && scope.row.diffFields.length > 0">
            <el-tag
              v-for="field in scope.row.diffFields"
              :key="field"
              size="small"
              style="margin-right: 4px"
            >
              {{ field }}
            </el-tag>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="diffType" label="差异类型" width="120">
        <template #default="scope">
          <el-tag :type="getDiffTypeTag(scope.row.diffType)">
            {{ getDiffTypeText(scope.row.diffType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="源数据" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ formatData(scope.row.sourceData) }}
        </template>
      </el-table-column>
      <el-table-column label="目标数据" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ formatData(scope.row.targetData) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="差异详情" width="800px">
      <div v-if="currentRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            {{ currentRecord.id }}
          </el-descriptions-item>
          <el-descriptions-item label="差异类型">
            <el-tag :type="getDiffTypeTag(currentRecord.diffType)">
              {{ getDiffTypeText(currentRecord.diffType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="差异字段" :span="2">
            <el-tag
              v-for="field in currentRecord.diffFields"
              :key="field"
              size="small"
              style="margin-right: 4px"
            >
              {{ field }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <div class="data-compare">
          <div class="compare-section">
            <h4>源数据</h4>
            <pre class="data-content">{{ JSON.stringify(currentRecord.sourceData, null, 2) }}</pre>
          </div>
          <div class="compare-section">
            <h4>目标数据</h4>
            <pre class="data-content">{{ JSON.stringify(currentRecord.targetData, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { getComparisonResult } from '@/api/database/compare';
import { handleDatabaseError, showErrorMessage } from '@/api/database/error';
import type { DifferenceRecord } from '@/types/database';

interface Props {
  taskId: string;
  total: number;
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const tableData = ref<DifferenceRecord[]>([]);
const currentPage = ref(1);
const pageSize = ref(20);
const searchKeyword = ref('');
const detailVisible = ref(false);
const currentRecord = ref<DifferenceRecord | null>(null);

// 加载数据
const loadData = async () => {
  if (!props.taskId) return;

  loading.value = true;
  try {
    console.log('🔍 DifferenceTable 开始加载差异数据，任务ID:', props.taskId);

    // 使用正确的API端点获取任务结果
    const response = await getComparisonResult(props.taskId);

    console.log('📡 DifferenceTable API响应:', response);

    if (response.success && response.data) {
      // 从任务结果中提取差异记录
      const details = response.data.details;
      if (details && details.items) {
        console.log('📊 找到差异记录:', details.items.length, '条');

        // 转换数据格式以匹配前端期望
        const transformedData = details.items.map((item: any, index: number) => {
          // 处理主键信息
          const primaryKey = item.key?.primary_key || item.primaryKey || `record_${index}`;

          // 处理差异类型
          const diffType = item.diff_type || item.diffType || 'unknown';

          // 处理源记录和目标记录（支持转换前后的字段名）
          const sourceData = item.sourceRecord || item.source_record || item.sourceData || null;
          const targetData = item.targetRecord || item.target_record || item.targetData || null;

          // 处理差异字段（支持转换前后的字段名）
          let diffFields = [];
          const fieldDiffsData = item.fieldDiffs || item.field_diffs || item.diffFields;

          if (fieldDiffsData && Array.isArray(fieldDiffsData)) {
            // 如果有具体的字段差异信息
            diffFields = fieldDiffsData.map((diff: any) => diff.field || diff.name).filter(Boolean);
          } else if (diffType === 'target_missing' && sourceData) {
            // 目标缺失：显示源记录的所有字段
            diffFields = Object.keys(sourceData);
          } else if (diffType === 'source_missing' && targetData) {
            // 源缺失：显示目标记录的所有字段
            diffFields = Object.keys(targetData);
          } else {
            // 其他情况使用原有逻辑
            diffFields = [];
          }

          // 添加详细调试信息
          console.log(`🔍 处理差异记录 ${index}:`, {
            原始数据: item,
            差异类型: diffType,
            源数据: sourceData,
            目标数据: targetData,
            差异字段: diffFields,
            字段差异数据: fieldDiffsData
          });

          return {
            id: primaryKey,
            diffType: diffType,
            diffFields: diffFields,
            sourceData: sourceData,
            targetData: targetData,
            // 添加调试信息
            _original: item
          };
        });

        console.log('🔄 转换后的差异数据:', transformedData);
        tableData.value = transformedData;
      } else {
        console.warn('⚠️ API响应中没有找到差异记录数据');
        tableData.value = [];
      }
    } else {
      console.error('❌ API响应失败:', response.message);
      throw new Error(response.message || '获取差异记录失败');
    }
  } catch (error) {
    console.error('💥 加载差异数据失败:', error);
    const dbError = handleDatabaseError(error);
    showErrorMessage(dbError);
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 获取差异类型标签样式
const getDiffTypeTag = (type: string) => {
  const map: Record<string, 'warning' | 'danger' | 'info' | 'success'> = {
    'value_diff': 'warning',
    'type_diff': 'info',
    'format_diff': 'danger',
    'target_missing': 'danger',
    'source_missing': 'warning',
    'source_only': 'warning',
    'target_only': 'danger',
    'differences': 'info',
  };
  return map[type] || 'info';
};

// 获取差异类型文本
const getDiffTypeText = (type: string) => {
  const map: Record<string, string> = {
    'value_diff': '值差异',
    'type_diff': '类型差异',
    'format_diff': '格式差异',
    'target_missing': '目标缺失',
    'source_missing': '源缺失',
    'source_only': '源独有',
    'target_only': '目标独有',
    'differences': '数据差异',
  };
  return map[type] || type;
};

// 格式化数据显示
const formatData = (data: Record<string, any> | null | undefined) => {
  if (!data || data === null || data === undefined) {
    return '(空)';
  }

  if (typeof data === 'string') {
    return data;
  }

  if (typeof data === 'object') {
    const keys = Object.keys(data);
    if (keys.length === 0) return '(空)';
    if (keys.length === 1) return String(data[keys[0]]);
    return `${keys.length}个字段`;
  }

  return String(data);
};

// 查看详情
const viewDetail = (record: DifferenceRecord) => {
  currentRecord.value = record;
  detailVisible.value = true;
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 分页处理
const handleSizeChange = () => {
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = () => {
  loadData();
};

// 监听任务ID变化
watch(() => props.taskId, () => {
  if (props.taskId) {
    currentPage.value = 1;
    loadData();
  }
});

// 组件挂载时加载数据
onMounted(() => {
  if (props.taskId) {
    loadData();
  }
});
</script>

<style scoped>
.difference-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-weight: 600;
  font-size: 16px;
}

.actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.data-compare {
  display: flex;
  gap: 16px;
}

.compare-section {
  flex: 1;
}

.compare-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.data-content {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
