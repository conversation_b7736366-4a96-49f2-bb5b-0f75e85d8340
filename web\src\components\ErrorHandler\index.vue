<!--
错误处理组件
用于统一处理和展示错误信息
-->
<template>
  <div class="error-handler">
    <slot v-if="!error"></slot>
    
    <div v-else class="error-container">
      <error-details 
        :error="error" 
        :loading="loading"
        :show-actions="showActions"
        :show-retry="showRetry"
        @retry="handleRetry"
        @close="handleClose"
      >
        <template v-if="$slots.actions" #actions>
          <slot name="actions"></slot>
        </template>
      </error-details>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import ErrorDetails from '@/components/ErrorDetails/index.vue'
import { formatError } from '@/utils/error/errorHandler'

export default {
  name: '<PERSON>rrorHandler',
  components: {
    ErrorDetails
  },
  props: {
    // 错误对象
    errorObj: {
      type: [Object, Error, String],
      default: null
    },
    // 任务ID
    taskId: {
      type: String,
      default: null
    },
    // 上下文信息
    context: {
      type: Object,
      default: () => ({})
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['retry', 'close'],
  setup(props, { emit }) {
    // 格式化后的错误对象
    const error = ref(null)
    
    // 监听错误对象变化
    watch(() => props.errorObj, (newError) => {
      if (newError) {
        error.value = formatError(newError, props.taskId, props.context)
      } else {
        error.value = null
      }
    }, { immediate: true })
    
    // 处理重试
    const handleRetry = () => {
      emit('retry', error.value)
    }
    
    // 处理关闭
    const handleClose = () => {
      emit('close')
    }
    
    return {
      error,
      handleRetry,
      handleClose
    }
  }
}
</script>

<style scoped>
.error-container {
  margin-top: 16px;
}
</style>
