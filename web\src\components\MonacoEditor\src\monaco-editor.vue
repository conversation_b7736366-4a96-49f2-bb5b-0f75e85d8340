<template>
  <div 
    ref="editorContainer" 
    class="monaco-editor-container" 
    :style="{ height }"
    :id="containerId"
  >
    <div v-if="loading" class="editor-loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载编辑器中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { v4 as uuidv4 } from 'uuid';
import { useMonacoEditor } from './useMonaco';
import type { MonacoEditorProps, MonacoEditorEmits } from './types';

const props = withDefaults(defineProps<MonacoEditorProps>(), {
  modelValue: '',
  language: 'sql',
  theme: undefined,
  readOnly: false,
  height: '320px',
  options: () => ({}),
  id: '',
});

const emit = defineEmits<MonacoEditorEmits>();

// 生成唯一容器ID
const containerId = computed(() => props.id || `monaco-editor-${uuidv4()}`);

// DOM引用
const editorContainer = ref<HTMLElement | null>(null);

// 使用MonacoEditor组合式API
const { loading, formatCode, editor, initMonaco } = useMonacoEditor(
  props,
  emit,
  containerId.value
);

// 确保DOM渲染完成后再初始化编辑器
onMounted(() => {
  if (editorContainer.value) {
    initMonaco(editorContainer.value);
  }
});

// 对外暴露方法
defineExpose({
  formatCode,
  editor,
});
</script>

<style lang="scss" scoped>
.monaco-editor-container {
  width: 100%;
  min-height: 100px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  position: relative;
}

.editor-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--el-bg-color);
  z-index: 1;
  
  .loading-icon {
    font-size: 24px;
    margin-bottom: 8px;
    animation: rotate 1.5s linear infinite;
  }
  
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

/* 确保编辑器本身没有边框，避免与容器边框重叠 */
:deep(.monaco-editor) {
  border-radius: 0 !important;
  border: none !important;
}

:deep(.monaco-editor .overflow-guard) {
  width: 100% !important;
  height: 100% !important;
}
</style> 