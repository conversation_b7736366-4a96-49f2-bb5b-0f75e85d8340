/**
 * XML处理工具函数
 * 用于表配置的XML导入导出功能
 */

/**
 * 将表配置数组转换为XML字符串
 * @param {Array} tables - 表配置数组
 * @returns {string} XML字符串
 */
export function tablesToXml(tables) {
  let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<tables>\n';
  
  tables.forEach((table, index) => {
    xmlContent += `  <table remark="${escapeXml(table.remark || '')}" diffEnabled="${Boolean(table.diffEnabled)}">\n`;
    xmlContent += `    <sql1><![CDATA[${table.sql_1 || ''}]]></sql1>\n`;
    xmlContent += `    <sql2><![CDATA[${table.sql_2 || ''}]]></sql2>\n`;
    xmlContent += `  </table>\n`;
  });
  
  xmlContent += '</tables>';
  
  return xmlContent;
}

/**
 * 将XML字符串解析为表配置数组
 * @param {string} xmlContent - XML字符串
 * @returns {Array} 表配置数组
 */
export function parseXmlToTables(xmlContent) {
  // 使用DOMParser解析XML
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
  
  // 检查XML解析错误
  const parseError = xmlDoc.getElementsByTagName('parsererror');
  if (parseError.length > 0) {
    throw new Error('XML解析错误: ' + parseError[0].textContent);
  }
  
  const tableNodes = xmlDoc.getElementsByTagName('table');
  const tables = [];
  
  for (let i = 0; i < tableNodes.length; i++) {
    const tableNode = tableNodes[i];
    
    // 获取表属性
    const remark = tableNode.getAttribute('remark') || `表配置${i + 1}`;
    const diffEnabled = tableNode.getAttribute('diffEnabled') === 'true';
    
    // 获取SQL内容 - 优先尝试获取CDATA内容
    let sql1 = '';
    let sql2 = '';
    
    // 查找 sql1 和 sql2 节点
    const sql1Node = tableNode.getElementsByTagName('sql1')[0];
    const sql2Node = tableNode.getElementsByTagName('sql2')[0];
    
    // 尝试不同的方式获取内容
    if (sql1Node) {
      // 首先尝试获取CDATA内容
      if (sql1Node.firstChild && sql1Node.firstChild.nodeType === 4) { // 4 是 CDATA 节点类型
        sql1 = sql1Node.firstChild.nodeValue;
      } else {
        // 否则获取文本内容
        sql1 = sql1Node.textContent || '';
      }
    }
    
    if (sql2Node) {
      // 首先尝试获取CDATA内容
      if (sql2Node.firstChild && sql2Node.firstChild.nodeType === 4) { // 4 是 CDATA 节点类型
        sql2 = sql2Node.firstChild.nodeValue;
      } else {
        // 否则获取文本内容
        sql2 = sql2Node.textContent || '';
      }
    }
    
    console.log(`解析表 ${i+1} SQL内容:`, { remark, sql1Length: sql1?.length, sql2Length: sql2?.length });
    
    // 兼容旧版XML格式
    // 尝试查找 sql_1 和 sql_2 节点
    if (!sql1) {
      const oldSql1Node = tableNode.getElementsByTagName('sql_1')[0];
      if (oldSql1Node) {
        sql1 = oldSql1Node.textContent || '';
      }
    }
    
    if (!sql2) {
      const oldSql2Node = tableNode.getElementsByTagName('sql_2')[0];
      if (oldSql2Node) {
        sql2 = oldSql2Node.textContent || '';
      }
    }
    
    // 创建表配置对象
    const table = {
      remark,
      sql_1: sql1,
      sql_2: sql2,
      diffEnabled,
      diffInfo: null
    };
    
    tables.push(table);
  }
  
  console.log('解析出的表配置:', tables);
  return tables;
}

/**
 * 创建示例XML字符串
 * @returns {string} 示例XML字符串
 */
export function createSampleXml() {
  const sampleTables = [
    {
      remark: '用户表配置',
      sql_1: `SELECT 
  id, 
  username,
  email,
  created_at
FROM 
  users
WHERE 
  status = 'active'
ORDER BY 
  created_at DESC`,
      sql_2: `SELECT 
  id, 
  username,
  email,
  phone,
  created_at
FROM 
  user_accounts
WHERE 
  account_status = 'active'
ORDER BY 
  created_at DESC`,
      diffEnabled: true
    },
    {
      remark: '订单表配置',
      sql_1: `SELECT 
  o.id, 
  o.order_number,
  o.total_amount,
  o.created_at
FROM 
  orders o
JOIN
  users u ON o.user_id = u.id
WHERE 
  o.status = 'completed'`,
      sql_2: '',
      diffEnabled: false
    }
  ];
  
  return tablesToXml(sampleTables);
}

/**
 * 下载XML文件
 * @param {string} content - XML内容
 * @param {string} filename - 文件名
 */
export function downloadXml(content, filename) {
  const blob = new Blob([content], { type: 'text/xml' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  
  URL.revokeObjectURL(url);
}

/**
 * 转义XML特殊字符
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeXml(text) {
  return String(text)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
} 