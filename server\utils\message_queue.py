"""
消息队列模块

提供消息队列机制，确保断连期间的状态更新不会丢失，并支持消息重发和批量处理。
"""
import logging
import threading
import time
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from django.utils import timezone
from collections import deque

logger = logging.getLogger(__name__)

class MessageQueue:
    """消息队列类，用于存储和管理待发送的消息"""

    def __init__(self, max_size: int = 1000, batch_size: int = 10,
                 retry_interval: int = 5, max_retries: int = 3):
        """
        初始化消息队列

        Args:
            max_size: 队列最大容量
            batch_size: 批处理大小
            retry_interval: 重试间隔（秒）
            max_retries: 最大重试次数
        """
        self.queues: Dict[str, deque] = {}  # 按任务ID分组的消息队列
        self.max_size = max_size
        self.batch_size = batch_size
        self.retry_interval = retry_interval
        self.max_retries = max_retries
        self.lock = threading.RLock()
        self.worker_thread = None
        self.running = False
        self.last_sync_time: Dict[str, datetime] = {}  # 记录每个任务的最后同步时间
        self.handlers: Dict[str, Callable] = {}  # 消息处理函数

    def register_handler(self, message_type: str, handler: Callable):
        """
        注册消息处理函数

        Args:
            message_type: 消息类型
            handler: 处理函数
        """
        with self.lock:
            self.handlers[message_type] = handler
            logger.debug(f"已注册消息处理函数: {message_type}")

    def add_message(self, task_id: str, message: Dict[str, Any],
                   message_type: str = "status_update", priority: int = 0):
        """
        添加消息到队列

        Args:
            task_id: 任务ID
            message: 消息内容
            message_type: 消息类型
            priority: 优先级（0-9，数字越小优先级越高）

        Returns:
            bool: 是否成功添加
        """
        with self.lock:
            # 确保任务队列存在
            if task_id not in self.queues:
                self.queues[task_id] = deque(maxlen=self.max_size)

            # 检查队列是否已满
            if len(self.queues[task_id]) >= self.max_size:
                logger.warning(f"任务 {task_id} 的消息队列已满，丢弃最早的消息")
                self.queues[task_id].popleft()  # 移除最早的消息

            # 添加时间戳和重试信息
            message_with_meta = {
                "content": message,
                "type": message_type,
                "priority": priority,
                "timestamp": timezone.now().isoformat(),
                "retries": 0,
                "next_retry": timezone.now()
            }

            # 添加到队列
            self.queues[task_id].append(message_with_meta)
            logger.debug(f"已添加消息到队列: task_id={task_id}, type={message_type}")

            # 如果工作线程未运行，启动它
            if not self.running:
                self.start_worker()

            return True

    def start_worker(self):
        """启动工作线程处理队列"""
        with self.lock:
            if self.running:
                return

            self.running = True
            self.worker_thread = threading.Thread(target=self._process_queues, daemon=True)
            self.worker_thread.start()
            logger.info("消息队列工作线程已启动")

    def stop_worker(self):
        """停止工作线程"""
        with self.lock:
            self.running = False
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=1.0)
                logger.info("消息队列工作线程已停止")

    def _process_queues(self):
        """处理所有队列中的消息"""
        while self.running:
            try:
                # 获取所有任务ID
                with self.lock:
                    task_ids = list(self.queues.keys())

                # 处理每个任务的队列
                for task_id in task_ids:
                    self._process_task_queue(task_id)

                # 检查是否需要全量同步
                self._check_full_sync()

                # 等待一段时间
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"处理消息队列时发生错误: {str(e)}")
                time.sleep(1.0)  # 出错后等待较长时间

    def _process_task_queue(self, task_id: str):
        """处理单个任务的消息队列"""
        try:
            # 获取待处理的消息
            messages_to_process = []
            with self.lock:
                if task_id not in self.queues or not self.queues[task_id]:
                    return

                queue = self.queues[task_id]
                now = timezone.now()

                # 收集准备好的消息
                for _ in range(min(len(queue), self.batch_size)):
                    message = queue[0]  # 查看队首消息

                    # 检查是否到达重试时间
                    if message["next_retry"] <= now:
                        messages_to_process.append(queue.popleft())  # 移除并返回队首消息
                    else:
                        break  # 如果队首消息未到重试时间，后面的也不会到

            # 处理收集到的消息
            for message in messages_to_process:
                self._process_message(task_id, message)
        except Exception as e:
            logger.error(f"处理任务 {task_id} 的消息队列时发生错误: {str(e)}")

    def _process_message(self, task_id: str, message: Dict[str, Any]):
        """处理单个消息"""
        try:
            message_type = message["type"]
            content = message["content"]

            # 查找对应的处理函数
            handler = self.handlers.get(message_type)
            if not handler:
                # 对于某些消息类型，在启动阶段可能处理器还未注册，这是正常的
                if message_type in ["notification", "task_status"] and not self.running:
                    logger.debug(f"消息处理器尚未注册，跳过消息: type={message_type}, task_id={task_id}")
                else:
                    logger.warning(f"未找到消息类型 {message_type} 的处理函数")
                return

            # 调用处理函数
            success = handler(task_id, content)

            # 处理失败，可能需要重试
            if not success and message["retries"] < self.max_retries:
                message["retries"] += 1
                message["next_retry"] = timezone.now() + timedelta(seconds=self.retry_interval)

                # 重新加入队列
                with self.lock:
                    if task_id in self.queues:
                        self.queues[task_id].append(message)
                        logger.debug(f"消息处理失败，已重新加入队列: task_id={task_id}, type={message_type}, retries={message['retries']}")
            elif not success:
                logger.warning(f"消息处理失败且超过最大重试次数: task_id={task_id}, type={message_type}")
            else:
                # 处理成功，更新最后同步时间
                with self.lock:
                    self.last_sync_time[task_id] = timezone.now()
                logger.debug(f"消息处理成功: task_id={task_id}, type={message_type}")
        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")

    def _check_full_sync(self):
        """检查是否需要全量同步"""
        try:
            now = timezone.now()
            sync_interval = timedelta(minutes=5)  # 5分钟同步一次

            with self.lock:
                # 获取需要检查的任务列表
                tasks_to_check = list(self.last_sync_time.items())

            # 遍历任务进行检查
            for task_id, last_time in tasks_to_check:
                try:
                    # 检查是否超过同步间隔
                    if now - last_time <= sync_interval:
                        continue

                    # 检查任务是否存在
                    if not self.task_exists(task_id):
                        logger.warning(f"任务 {task_id} 不存在，清理相关资源")
                        self.cleanup_task(task_id)
                        continue

                    # 检查是否有活跃连接
                    if not self.has_active_connections(task_id):
                        logger.info(f"任务 {task_id} 没有活跃连接，跳过全量同步")
                        # 更新同步时间，避免频繁检查
                        with self.lock:
                            self.last_sync_time[task_id] = now
                        continue

                    # 执行全量同步
                    handler = self.handlers.get("full_sync")
                    if handler:
                        # 在新线程中执行全量同步，避免阻塞主工作线程
                        threading.Thread(
                            target=handler,
                            args=(task_id,),
                            daemon=True
                        ).start()

                        # 更新最后同步时间
                        with self.lock:
                            self.last_sync_time[task_id] = now
                        logger.info(f"已触发任务 {task_id} 的全量同步")

                except Exception as e:
                    logger.error(f"检查任务 {task_id} 全量同步时发生错误: {str(e)}")

        except Exception as e:
            logger.error(f"检查全量同步时发生错误: {str(e)}")

    def clear_task_queue(self, task_id: str):
        """
        清空指定任务的消息队列

        Args:
            task_id: 任务ID
        """
        with self.lock:
            if task_id in self.queues:
                self.queues[task_id].clear()
                logger.info(f"已清空任务 {task_id} 的消息队列")

            if task_id in self.last_sync_time:
                del self.last_sync_time[task_id]

    def cleanup_task(self, task_id: str):
        """
        清理任务相关资源

        Args:
            task_id: 任务ID
        """
        with self.lock:
            # 清空消息队列
            if task_id in self.queues:
                del self.queues[task_id]
                logger.info(f"已删除任务 {task_id} 的消息队列")

            # 清除同步时间记录
            if task_id in self.last_sync_time:
                del self.last_sync_time[task_id]
                logger.info(f"已删除任务 {task_id} 的同步时间记录")

    def has_active_connections(self, task_id: str) -> bool:
        """
        检查任务是否有活跃的WebSocket连接

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否有活跃连接
        """
        try:
            # 导入WebSocket管理器
            from .websocket_manager import websocket_manager

            # 检查是否有连接
            connections = websocket_manager.get_task_connections(task_id)
            return len(connections) > 0
        except Exception as e:
            logger.error(f"检查任务 {task_id} 连接状态时发生错误: {str(e)}")
            return False

    def task_exists(self, task_id: str) -> bool:
        """
        检查任务是否存在

        Args:
            task_id: 任务ID

        Returns:
            bool: 任务是否存在
        """
        try:
            # 导入任务模型
            from apps.database.models import CompareTask

            # 检查任务是否存在
            if task_id == "global":
                return False  # global不是有效的任务ID

            try:
                # 先尝试通过ID查找
                CompareTask.objects.get(id=task_id)
                return True
            except (CompareTask.DoesNotExist, ValueError):
                # 再尝试通过external_id查找
                return CompareTask.objects.filter(external_id=task_id).exists()
        except Exception as e:
            logger.error(f"检查任务 {task_id} 是否存在时发生错误: {str(e)}")
            return False

    def get_queue_stats(self):
        """
        获取队列统计信息

        Returns:
            Dict: 队列统计信息
        """
        with self.lock:
            stats = {
                "total_queues": len(self.queues),
                "total_messages": sum(len(q) for q in self.queues.values()),
                "queues": {
                    task_id: len(queue) for task_id, queue in self.queues.items()
                }
            }
            return stats

# 创建全局消息队列实例
message_queue = MessageQueue()
