/**
 * 错误处理工具
 * 提供统一的错误处理和格式化功能
 */

import { ElMessage, ElNotification } from 'element-plus';
import { 
  ErrorCategory, 
  ErrorSeverity, 
  ErrorReport, 
  getCategoryDisplayName,
  getSeverityDisplayName,
  getSeverityType
} from './errorTypes';

/**
 * 格式化错误对象
 * 将各种错误对象格式化为统一的ErrorReport格式
 * 
 * @param error 错误对象
 * @param taskId 任务ID（可选）
 * @param context 上下文信息（可选）
 * @returns 格式化后的错误报告
 */
export function formatError(error: any, taskId?: string, context?: any): ErrorReport {
  // 如果已经是标准格式，直接返回
  if (error && error.category && error.severity && error.type && error.message) {
    return error as ErrorReport;
  }
  
  // 确定错误类型和消息
  let errorType = 'Unknown';
  let errorMessage = '未知错误';
  let category = ErrorCategory.UNKNOWN;
  let severity = ErrorSeverity.ERROR;
  let isFatal = false;
  
  if (error instanceof Error) {
    errorType = error.name;
    errorMessage = error.message;
    
    // 根据错误类型确定类别
    if (error.name.includes('Network') || error.name.includes('Fetch') || error.name.includes('Http')) {
      category = ErrorCategory.NETWORK;
    } else if (error.name.includes('Timeout')) {
      category = ErrorCategory.TIMEOUT;
    } else if (error.name.includes('Auth')) {
      category = ErrorCategory.AUTHENTICATION;
    } else if (error.name.includes('Permission') || error.name.includes('Access')) {
      category = ErrorCategory.PERMISSION;
    } else if (error.name.includes('Validation')) {
      category = ErrorCategory.VALIDATION;
    }
  } else if (typeof error === 'string') {
    errorType = 'StringError';
    errorMessage = error;
  } else if (error && typeof error === 'object') {
    // 处理API错误响应
    if (error.response) {
      errorType = `HTTP${error.response.status || ''}Error`;
      errorMessage = error.response.data?.message || error.message || '服务器错误';
      
      // 根据HTTP状态码确定类别和严重程度
      const status = error.response.status;
      if (status >= 500) {
        category = ErrorCategory.INTERNAL;
        severity = ErrorSeverity.CRITICAL;
      } else if (status === 401) {
        category = ErrorCategory.AUTHENTICATION;
        severity = ErrorSeverity.ERROR;
      } else if (status === 403) {
        category = ErrorCategory.PERMISSION;
        severity = ErrorSeverity.ERROR;
      } else if (status === 404) {
        category = ErrorCategory.RESOURCE;
        severity = ErrorSeverity.WARNING;
      } else if (status === 422) {
        category = ErrorCategory.VALIDATION;
        severity = ErrorSeverity.WARNING;
      }
    } else if (error.message) {
      errorType = error.name || 'ObjectError';
      errorMessage = error.message;
      
      // 如果有服务器返回的错误类别和严重程度，使用它们
      if (error.category) category = error.category;
      if (error.severity) severity = error.severity;
      if (error.is_fatal !== undefined) isFatal = error.is_fatal;
    }
  }
  
  // 构建标准错误报告
  return {
    type: errorType,
    message: errorMessage,
    timestamp: new Date().toISOString(),
    category: category,
    severity: severity,
    is_fatal: isFatal,
    task_id: taskId || error.task_id,
    context: context || error.context || {},
    traceback: error.traceback || '',
    originalError: error
  };
}

/**
 * 显示错误消息
 * 根据错误严重程度选择不同的显示方式
 * 
 * @param error 错误对象
 * @param options 选项
 */
export function showError(error: any, options?: {
  title?: string;
  duration?: number;
  showDetails?: boolean;
}) {
  const errorReport = formatError(error);
  const title = options?.title || `${getSeverityDisplayName(errorReport.severity)}: ${getCategoryDisplayName(errorReport.category)}`;
  const duration = options?.duration || 5000;
  
  // 根据严重程度选择不同的显示方式
  switch (errorReport.severity) {
    case ErrorSeverity.CRITICAL:
      // 严重错误使用通知
      ElNotification({
        title,
        message: errorReport.message,
        type: 'error',
        duration: 0, // 不自动关闭
        showClose: true
      });
      break;
      
    case ErrorSeverity.ERROR:
      // 一般错误使用通知
      ElNotification({
        title,
        message: errorReport.message,
        type: 'error',
        duration,
        showClose: true
      });
      break;
      
    case ErrorSeverity.WARNING:
      // 警告使用消息
      ElMessage({
        message: errorReport.message,
        type: 'warning',
        duration,
        showClose: true
      });
      break;
      
    case ErrorSeverity.INFO:
    default:
      // 信息使用消息
      ElMessage({
        message: errorReport.message,
        type: 'info',
        duration,
        showClose: true
      });
      break;
  }
  
  // 记录到控制台
  console.error('错误详情:', errorReport);
  
  return errorReport;
}

/**
 * 处理API错误
 * 专门处理API请求错误
 * 
 * @param error API错误对象
 * @param options 选项
 */
export function handleApiError(error: any, options?: {
  title?: string;
  silent?: boolean;
  taskId?: string;
  context?: any;
}) {
  const errorReport = formatError(error, options?.taskId, options?.context);
  
  // 如果不是静默模式，显示错误
  if (!options?.silent) {
    showError(errorReport, {
      title: options?.title || '请求失败'
    });
  }
  
  return errorReport;
}

/**
 * 处理WebSocket错误
 * 专门处理WebSocket连接错误
 * 
 * @param error WebSocket错误对象
 * @param options 选项
 */
export function handleWebSocketError(error: any, options?: {
  title?: string;
  silent?: boolean;
  taskId?: string;
  context?: any;
}) {
  // 确保错误类别是网络相关
  let errorReport = formatError(error, options?.taskId, options?.context);
  if (errorReport.category !== ErrorCategory.NETWORK && 
      errorReport.category !== ErrorCategory.CONNECTION) {
    errorReport.category = ErrorCategory.CONNECTION;
  }
  
  // 如果不是静默模式，显示错误
  if (!options?.silent) {
    showError(errorReport, {
      title: options?.title || 'WebSocket连接错误'
    });
  }
  
  return errorReport;
}

export default {
  formatError,
  showError,
  handleApiError,
  handleWebSocketError
};
