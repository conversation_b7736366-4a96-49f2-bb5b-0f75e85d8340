/**
 * 数据库API端点配置
 *
 * 统一管理所有数据库相关的API路径，确保路径的一致性和可维护性
 */

// API基础路径配置
// 注意：DatabaseApiClient 的 baseURL 是 'http://localhost:8000/api'，所以这里的路径不应包含 /api 前缀
// 重要：Django的APPEND_SLASH=True会自动重定向到带斜杠的URL，所以端点应该以斜杠结尾
export const DATABASE_API_BASE = '/database';

// 数据库基础操作端点
export const DATABASE_ENDPOINTS = {
  // 数据库类型
  TYPES: '/database/types',

  // 数据库对象和结构
  SOURCE_OBJECTS: (sourceId: string) => `/database/source/${sourceId}/objects`,
  TABLE_STRUCTURE: (sourceId: string, schemaName: string, tableName: string) =>
    `/database/source/${sourceId}/table/${encodeURIComponent(schemaName)}/${encodeURIComponent(tableName)}`,
  TABLE_DATA: (sourceId: string, schemaName: string, tableName: string) =>
    `/database/source/${sourceId}/table/${encodeURIComponent(schemaName)}/${encodeURIComponent(tableName)}/data`,

  // SQL执行
  EXECUTE_QUERY: (sourceId: string) => `/database/source/${sourceId}/query`,
  SQL_COMPLETION: (sourceId: string) => `/database/source/${sourceId}/sql/completion`,

  // 数据库模式
  SCHEMAS: (sourceId: string) => `/database/source/${sourceId}/schemas`,
} as const;

// 比较任务端点
export const COMPARE_ENDPOINTS = {
  // 比较模型
  MODELS: `${DATABASE_API_BASE}/model`,
  MODEL_DETAIL: (id: string) => `${DATABASE_API_BASE}/model/${id}`,

  // 比较任务
  TASKS: `${DATABASE_API_BASE}/task`,
  TASK_DETAIL: (id: string) => `${DATABASE_API_BASE}/task/${id}`,
  TASK_STATUS: (id: string) => `${DATABASE_API_BASE}/task/${id}/status`,
  TASK_CONTROL: (id: string) => `${DATABASE_API_BASE}/task/${id}/control`,

  // 任务操作
  PRECHECK: `${DATABASE_API_BASE}/task/precheck`,
  START_TASK: '/compare_data',
  DIRECT_COMPARISON: '/compare_data',
  LAUNCH_TASK: `${DATABASE_API_BASE}/task/launch/`, // 修复任务启动端点
} as const;

// 连接管理端点
export const CONNECTION_ENDPOINTS = {
  BASE: `${DATABASE_API_BASE}/connection/`,
  LIST: `${DATABASE_API_BASE}/connection/`,
  DETAIL: (id: string) => `${DATABASE_API_BASE}/connection/${id}/`,
  TEST: (id: string) => `${DATABASE_API_BASE}/connection/${id}/test/`,
  BATCH_TEST: `${DATABASE_API_BASE}/connection/batch-test/`,
  DEBUG: `${DATABASE_API_BASE}/connection/debug/`,
} as const;

// 差异数据端点
export const DIFF_ENDPOINTS = {
  LIST: (taskId: string) => `${DATABASE_API_BASE}/diff`,
  DETAIL: (id: string) => `${DATABASE_API_BASE}/diff/${id}`,
  EXPORT: (taskId: string) => `${DATABASE_API_BASE}/diff/${taskId}/export`,
  ANALYZE: (taskId: string) => `${DATABASE_API_BASE}/diff/${taskId}/analyze`,
} as const;

// 比对结果端点
export const RESULT_ENDPOINTS = {
  RESULT: (taskId: string) => `${DATABASE_API_BASE}/task/${taskId}/result/`,
  DIFFERENCES: (taskId: string) => `${DATABASE_API_BASE}/task/${taskId}/differences/`,
  SOURCE_ONLY: (taskId: string) => `${DATABASE_API_BASE}/task/${taskId}/source-only/`,
  TARGET_ONLY: (taskId: string) => `${DATABASE_API_BASE}/task/${taskId}/target-only/`,
  SYNC: `${DATABASE_API_BASE}/comparison/sync/`,
} as const;

// 元数据端点
export const METADATA_ENDPOINTS = {
  DATASOURCES: '/database/meta/datasources',
  DATASOURCE_DETAIL: (id: string) => `/database/meta/datasources/${id}`,
  DATABASES: (datasourceId: string) => `/database/meta/${datasourceId}/databases`,
  TABLES: (datasourceId: string, database: string) =>
    `/database/meta/${datasourceId}/databases/${database}/tables`,
  TABLE_SCHEMA: (datasourceId: string, database: string, table: string) =>
    `/database/meta/${datasourceId}/databases/${database}/tables/${table}/schema`,
  TABLE_DATA: (datasourceId: string, database: string, table: string) =>
    `/database/meta/${datasourceId}/databases/${database}/tables/${table}/data`,
  TABLE_COUNT: (datasourceId: string, database: string, table: string) =>
    `/database/meta/${datasourceId}/databases/${database}/tables/${table}/count`,
} as const;

// WebSocket端点
export const WEBSOCKET_ENDPOINTS = {
  TASK_STATUS: (taskId: string) => `/ws/database/task/${taskId}`,
  TASK_NOTIFICATIONS: '/ws/database/tasks/notifications',
} as const;

/**
 * 端点路径管理器
 * 提供路径构建和参数替换的工具方法
 */
export class EndpointManager {
  /**
   * 构建带参数的URL
   * @param template URL模板
   * @param params 参数对象
   * @returns 构建后的URL
   */
  static buildUrl(template: string, params: Record<string, string | number>): string {
    return template.replace(/{(\w+)}/g, (match, key) => {
      const value = params[key];
      if (value === undefined) {
        console.warn(`URL参数 "${key}" 未提供，保留原始模板: ${match}`);
        return match;
      }
      return encodeURIComponent(String(value));
    });
  }

  /**
   * 验证端点路径是否有效
   * @param endpoint 端点路径
   * @returns 是否有效
   */
  static isValidEndpoint(endpoint: string): boolean {
    return typeof endpoint === 'string' && endpoint.length > 0;
  }

  /**
   * 获取完整的API URL
   * @param endpoint 端点路径
   * @param baseUrl 基础URL（可选）
   * @returns 完整的API URL
   */
  static getFullUrl(endpoint: string, baseUrl?: string): string {
    const base = baseUrl || '';

    // 如果端点已经是完整URL，直接返回
    if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
      return endpoint;
    }

    // 如果端点以 / 开头，直接拼接
    if (endpoint.startsWith('/')) {
      return `${base}${endpoint}`;
    }

    // 否则添加 / 分隔符
    return `${base}/${endpoint}`;
  }
}

/**
 * 端点路径常量集合
 * 方便统一导入和使用
 */
export const ENDPOINTS = {
  DATABASE: DATABASE_ENDPOINTS,
  COMPARE: COMPARE_ENDPOINTS,
  CONNECTION: CONNECTION_ENDPOINTS,
  DIFF: DIFF_ENDPOINTS,
  RESULT: RESULT_ENDPOINTS,
  METADATA: METADATA_ENDPOINTS,
  WEBSOCKET: WEBSOCKET_ENDPOINTS,
} as const;

export default ENDPOINTS;
