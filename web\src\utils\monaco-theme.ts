/**
 * Monaco Editor主题配置
 */
import type * as Monaco from 'monaco-editor';

// SQL主题定义 - 亮色
export const sqlLightTheme = {
  base: 'vs' as const,
  inherit: true,
  rules: [
    { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' },
    { token: 'predefined', foreground: '6F42C1', fontStyle: 'bold' },
    { token: 'string', foreground: '008000' },
    { token: 'operator', foreground: 'D73A49' },
    { token: 'number', foreground: '005CC5' },
    { token: 'comment', foreground: '6A737D', fontStyle: 'italic' },
    { token: 'delimiter', foreground: '000000' },
    { token: 'delimiter.square', foreground: '000000' },
    { token: 'delimiter.parenthesis', foreground: '000000' },
  ],
  colors: {
    'editor.foreground': '#000000',
    'editor.background': '#FFFFFF',
    'editorCursor.foreground': '#000000',
    'editor.lineHighlightBackground': '#F0F0F0',
    'editorLineNumber.foreground': '#999999',
    'editor.selectionBackground': '#ADD6FF',
    'editor.inactiveSelectionBackground': '#E5EBF1',
  }
};

// SQL主题定义 - 暗色
export const sqlDarkTheme = {
  base: 'vs-dark' as const,
  inherit: true,
  rules: [
    { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
    { token: 'predefined', foreground: 'DCDCAA', fontStyle: 'bold' },
    { token: 'string', foreground: 'CE9178' },
    { token: 'operator', foreground: 'D4D4D4' },
    { token: 'number', foreground: 'B5CEA8' },
    { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
    { token: 'delimiter', foreground: 'D4D4D4' },
    { token: 'delimiter.square', foreground: 'D4D4D4' },
    { token: 'delimiter.parenthesis', foreground: 'D4D4D4' },
  ],
  colors: {
    'editor.foreground': '#D4D4D4',
    'editor.background': '#1E1E1E',
    'editorCursor.foreground': '#D4D4D4',
    'editor.lineHighlightBackground': '#2D2D30',
    'editorLineNumber.foreground': '#858585',
    'editor.selectionBackground': '#264F78',
    'editor.inactiveSelectionBackground': '#3A3D41',
  }
};

/**
 * 注册Monaco Editor主题
 * @param monaco Monaco实例
 */
export function registerMonacoThemes(monaco: typeof Monaco): void {
  // 注册亮色主题
  monaco.editor.defineTheme('sql-light', sqlLightTheme);
  
  // 注册暗色主题
  monaco.editor.defineTheme('sql-dark', sqlDarkTheme);
}

/**
 * Monaco编辑器主题工具函数
 * 用于统一管理编辑器主题和获取当前主题
 */

// 可用的编辑器主题
export const MONACO_THEMES = {
  LIGHT: 'vs',
  DARK: 'vs-dark',
  HIGH_CONTRAST: 'hc-black'
};

/**
 * 获取当前编辑器主题
 * 根据当前系统主题或用户设置自动选择合适的主题
 * @returns 当前主题名称
 */
export function getCurrentTheme(): string {
  // 检查Element Plus的深色模式 - 优先检查
  if (document.documentElement.classList.contains('dark') || 
      document.body.getAttribute('data-theme') === 'dark' ||
      document.documentElement.getAttribute('data-theme') === 'dark' ||
      document.documentElement.style.getPropertyValue('--el-bg-color').trim() === '#141414') {
    // 更新localStorage以确保与当前UI状态匹配
    localStorage.setItem('monacoTheme', MONACO_THEMES.DARK);
    return MONACO_THEMES.DARK;
  }
  
  // 检查系统偏好
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    // 如果系统是深色，但UI不是深色，则不使用深色主题
    if (!document.documentElement.classList.contains('dark') && 
        document.documentElement.getAttribute('data-theme') !== 'dark' && 
        document.body.getAttribute('data-theme') !== 'dark') {
      localStorage.setItem('monacoTheme', MONACO_THEMES.LIGHT);
      return MONACO_THEMES.LIGHT;
    }
    
    localStorage.setItem('monacoTheme', MONACO_THEMES.DARK);
    return MONACO_THEMES.DARK;
  }
  
  // 如果所有深色模式检查都未通过，则使用浅色主题
  localStorage.setItem('monacoTheme', MONACO_THEMES.LIGHT);
  return MONACO_THEMES.LIGHT;
}

/**
 * 设置当前编辑器主题
 * @param theme 主题名称
 */
export function setCurrentTheme(theme: string): void {
  if (Object.values(MONACO_THEMES).includes(theme)) {
    localStorage.setItem('monacoTheme', theme);
    
    // 如果monaco已加载，直接设置全局主题
    if (typeof window !== 'undefined' && (window as any).monaco) {
      try {
        (window as any).monaco.editor.setTheme(theme);
      } catch (error) {
        console.error('设置Monaco编辑器主题失败:', error);
      }
    }
  }
}

/**
 * 切换编辑器主题（亮/暗）
 */
export function toggleTheme(): string {
  const currentTheme = getCurrentTheme();
  const newTheme = currentTheme === MONACO_THEMES.DARK ? MONACO_THEMES.LIGHT : MONACO_THEMES.DARK;
  setCurrentTheme(newTheme);
  return newTheme;
}

// 导出主题管理
export default {
  registerMonacoThemes,
  getCurrentTheme,
  sqlLightTheme,
  sqlDarkTheme
}; 