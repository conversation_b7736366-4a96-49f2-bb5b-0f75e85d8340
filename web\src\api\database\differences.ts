/**
 * 比较差异数据API
 *
 * 提供比较任务产生的差异数据查询、分析、导出等功能
 * 重构版本 - 统一使用DatabaseApiClient和标准化端点
 */

import { databaseApiClient, withDatabaseErrorHandling } from './client';
import { DIFF_ENDPOINTS } from './endpoints';
import type { BaseApiResponse } from './types';
import type { CompareDifference } from '@/types/database';
import {
  DatabaseErrorCode,
  DatabaseApiError,
  createDatabaseError
} from './error';

// 扩展查询参数类型，支持sort参数
interface ExtendedCompareDiffQuery {
  page?: number;
  size?: number;
  sort?: string;
  diffType?: string;
  severity?: string;
  keyword?: string;
}

/**
 * 获取比对差异列表
 * @param taskId - 任务ID
 * @param params - 查询参数
 * @returns 比对差异列表
 */
export const getCompareDiffList = withDatabaseErrorHandling(
  async (taskId: string, params?: ExtendedCompareDiffQuery): Promise<BaseApiResponse<CompareDifference[]>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    return databaseApiClient.get<CompareDifference[]>(DIFF_ENDPOINTS.LIST(taskId), {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

// ===== 新增扩展功能 =====

/**
 * 获取差异统计信息
 * @param taskId - 任务ID
 */
export const getDiffStatistics = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<{
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    byTable: Record<string, number>;
    resolved: number;
    unresolved: number;
    trends?: {
      daily: Array<{ date: string; count: number }>;
      hourly: Array<{ hour: number; count: number }>;
    };
  }>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    return databaseApiClient.get(`${DIFF_ENDPOINTS.LIST(taskId)}statistics/`, {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 分析差异模式和趋势
 * @param taskId - 任务ID
 */
export const analyzeDifferences = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<{
    summary: {
      totalDifferences: number;
      criticalIssues: number;
      dataIntegrityIssues: number;
      schemaIssues: number;
    };
    patterns: Array<{
      pattern: string;
      count: number;
      description: string;
      suggestion: string;
    }>;
    recommendations: Array<{
      type: 'IMMEDIATE' | 'PLANNED' | 'OPTIONAL';
      title: string;
      description: string;
      impact: 'HIGH' | 'MEDIUM' | 'LOW';
    }>;
  }>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    return databaseApiClient.post(DIFF_ENDPOINTS.ANALYZE(taskId), {}, {
      showErrorMessage: true
    });
  }
);

/**
 * 获取差异详情
 * @param taskId - 任务ID
 * @param diffId - 差异ID
 */
export const getDiffDetail = withDatabaseErrorHandling(
  async (taskId: string, diffId: string): Promise<BaseApiResponse<CompareDifference & {
    context?: {
      beforeValue: any;
      afterValue: any;
      relatedDiffs: string[];
      affectedColumns: string[];
    };
  }>> => {
    if (!taskId || !diffId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID和差异ID不能为空'
      );
    }

    return databaseApiClient.get(DIFF_ENDPOINTS.DETAIL(diffId), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 标记差异为已解决
 * @param taskId - 任务ID
 * @param diffId - 差异ID
 * @param resolution - 解决方案
 */
export const resolveDiff = withDatabaseErrorHandling(
  async (
    taskId: string,
    diffId: string,
    resolution?: {
      action: 'IGNORE' | 'FIXED' | 'ACCEPTED';
      comment?: string;
      resolvedBy?: string;
    }
  ): Promise<BaseApiResponse<null>> => {
    if (!taskId || !diffId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID和差异ID不能为空'
      );
    }

    const response = await databaseApiClient.post<null>(
      `${DIFF_ENDPOINTS.DETAIL(diffId)}resolve/`,
      resolution,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除差异相关缓存
    databaseApiClient.clearCache('diff');

    return response;
  }
);

/**
 * 批量解决差异
 * @param taskId - 任务ID
 * @param diffIds - 差异ID列表
 * @param resolution - 解决方案
 */
export const batchResolveDifferences = withDatabaseErrorHandling(
  async (
    taskId: string,
    diffIds: string[],
    resolution: {
      action: 'IGNORE' | 'FIXED' | 'ACCEPTED';
      comment?: string;
      resolvedBy?: string;
    }
  ): Promise<BaseApiResponse<{
    success: boolean;
    processed: number;
    succeeded: number;
    failed: number;
    errors: Array<{
      diffId: string;
      error: string;
    }>;
  }>> => {
    if (!taskId || !diffIds || diffIds.length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID和差异ID列表不能为空'
      );
    }

    if (diffIds.length > 1000) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '批量解决差异数量不能超过1000个'
      );
    }

    const response = await databaseApiClient.post(
      `${DIFF_ENDPOINTS.LIST(taskId)}batch-resolve/`,
      {
        diffIds,
        resolution
      },
      {
        showSuccessMessage: true,
        showErrorMessage: true,
        timeout: 60000 // 批量操作可能需要更长时间
      }
    );

    // 清除差异相关缓存
    databaseApiClient.clearCache('diff');

    return response;
  }
);

/**
 * 导出差异数据
 * @param taskId - 任务ID
 * @param options - 导出选项
 */
export const exportDifferences = withDatabaseErrorHandling(
  async (
    taskId: string,
    options: {
      format: 'csv' | 'excel' | 'json' | 'pdf';
      includeResolved?: boolean;
      includeMetadata?: boolean;
      columns?: string[];
      filters?: ExtendedCompareDiffQuery;
    }
  ): Promise<BaseApiResponse<{
    downloadUrl: string;
    fileName: string;
    fileSize: number;
    expiresAt: string;
  }>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    return databaseApiClient.post(DIFF_ENDPOINTS.EXPORT(taskId), options, {
      showSuccessMessage: true,
      showErrorMessage: true,
      timeout: 30000 // 导出可能需要较长时间
    });
  }
);

/**
 * 获取差异变更历史
 * @param taskId - 任务ID
 * @param diffId - 差异ID
 */
export const getDiffHistory = withDatabaseErrorHandling(
  async (taskId: string, diffId: string): Promise<BaseApiResponse<Array<{
    timestamp: string;
    action: string;
    user: string;
    comment?: string;
    changes: Record<string, any>;
  }>>> => {
    if (!taskId || !diffId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID和差异ID不能为空'
      );
    }

    return databaseApiClient.get(`${DIFF_ENDPOINTS.DETAIL(diffId)}history/`, {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 快速操作：忽略所有低严重性差异
 * @param taskId - 任务ID
 */
export const ignoreAllLowSeverityDiffs = withDatabaseErrorHandling(
  async (taskId: string): Promise<BaseApiResponse<{
    processed: number;
    succeeded: number;
    failed: number;
  }>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    // 首先获取所有低严重性差异
    const diffListResponse = await getCompareDiffList(taskId, {
      severity: 'LOW',
      size: 1000 // 获取所有低严重性差异
    });

    if (!diffListResponse.data || diffListResponse.data.length === 0) {
      return {
        success: true,
        message: '没有找到低严重性差异',
        code: 200,
        timestamp: new Date().toISOString(),
        data: { processed: 0, succeeded: 0, failed: 0 }
      };
    }

    // 批量忽略
    const diffIds = diffListResponse.data.map((diff: any) => diff.id);
    const result = await batchResolveDifferences(taskId, diffIds, {
      action: 'IGNORE',
      comment: '批量忽略低严重性差异',
      resolvedBy: 'system'
    });

    return {
      success: true,
      message: '批量忽略低严重性差异完成',
      code: 200,
      timestamp: new Date().toISOString(),
      data: {
        processed: result.data?.processed || 0,
        succeeded: result.data?.succeeded || 0,
        failed: result.data?.failed || 0
      }
    };
  }
);

/**
 * 快速操作：导出所有未解决的差异
 * @param taskId - 任务ID
 * @param format - 导出格式
 */
export const exportUnresolvedDiffs = withDatabaseErrorHandling(
  async (taskId: string, format: 'csv' | 'excel' | 'json' | 'pdf' = 'excel'): Promise<BaseApiResponse<{
    downloadUrl: string;
    fileName: string;
    fileSize: number;
    expiresAt: string;
  }>> => {
    if (!taskId) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '任务ID不能为空'
      );
    }

    return exportDifferences(taskId, {
      format,
      includeResolved: false,
      includeMetadata: true,
      columns: ['tableName', 'diffType', 'severity', 'sourceData', 'targetData']
    });
  }
);
