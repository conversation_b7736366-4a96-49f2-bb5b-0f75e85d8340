"""
基于Agent标准的视图
只读取Agent写入的数据，通过简单的HTTP请求与Agent交互

设计原则：
1. 所有数据读取都来自Agent写入的数据库表
2. 任务创建通过HTTP请求发送给Agent
3. 不直接修改Agent管理的表结构
4. 提供实时的进度和结果查询
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count
from django.utils import timezone
from django.conf import settings
import requests
import logging

from .models_agent_standard import (
    ComparisonConnection, 
    ComparisonModel, 
    ComparisonTableRule, 
    ComparisonTask, 
    ComparisonResult,
    User
)
from .serializers_agent_standard import (
    AgentConnectionSerializer,
    AgentComparisonModelSerializer,
    AgentTableRuleSerializer,
    AgentComparisonTaskSerializer,
    AgentComparisonResultSerializer,
    AgentTaskSummarySerializer,
    AgentTaskProgressSerializer,
    AgentDashboardStatsSerializer,
    TaskCreateRequestSerializer,
    TaskControlRequestSerializer,
    AgentHealthCheckSerializer
)

logger = logging.getLogger(__name__)


class AgentConnectionViewSet(viewsets.ReadOnlyModelViewSet):
    """Agent数据库连接视图集 - 只读"""
    queryset = ComparisonConnection.objects.all()
    serializer_class = AgentConnectionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """过滤活跃连接"""
        queryset = super().get_queryset()
        if self.request.query_params.get('active_only', 'false').lower() == 'true':
            queryset = queryset.filter(status='active')
        return queryset.order_by('-create_time')
    
    @action(detail=True, methods=['get'])
    def test_connection(self, request, pk=None):
        """测试数据库连接（通过Agent）"""
        connection = self.get_object()
        
        try:
            # 调用Agent的连接测试API
            agent_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
            response = requests.post(
                f"{agent_url}/api/v1/connections/{pk}/test",
                timeout=30
            )
            
            if response.status_code == 200:
                return Response({
                    'success': True,
                    'message': '连接测试成功',
                    'details': response.json()
                })
            else:
                return Response({
                    'success': False,
                    'message': '连接测试失败',
                    'error': response.text
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except requests.RequestException as e:
            return Response({
                'success': False,
                'message': 'Agent连接失败',
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


class AgentComparisonModelViewSet(viewsets.ReadOnlyModelViewSet):
    """Agent比对模型视图集 - 只读"""
    queryset = ComparisonModel.objects.select_related(
        'source_connid', 'target_connid'
    ).prefetch_related('table_rules')
    serializer_class = AgentComparisonModelSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """过滤活跃模型"""
        queryset = super().get_queryset()
        if self.request.query_params.get('active_only', 'false').lower() == 'true':
            queryset = queryset.filter(status=True)
        return queryset.order_by('-create_time')
    
    @action(detail=True, methods=['get'])
    def table_rules(self, request, pk=None):
        """获取模型的表规则"""
        model = self.get_object()
        rules = model.table_rules.filter(is_active=True)
        serializer = AgentTableRuleSerializer(rules, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def recent_tasks(self, request, pk=None):
        """获取模型的最近任务"""
        model = self.get_object()
        tasks = ComparisonTask.objects.filter(
            model_id=model
        ).order_by('-create_time')[:10]
        
        serializer = AgentTaskSummarySerializer(tasks, many=True)
        return Response(serializer.data)


class AgentComparisonTaskViewSet(viewsets.ReadOnlyModelViewSet):
    """Agent比对任务视图集 - 只读查询，通过Agent API创建"""
    queryset = ComparisonTask.objects.select_related(
        'user_id', 'model_id', 'table_rule_id'
    )
    serializer_class = AgentComparisonTaskSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """支持多种过滤条件"""
        queryset = super().get_queryset()
        
        # 按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 按模型过滤
        model_id = self.request.query_params.get('model_id')
        if model_id:
            queryset = queryset.filter(model_id=model_id)
        
        # 按用户过滤
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 按时间范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(create_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(create_time__lte=end_date)
        
        return queryset.order_by('-create_time')
    
    @action(detail=False, methods=['post'])
    def create_task(self, request):
        """创建比对任务（通过Agent API）"""
        serializer = TaskCreateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 调用Agent的任务创建API
            agent_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
            response = requests.post(
                f"{agent_url}/api/v1/tasks/create",
                json=serializer.validated_data,
                timeout=30
            )
            
            if response.status_code == 201:
                task_data = response.json()
                return Response({
                    'success': True,
                    'message': '任务创建成功',
                    'task_id': task_data.get('task_id'),
                    'data': task_data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '任务创建失败',
                    'error': response.text
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except requests.RequestException as e:
            return Response({
                'success': False,
                'message': 'Agent连接失败',
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    @action(detail=True, methods=['post'])
    def control_task(self, request, pk=None):
        """控制任务（通过Agent API）"""
        task = self.get_object()
        
        serializer = TaskControlRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 调用Agent的任务控制API
            agent_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
            response = requests.post(
                f"{agent_url}/api/v1/tasks/{task.task_id}/control",
                json=serializer.validated_data,
                timeout=30
            )
            
            if response.status_code == 200:
                return Response({
                    'success': True,
                    'message': f'任务{serializer.validated_data["action"]}成功',
                    'data': response.json()
                })
            else:
                return Response({
                    'success': False,
                    'message': f'任务{serializer.validated_data["action"]}失败',
                    'error': response.text
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except requests.RequestException as e:
            return Response({
                'success': False,
                'message': 'Agent连接失败',
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """获取任务实时进度"""
        task = self.get_object()
        serializer = AgentTaskProgressSerializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """获取任务比对结果"""
        task = self.get_object()
        
        # 分页参数
        page_size = int(request.query_params.get('page_size', 50))
        page = int(request.query_params.get('page', 1))
        offset = (page - 1) * page_size
        
        # 过滤参数
        status_filter = request.query_params.get('status')
        table_filter = request.query_params.get('table_name')
        
        # 构建查询
        results = ComparisonResult.objects.filter(task_id=task.task_id)
        
        if status_filter:
            results = results.filter(status=status_filter)
        if table_filter:
            results = results.filter(table_name=table_filter)
        
        # 统计信息
        total_count = results.count()
        
        # 分页查询
        results = results.order_by('-created_at')[offset:offset + page_size]
        
        serializer = AgentComparisonResultSerializer(results, many=True)
        
        return Response({
            'total': total_count,
            'page': page,
            'page_size': page_size,
            'results': serializer.data
        })


class AgentDashboardViewSet(viewsets.ViewSet):
    """Agent仪表板视图集"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取仪表板统计信息"""
        today = timezone.now().date()
        
        stats = {
            # 连接统计
            'total_connections': ComparisonConnection.objects.count(),
            'active_connections': ComparisonConnection.objects.filter(status='active').count(),
            
            # 模型统计
            'total_models': ComparisonModel.objects.count(),
            'active_models': ComparisonModel.objects.filter(status=True).count(),
            
            # 任务统计
            'total_tasks': ComparisonTask.objects.count(),
            'running_tasks': ComparisonTask.objects.filter(status='running').count(),
            'completed_tasks': ComparisonTask.objects.filter(status='completed').count(),
            'failed_tasks': ComparisonTask.objects.filter(status='failed').count(),
            
            # 今日统计
            'today_tasks': ComparisonTask.objects.filter(create_time__date=today).count(),
            'today_completed': ComparisonTask.objects.filter(
                create_time__date=today, status='completed'
            ).count(),
            
            # 结果统计
            'total_results': ComparisonResult.objects.count(),
            'total_differences': ComparisonResult.objects.filter(status__in=['D', 'S', 'T']).count(),
        }
        
        serializer = AgentDashboardStatsSerializer(stats)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def recent_tasks(self, request):
        """获取最近任务"""
        tasks = ComparisonTask.objects.select_related(
            'model_id', 'user_id'
        ).order_by('-create_time')[:20]
        
        serializer = AgentTaskSummarySerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def running_tasks(self, request):
        """获取运行中的任务"""
        tasks = ComparisonTask.objects.filter(
            status='running'
        ).select_related('model_id', 'user_id').order_by('-start_time')
        
        serializer = AgentTaskProgressSerializer(tasks, many=True)
        return Response(serializer.data)


class AgentHealthViewSet(viewsets.ViewSet):
    """Agent健康检查视图集"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def check(self, request):
        """检查Agent健康状态"""
        try:
            # 调用Agent健康检查API
            agent_url = getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001')
            response = requests.get(
                f"{agent_url}/api/v1/health",
                timeout=10
            )
            
            if response.status_code == 200:
                agent_health = response.json()
                
                # 补充数据库连接状态
                try:
                    db_status = 'healthy'
                    ComparisonConnection.objects.count()  # 测试数据库连接
                except Exception:
                    db_status = 'error'
                
                health_data = {
                    'agent_status': 'healthy',
                    'database_status': db_status,
                    'last_heartbeat': timezone.now(),
                    'active_tasks': ComparisonTask.objects.filter(status='running').count(),
                    'system_load': agent_health.get('system_load', {})
                }
                
                serializer = AgentHealthCheckSerializer(health_data)
                return Response(serializer.data)
            else:
                return Response({
                    'agent_status': 'error',
                    'database_status': 'unknown',
                    'error': 'Agent响应异常'
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
        except requests.RequestException as e:
            return Response({
                'agent_status': 'offline',
                'database_status': 'unknown',
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
