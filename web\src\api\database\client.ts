/**
 * 数据库API客户端 - 简化版本
 * 专注于核心API调用功能
 */
import router from '@/router';
import { DatabaseApiError, handleDatabaseError } from './error';
import { ErrorCode, BaseApiResponse } from './types';
import { getToken, formatToken, removeToken } from '@/utils/auth';
import { useUserStoreHook } from '@/store/modules/user';

// ===== 类型定义 =====

/**
 * 请求选项接口
 */
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  useCache?: boolean;
  cacheTTL?: number;
  showSuccessMessage?: boolean;
  showErrorMessage?: boolean;
  responseType?: 'json' | 'blob' | 'text';
}

// ===== 数据库API客户端类 =====

export class DatabaseApiClient {
  private baseURL: string;
  private cache: Map<string, any>;

  constructor(config?: {
    baseURL?: string;
    timeout?: number;
  }) {
    this.baseURL = config?.baseURL || import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
    this.cache = new Map();
  }

  // 基础请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<BaseApiResponse<T>> {
    const requestId = Math.random().toString(36).substring(2, 11);
    console.log(`🚀 [${requestId}] DatabaseApiClient.request 开始`);
    console.log(`📍 [${requestId}] 端点:`, endpoint);
    console.log(`⚙️ [${requestId}] 选项:`, options);
    console.log(`🔗 [${requestId}] 完整URL:`, `${this.baseURL}${endpoint}`);

    try {
      // 准备请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(options.headers as Record<string, string> || {}),
      };

      // 添加认证头
      const tokenData = getToken();
      if (tokenData) {
        const now = new Date().getTime();
        const expired = parseInt(tokenData.expires) - now <= 0;

        if (expired) {
          // Token已过期，尝试刷新
          try {
            const res = await useUserStoreHook().handRefreshToken({
              refreshToken: tokenData.refreshToken
            });
            headers["Authorization"] = formatToken(res.data.accessToken);
          } catch (error) {
            console.error("Token刷新失败:", error);
            removeToken();
            router.push("/login");
            throw new Error("Token刷新失败，请重新登录");
          }
        } else {
          // Token未过期，直接使用
          headers["Authorization"] = formatToken(tokenData.accessToken);
        }
      }

      // 构建URL并清理双斜杠问题
      const url = this.buildUrl(endpoint);

      const response = await fetch(url, {
        headers,
        ...options,
      });

      if (!response.ok) {
        throw new DatabaseApiError(
          `请求失败: ${response.status}`,
          this.mapStatusToErrorCode(response.status),
          endpoint
        );
      }

      const data = await response.json();
      console.log(`📦 [${requestId}] 原始响应数据:`, data);

      // 检查Django API的响应格式并转换为BaseApiResponse格式
      if (data && typeof data === 'object') {
        // Django API格式: { code: 0, message: "...", data: [...], total?: number }
        if ('code' in data && 'message' in data && 'data' in data) {
          console.log(`🔄 [${requestId}] 检测到Django API格式，开始转换`);
          console.log(`📊 [${requestId}] Django响应 - code: ${data.code}, message: ${data.message}`);

          const convertedData = {
            success: data.code === 0, // Django使用code=0表示成功
            code: data.code === 0 ? 200 : (data.code || 500),
            message: data.message || '请求成功',
            data: data.data as T,
            timestamp: new Date().toISOString(),
            total: data.total // 保留total字段用于分页
          };

          console.log(`✅ [${requestId}] 转换完成:`, convertedData);
          return convertedData;
        }

        // 标准BaseApiResponse格式
        if ('success' in data && 'code' in data) {
          console.log(`✨ [${requestId}] 检测到标准BaseApiResponse格式`);
          return data as BaseApiResponse<T>;
        }
      }

      // 否则包装成 BaseApiResponse 格式
      console.log(`📦 [${requestId}] 包装为BaseApiResponse格式`);
      const wrappedData = {
        success: true,
        code: 200,
        message: '请求成功',
        data: data as T,
        timestamp: new Date().toISOString()
      };
      console.log(`🎁 [${requestId}] 包装完成:`, wrappedData);
      return wrappedData;
    } catch (error) {
      console.error('API请求失败:', error);
      throw handleDatabaseError(error);
    }
  }

  /**
   * 构建完整的请求URL，避免双斜杠问题
   * @param endpoint - API端点
   * @returns 完整的URL
   */
  private buildUrl(endpoint: string): string {
    // 确保baseURL不以斜杠结尾，endpoint以斜杠开头
    const cleanBaseURL = this.baseURL.replace(/\/+$/, '');
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

    // 构建URL并清理双斜杠（但保留协议中的双斜杠）
    const url = `${cleanBaseURL}${cleanEndpoint}`.replace(/([^:]\/)\/+/g, '$1');

    // 验证URL格式
    if (url.includes('//') && !url.startsWith('http')) {
      console.warn(`⚠️ 检测到可能的双斜杠问题: ${url}`);
    }

    return url;
  }

  private mapStatusToErrorCode(status: number): ErrorCode {
    // 直接导入ErrorCode，避免异步导入
    switch (status) {
      case 400: return ErrorCode.QUERY_SYNTAX_ERROR;
      case 401: return ErrorCode.CONNECTION_FAILED;
      case 404: return ErrorCode.TABLE_NOT_FOUND;
      case 500: return ErrorCode.INTERNAL_ERROR;
      default: return ErrorCode.NETWORK_ERROR;
    }
  }

  // ===== HTTP方法 =====

  /**
   * GET请求
   */
  async get<T = any>(url: string, options?: RequestOptions): Promise<BaseApiResponse<T>> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'GET'
    };
    return this.request<T>(url, requestOptions);
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, options?: RequestOptions): Promise<BaseApiResponse<T>> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    };
    return this.request<T>(url, requestOptions);
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, options?: RequestOptions): Promise<BaseApiResponse<T>> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    };
    return this.request<T>(url, requestOptions);
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, options?: RequestOptions): Promise<BaseApiResponse<T>> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'DELETE'
    };
    return this.request<T>(url, requestOptions);
  }

  /**
   * 清理缓存
   */
  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

}

/**
 * 错误处理装饰器
 */
export function withDatabaseErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T
): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }) as T;
}

/**
 * 默认数据库API客户端实例
 */
export const databaseApiClient = new DatabaseApiClient();

export default databaseApiClient;
