<template>
  <div class="execute-params-container">
    <el-form ref="formRef" :model="params" :rules="rules" label-width="140px">
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        <span class="divider-title">基本配置</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="task_name">
            <el-input v-model="params.task_name" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行超时(分钟)" prop="timeoutMinutes">
            <el-input-number 
              v-model="params.timeoutMinutes" 
              :min="1" 
              :max="1440"
              style="width: 100%" 
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-divider content-position="left">
        <el-icon><DataAnalysis /></el-icon>
        <span class="divider-title">比对配置</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="比对模式" prop="compareMode">
            <el-radio-group v-model="params.compareMode">
              <el-radio-button value="FULL">全量比对</el-radio-button>
              <el-radio-button value="SAMPLE">抽样比对</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="比对对象" prop="compareObjects">
            <el-checkbox-group v-model="params.compareObjects">
              <el-checkbox value="TABLE">表结构</el-checkbox>
              <el-checkbox value="DATA">数据内容</el-checkbox>
              <el-checkbox value="INDEX">索引</el-checkbox>
              <el-checkbox value="CONSTRAINT">约束</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="抽样比例 (%)" prop="sampleRate">
            <el-slider 
              v-model="params.sampleRate" 
              :min="1" 
              :max="100" 
              :step="1" 
              :show-input="true"
              :disabled="params.compareMode === 'FULL'"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结果存储方式" prop="resultStorage">
            <el-select v-model="params.resultStorage" style="width: 100%">
              <el-option label="仅保存差异" value="diff_only" />
              <el-option label="保存全部" value="all" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-divider content-position="left">
        <el-icon><Cpu /></el-icon>
        <span class="divider-title">执行选项</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="执行模式" prop="executionMode">
            <el-radio-group v-model="params.executionMode">
              <el-radio value="NORMAL">普通模式</el-radio>
              <el-radio value="FAST">快速模式 (2倍CPU核心)</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="异常处理" prop="errorHandling">
            <el-select v-model="params.errorHandling" placeholder="选择异常处理方式" style="width: 100%">
              <el-option label="继续执行" value="CONTINUE" />
              <el-option label="立即终止" value="STOP" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        <span class="divider-title">高级选项</span>
      </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="忽略大小写" prop="ignoreCase">
            <el-switch v-model="params.ignoreCase" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="忽略空白字符" prop="ignoreWhitespace">
            <el-switch v-model="params.ignoreWhitespace" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数值精度" prop="numericPrecision">
            <el-input-number 
              v-model="params.numericPrecision" 
              :min="0" 
              :max="10" 
              :step="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="日期精度" prop="datePrecision">
            <el-select v-model="params.datePrecision" placeholder="选择日期精度" style="width: 100%">
              <el-option label="秒" value="SECOND" />
              <el-option label="分钟" value="MINUTE" />
              <el-option label="小时" value="HOUR" />
              <el-option label="天" value="DAY" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Setting, DataAnalysis, Cpu } from '@element-plus/icons-vue';
import { getCompareModel } from '@/api/database/compare';

const props = defineProps({
  model: {
    type: Object,
    default: null
  },
  modelId: {
    type: [String, Number],
    default: ''
  }
});

// 表单引用
const formRef = ref(null);

// 可用字段
const availableFields = ref([]);

// 执行参数
const params = reactive({
  // 基本信息与模型关联
  model_id: '',
  task_name: '', // 初始为空，只有在选择模型后才设置
  source_connection_id: '',
  target_connection_id: '',
  timeoutMinutes: 60,
  
  // 比对配置
  compareMode: 'FULL',
  sampleRate: 100, // 默认设为100%
  compareObjects: ['TABLE', 'DATA'],
  resultStorage: 'diff_only',
  
  // 执行选项
  executionMode: 'NORMAL',
  errorHandling: 'CONTINUE',
  
  // 高级选项
  ignoreCase: false,
  ignoreWhitespace: false,
  numericPrecision: 2,
  datePrecision: 'SECOND'
});

// 校验规则
const rules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  compareMode: [{ required: true, message: '请选择比对模式', trigger: 'change' }],
  compareObjects: [{ 
    type: 'array', 
    required: true, 
    message: '请至少选择一项比对对象', 
    trigger: 'change' 
  }],
  sampleRate: [
    { required: true, message: '请设置抽样比例', trigger: 'change' },
    { type: 'number', min: 1, max: 100, message: '抽样比例必须在1%到100%之间', trigger: 'change' }
  ],
  executionMode: [{ required: true, message: '请选择执行模式', trigger: 'change' }],
  errorHandling: [{ required: true, message: '请选择异常处理方式', trigger: 'change' }],
  timeoutMinutes: [
    { required: true, message: '请设置超时时间', trigger: 'change' },
    { type: 'number', min: 1, max: 1440, message: '超时时间必须在1到1440分钟之间', trigger: 'change' }
  ],
  numericPrecision: [
    { required: true, message: '请设置数值精度', trigger: 'change' },
    { type: 'number', min: 0, max: 10, message: '数值精度必须在0到10之间', trigger: 'change' }
  ],
  datePrecision: [{ required: true, message: '请选择日期精度', trigger: 'change' }],
  resultStorage: [{ required: true, message: '请选择结果存储方式', trigger: 'change' }]
};

/**
 * 格式化日期时间字符串为年月日时
 */
function formatDateTimeString() {
  const now = new Date();
  return now.getFullYear() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0') +
    now.getHours().toString().padStart(2, '0');
}

/**
 * 生成默认任务名称：模型名称_时间
 */
function generateDefaultTaskName(modelName) {
  if (!modelName) return '';
  return `${modelName}_${formatDateTimeString()}`;
}

// 根据选择的模型初始化表单
const initializeWithModel = async (model) => {
  if (!model) return;
  
  console.log('初始化模型信息:', model);
  
  // 基础信息设置
  params.model_id = model.id;
  
  // 设置默认任务名称
  if (model.name) {
    params.task_name = generateDefaultTaskName(model.name);
    console.log('已设置默认任务名称:', params.task_name);
  } else {
    console.warn('模型名称为空，无法生成默认任务名称');
  }
  
  params.source_connection_id = model.source_connection_id || '';
  params.target_connection_id = model.target_connection_id || '';
  
  // 设置全量比对的初始抽样比例为100%
  if (params.compareMode === 'FULL') {
    params.sampleRate = 100;
  } else if (params.compareMode === 'SAMPLE') {
    params.sampleRate = 10;
  }
  
  // 加载模型详情
  await loadModelDetails(model.id);
};

// 加载模型详情
const loadModelDetails = async (modelId) => {
  if (!modelId) return;
   
  try {
    const response = await getCompareModel(modelId);
    
    if (response && response.data) {
      const model = response.data;
      console.log('成功获取模型数据，模型名称:', model.name, '模型ID:', model.id);
      
      // 设置模型ID
      params.model_id = model.id || modelId;
      
      // 设置数据库连接ID
      params.source_connection_id = model.source_connection || '';
      params.target_connection_id = model.target_connection || '';
      
      // 重要：设置默认任务名称
      if (model.name && typeof model.name === 'string' && model.name.trim() !== '') {
        const modelName = model.name.trim();
        params.task_name = generateDefaultTaskName(modelName);
      } else {
        params.task_name = `模型_${modelId}_${formatDateTimeString()}`;
      }
    } else {
      console.error('API返回格式不符合预期:', response);
      // 即使API出错，也设置一个默认任务名
      params.task_name = `模型_${modelId}_${formatDateTimeString()}`;
      console.log('API返回格式错误，使用备用名称:', params.task_name);
    }
  } catch (error) {
    console.error('加载模型详情出错', error);
    ElMessage.error('加载模型详情失败');
    
    // 即使发生错误，也设置一个默认任务名
    params.task_name = `模型_${modelId}_${formatDateTimeString()}`;
    console.log('API异常，使用备用名称:', params.task_name);
  }
};

// 验证表单
const validate = () => {
  return new Promise((resolve, reject) => {
    if (!formRef.value) {
      reject(new Error('表单引用不存在'));
      return;
    }
    
    formRef.value.validate((valid) => {
    if (valid) {
        // 准备提交的数据
        const submitData = { ...params };
        
        // 根据条件调整数据
        if (params.compareMode !== 'SAMPLE') {
          delete submitData.sampleRate;
        }
        
        // 设置线程数
        if (params.executionMode === 'FAST') {
          // 快速模式下，使用2倍CPU核心数
          submitData.threadCount = 'AUTO';
        } else {
          // 普通模式下，线程数固定为1
          submitData.threadCount = 1;
        }
        
        resolve(submitData);
      } else {
        reject(new Error('表单验证失败'));
      }
    });
  });
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  
    formRef.value.resetFields();
  
  // 获取当前模型名称（如果有）
  const currentModelName = props.model ? props.model.name : '未命名模型';
  
  // 重置为默认值
  Object.assign(params, {
    // 保留模型关联
    model_id: props.modelId || (props.model ? props.model.id : ''),
    task_name: generateDefaultTaskName(currentModelName),
    source_connection_id: '',
    target_connection_id: '',
    timeoutMinutes: 60,
    
    // 重置比对配置
    compareMode: 'FULL',
    sampleRate: 100,
    compareObjects: ['TABLE', 'DATA'],
    resultStorage: 'diff_only',
    
    // 重置执行选项
    executionMode: 'NORMAL',
    errorHandling: 'CONTINUE',
    
    // 重置高级选项
    ignoreCase: false,
    ignoreWhitespace: false,
    numericPrecision: 2,
    datePrecision: 'SECOND'
  });
  
  // 重新初始化模型相关信息
  if (props.model) {
    initializeWithModel(props.model);
  } else if (props.modelId) {
    loadModelDetails(props.modelId);
  }
};

// 监听模型变化
watch(
  () => props.model,
  (model) => {
    if (model) {
      initializeWithModel(model);
    }
  },
  { immediate: true }
);

// 监听比对模式变化，自动调整抽样比例
watch(
  () => params.compareMode,
  (newMode) => {
    if (newMode === 'FULL') {
      // 全量比对时，固定为100%
      params.sampleRate = 100;
    } else if (newMode === 'SAMPLE' && params.sampleRate === 100) {
      // 从全量切换到抽样时，如果是100%，调整为默认值
      params.sampleRate = 10;
    }
  },
  { immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  if (props.model) {
    initializeWithModel(props.model);
  } else if (props.modelId) {
    loadModelDetails(props.modelId);
  }
});

// 对外暴露方法
defineExpose({
  validate,
  resetForm,
  initializeWithModel
});
</script>

<style lang="scss" scoped>
.execute-params-container {
  padding: 10px;
  
  .divider-title {
    margin-left: 8px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .el-divider {
    margin: 20px 0;
  }
}
</style>