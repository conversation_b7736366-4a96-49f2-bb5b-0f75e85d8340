"""
ASGI config for arkdrf project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arkdrf.settings')

# 获取Django ASGI应用
django_asgi_app = get_asgi_application()

# 导入WebSocket URL配置
import apps.database.routing

# 配置ASGI应用，支持HTTP和WebSocket
# 暂时移除认证中间件，允许匿名WebSocket连接
application = ProtocolTypeRouter({
    # Django处理HTTP请求
    "http": django_asgi_app,

    # WebSocket处理 - 暂时允许匿名连接
    "websocket": AllowedHostsOriginValidator(
        URLRouter(
            apps.database.routing.websocket_urlpatterns
        )
    ),
})
