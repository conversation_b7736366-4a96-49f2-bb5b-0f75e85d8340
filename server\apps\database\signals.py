"""
信号处理模块

处理与数据库比对相关的Django信号。
"""
import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import CompareTask
from utils.message_queue import message_queue
from utils.websocket_manager import send_task_update

logger = logging.getLogger(__name__)

@receiver(post_save, sender=CompareTask)
def task_post_save(sender, instance, created, **kwargs):
    """
    任务保存后的处理
    
    Args:
        sender: 发送信号的模型类
        instance: 保存的实例
        created: 是否是新创建的实例
        **kwargs: 其他参数
    """
    try:
        # 发送任务状态更新
        send_task_update(instance)
        
        # 记录日志
        if created:
            logger.info(f"任务已创建: task_id={instance.id}, status={instance.status}")
        else:
            logger.info(f"任务已更新: task_id={instance.id}, status={instance.status}")
    except Exception as e:
        logger.error(f"处理任务保存信号时发生错误: {str(e)}")

@receiver(post_delete, sender=CompareTask)
def task_post_delete(sender, instance, **kwargs):
    """
    任务删除后的处理
    
    Args:
        sender: 发送信号的模型类
        instance: 删除的实例
        **kwargs: 其他参数
    """
    try:
        # 清理任务消息队列
        message_queue.clear_task_queue(str(instance.id))
        
        # 如果有external_id，也清理对应的队列
        if instance.external_id:
            message_queue.clear_task_queue(instance.external_id)
        
        # 记录日志
        logger.info(f"任务已删除: task_id={instance.id}")
    except Exception as e:
        logger.error(f"处理任务删除信号时发生错误: {str(e)}")
