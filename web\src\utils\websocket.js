/**
 * WebSocket工具类
 *
 * 提供WebSocket连接管理和消息处理功能
 */

// WebSocket连接映射表
const wsConnections = new Map();

/**
 * 创建并管理WebSocket连接
 *
 * @param {string} url WebSocket连接URL
 * @param {Object} options 配置选项
 * @param {Function} options.onMessage 消息处理回调
 * @param {Function} options.onOpen 连接打开回调
 * @param {Function} options.onClose 连接关闭回调
 * @param {Function} options.onError 错误处理回调
 * @param {boolean} options.reconnect 是否自动重连
 * @param {number} options.reconnectInterval 重连间隔(毫秒)
 * @param {number} options.maxReconnectAttempts 最大重连次数
 * @returns {Object} WebSocket连接控制对象
 */
export function createWebSocket(url, options = {}) {
  // 默认配置
  const defaultOptions = {
    onMessage: () => {},
    onOpen: () => {},
    onClose: () => {},
    onError: () => {},
    onReconnecting: () => {},
    onReconnectFailed: () => {},
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    debug: false
  };

  // 合并配置
  const config = { ...defaultOptions, ...options };

  // 调试日志函数
  const log = (message, ...args) => {
    if (config.debug) {
      console.log(`[WebSocket] ${message}`, ...args);
    }
  };

  // 错误日志函数
  const logError = (message, ...args) => {
    console.error(`[WebSocket] ${message}`, ...args);
  };

  // 如果已存在相同URL的连接，先关闭它
  if (wsConnections.has(url)) {
    const existingConn = wsConnections.get(url);
    existingConn.close();
  }

  // 创建连接状态对象
  const connectionState = {
    ws: null,
    url,
    isConnected: false,
    reconnectAttempts: 0,
    reconnectTimer: null,
    pingTimer: null,
    config
  };

  // 连接函数
  const connect = () => {
    try {
      // 创建WebSocket连接
      const ws = new WebSocket(url);

      // 保存WebSocket实例
      connectionState.ws = ws;

      // 连接打开处理
      ws.onopen = (event) => {
        log(`连接已打开: ${url}`);
        connectionState.isConnected = true;
        connectionState.reconnectAttempts = 0;

        // 启动心跳检测
        startHeartbeat();

        // 调用用户回调
        config.onOpen(event);
      };

      // 消息处理
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // 处理心跳响应
          if (data.type === 'pong') {
            log('收到心跳响应');
            return;
          }

          // 处理错误消息
          if (data.type === 'error') {
            logError('收到错误消息:', data);
            // 调用错误回调
            config.onError({
              message: data.message || '服务器返回错误',
              serverError: data,
              timestamp: new Date().toISOString()
            });
            return;
          }

          log('收到消息:', data);

          // 调用用户回调
          config.onMessage(data, event);
        } catch (error) {
          logError('消息解析错误:', error);

          // 尝试作为纯文本处理
          try {
            log('尝试作为纯文本处理:', event.data);
            config.onMessage({ type: 'text', content: event.data }, event);
          } catch (textError) {
            logError('纯文本处理失败:', textError);
          }
        }
      };

      // 连接关闭处理
      ws.onclose = (event) => {
        log(`连接已关闭: ${url}, 代码: ${event.code}, 原因: ${event.reason || '未知'}`);
        connectionState.isConnected = false;

        // 停止心跳检测
        stopHeartbeat();

        // 调用用户回调
        config.onClose(event);

        // 检查是否应该停止重连（新增检查）
        const shouldStopReconnect = config.shouldStopReconnect && config.shouldStopReconnect();

        // 如果需要重连且未达到最大重连次数且不应该停止重连
        if (config.reconnect && connectionState.reconnectAttempts < config.maxReconnectAttempts && !shouldStopReconnect) {
          // 使用指数退避策略计算重连间隔
          const reconnectDelay = Math.min(
            config.reconnectInterval * Math.pow(1.5, connectionState.reconnectAttempts),
            30000 // 最大30秒
          );

          log(`尝试重新连接 (${connectionState.reconnectAttempts + 1}/${config.maxReconnectAttempts})，延迟: ${reconnectDelay}ms...`);

          // 设置重连定时器
          connectionState.reconnectTimer = setTimeout(() => {
            // 在重连前再次检查是否应该停止
            if (config.shouldStopReconnect && config.shouldStopReconnect()) {
              log('重连前检查：任务已完成，停止重连');
              wsConnections.delete(url);
              return;
            }

            connectionState.reconnectAttempts++;

            // 在重连前通知用户
            if (config.onReconnecting) {
              config.onReconnecting(connectionState.reconnectAttempts, config.maxReconnectAttempts);
            }

            connect();
          }, reconnectDelay);
        } else {
          let reason = '';
          if (shouldStopReconnect) {
            reason = '任务已完成';
          } else if (connectionState.reconnectAttempts >= config.maxReconnectAttempts) {
            reason = `已达到最大重连次数 (${config.maxReconnectAttempts})`;
          } else {
            reason = '重连被禁用';
          }

          log(`停止重连: ${reason}`);

          // 通知用户重连失败（仅在达到最大重连次数时）
          if (connectionState.reconnectAttempts >= config.maxReconnectAttempts && config.onReconnectFailed) {
            config.onReconnectFailed();
          }

          // 从连接映射表中移除
          wsConnections.delete(url);
        }
      };

      // 错误处理
      ws.onerror = (error) => {
        logError(`连接错误: ${url}`, error);

        // 创建更友好的错误对象
        const friendlyError = {
          message: '连接服务器时发生错误',
          originalError: error,
          timestamp: new Date().toISOString(),
          url: url,
          reconnectAttempts: connectionState.reconnectAttempts
        };

        // 调用用户回调
        config.onError(friendlyError);

        // 如果连接已建立，则标记为断开
        if (connectionState.isConnected) {
          connectionState.isConnected = false;

          // 停止心跳检测
          stopHeartbeat();
        }
      };
    } catch (error) {
      logError(`创建WebSocket连接失败: ${url}`, error);

      // 通知用户连接创建失败
      config.onError({
        message: '创建WebSocket连接失败',
        originalError: error,
        timestamp: new Date().toISOString(),
        url: url,
        fatal: true
      });
    }
  };

  // 启动心跳检测
  const startHeartbeat = () => {
    // 清除现有的心跳定时器
    stopHeartbeat();

    // 设置新的心跳定时器
    connectionState.pingTimer = setInterval(() => {
      if (connectionState.isConnected && connectionState.ws && connectionState.ws.readyState === WebSocket.OPEN) {
        try {
          // 发送心跳消息
          log('发送心跳消息');
          connectionState.ws.send(JSON.stringify({
            type: 'ping',
            timestamp: Date.now()
          }));
        } catch (error) {
          logError('发送心跳消息失败:', error);

          // 如果发送心跳失败，可能连接已断开
          if (connectionState.isConnected) {
            connectionState.isConnected = false;

            // 尝试重新连接
            if (config.reconnect && connectionState.reconnectAttempts < config.maxReconnectAttempts) {
              log('心跳失败，尝试重新连接');
              connect();
            }
          }
        }
      }
    }, 30000); // 30秒发送一次心跳
  };

  // 停止心跳检测
  const stopHeartbeat = () => {
    if (connectionState.pingTimer) {
      clearInterval(connectionState.pingTimer);
      connectionState.pingTimer = null;
    }
  };

  // 关闭连接
  const close = () => {
    // 停止重连
    if (connectionState.reconnectTimer) {
      clearTimeout(connectionState.reconnectTimer);
      connectionState.reconnectTimer = null;
    }

    // 停止心跳
    stopHeartbeat();

    // 关闭WebSocket连接
    if (connectionState.ws) {
      try {
        // 移除所有事件处理器，防止触发重连
        connectionState.ws.onclose = null;
        connectionState.ws.onerror = null;
        connectionState.ws.onmessage = null;
        connectionState.ws.onopen = null;

        // 发送关闭消息
        if (connectionState.isConnected && connectionState.ws.readyState === WebSocket.OPEN) {
          try {
            connectionState.ws.send(JSON.stringify({
              type: 'close',
              timestamp: Date.now()
            }));
          } catch (error) {
            logError('发送关闭消息失败:', error);
          }
        }

        // 关闭连接
        connectionState.ws.close(1000, '客户端主动关闭');
        connectionState.ws = null;
        connectionState.isConnected = false;

        log(`连接已手动关闭: ${url}`);
      } catch (error) {
        logError(`关闭连接时发生错误: ${url}`, error);
      }
    }

    // 从连接映射表中移除
    wsConnections.delete(url);
  };

  // 发送消息
  const send = (data) => {
    if (connectionState.isConnected && connectionState.ws && connectionState.ws.readyState === WebSocket.OPEN) {
      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        connectionState.ws.send(message);
        log('消息已发送:', data);
        return true;
      } catch (error) {
        logError('发送消息失败:', error);

        // 如果发送消息失败，可能连接已断开
        if (connectionState.isConnected) {
          connectionState.isConnected = false;

          // 尝试重新连接
          if (config.reconnect && connectionState.reconnectAttempts < config.maxReconnectAttempts) {
            log('发送失败，尝试重新连接');
            connect();
          }
        }

        return false;
      }
    } else {
      log('无法发送消息，连接未建立或已关闭');
      return false;
    }
  };

  // 创建连接控制对象
  const controller = {
    connect,
    close,
    send,
    getState: () => ({
      url: connectionState.url,
      isConnected: connectionState.isConnected,
      reconnectAttempts: connectionState.reconnectAttempts,
      readyState: connectionState.ws ? connectionState.ws.readyState : -1,
      timestamp: new Date().toISOString()
    }),
    isConnected: () => connectionState.isConnected,
    getReadyState: () => connectionState.ws ? connectionState.ws.readyState : -1,
    getReconnectAttempts: () => connectionState.reconnectAttempts,
    resetReconnectAttempts: () => { connectionState.reconnectAttempts = 0; }
  };

  // 保存到连接映射表
  wsConnections.set(url, controller);

  // 立即连接
  connect();

  return controller;
}

/**
 * 获取WebSocket连接
 *
 * @param {string} url WebSocket连接URL
 * @returns {Object|null} WebSocket连接控制对象或null
 */
export function getWebSocket(url) {
  return wsConnections.get(url) || null;
}

/**
 * 关闭所有WebSocket连接
 */
export function closeAllWebSockets() {
  wsConnections.forEach((conn) => {
    conn.close();
  });
  wsConnections.clear();
}

/**
 * 构建WebSocket URL
 *
 * @param {string} path WebSocket路径
 * @returns {string} 完整的WebSocket URL
 */
export function buildWebSocketUrl(path) {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  return `${protocol}//${host}${path}`;
}

export default {
  createWebSocket,
  getWebSocket,
  closeAllWebSockets,
  buildWebSocketUrl
};
