"""
清理任务管理命令

提供手动清理任务的功能，包括清理不活跃的任务、过期的任务状态同步等。
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from utils.task_cleanup_service import task_cleanup_service
from utils.message_queue import message_queue
from utils.websocket_manager import websocket_manager


class Command(BaseCommand):
    help = '清理不活跃的任务和相关资源'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task-id',
            type=str,
            help='指定要清理的任务ID',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制清理，忽略连接状态检查',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要清理的任务，不实际执行清理',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='清理所有不活跃的任务',
        )

    def handle(self, *args, **options):
        """执行清理命令"""
        try:
            task_id = options.get('task_id')
            force = options.get('force', False)
            dry_run = options.get('dry_run', False)
            clean_all = options.get('all', False)

            if task_id:
                # 清理指定任务
                self._cleanup_specific_task(task_id, force, dry_run)
            elif clean_all:
                # 清理所有不活跃任务
                self._cleanup_all_tasks(force, dry_run)
            else:
                # 显示当前状态
                self._show_status()

        except Exception as e:
            raise CommandError(f'清理任务时发生错误: {str(e)}')

    def _cleanup_specific_task(self, task_id: str, force: bool, dry_run: bool):
        """清理指定任务"""
        self.stdout.write(f"检查任务 {task_id}...")

        # 检查任务是否存在
        if not message_queue.task_exists(task_id):
            self.stdout.write(
                self.style.WARNING(f"任务 {task_id} 不存在")
            )
            if not dry_run:
                message_queue.cleanup_task(task_id)
                self.stdout.write(
                    self.style.SUCCESS(f"已清理任务 {task_id} 的相关资源")
                )
            return

        # 检查连接状态
        has_connections = message_queue.has_active_connections(task_id)
        if has_connections and not force:
            self.stdout.write(
                self.style.WARNING(f"任务 {task_id} 仍有活跃连接，使用 --force 强制清理")
            )
            return

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"[DRY RUN] 将清理任务 {task_id}")
            )
        else:
            message_queue.cleanup_task(task_id)
            self.stdout.write(
                self.style.SUCCESS(f"已清理任务 {task_id}")
            )

    def _cleanup_all_tasks(self, force: bool, dry_run: bool):
        """清理所有不活跃任务"""
        self.stdout.write("检查所有任务...")

        # 获取所有正在同步的任务
        with message_queue.lock:
            sync_tasks = list(message_queue.last_sync_time.keys())
            queue_tasks = list(message_queue.queues.keys())
            all_tasks = set(sync_tasks + queue_tasks)

        if not all_tasks:
            self.stdout.write("没有找到需要清理的任务")
            return

        cleaned_count = 0
        for task_id in all_tasks:
            try:
                # 检查任务是否存在
                if not message_queue.task_exists(task_id):
                    if dry_run:
                        self.stdout.write(
                            self.style.WARNING(f"[DRY RUN] 将清理不存在的任务 {task_id}")
                        )
                    else:
                        message_queue.cleanup_task(task_id)
                        self.stdout.write(f"清理不存在的任务: {task_id}")
                    cleaned_count += 1
                    continue

                # 检查连接状态
                has_connections = message_queue.has_active_connections(task_id)
                if has_connections and not force:
                    self.stdout.write(f"跳过有活跃连接的任务: {task_id}")
                    continue

                if dry_run:
                    self.stdout.write(
                        self.style.WARNING(f"[DRY RUN] 将清理不活跃的任务 {task_id}")
                    )
                else:
                    message_queue.cleanup_task(task_id)
                    self.stdout.write(f"清理不活跃的任务: {task_id}")
                cleaned_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"清理任务 {task_id} 时发生错误: {str(e)}")
                )

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"[DRY RUN] 将清理 {cleaned_count} 个任务")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"已清理 {cleaned_count} 个任务")
            )

    def _show_status(self):
        """显示当前状态"""
        self.stdout.write("=== 任务状态概览 ===")

        # 消息队列状态
        queue_stats = message_queue.get_queue_stats()
        self.stdout.write(f"消息队列: {queue_stats['total_queues']} 个任务队列")
        self.stdout.write(f"待处理消息: {queue_stats['total_messages']} 条")

        # WebSocket连接状态
        connection_stats = websocket_manager.get_connection_stats()
        self.stdout.write(f"WebSocket连接: {connection_stats['connection_count']} 个")
        self.stdout.write(f"任务连接: {connection_stats['task_connection_count']} 个")

        # 清理服务状态
        cleanup_stats = task_cleanup_service.get_stats()
        self.stdout.write(f"清理服务运行状态: {'运行中' if cleanup_stats['running'] else '已停止'}")
        self.stdout.write(f"清理次数: {cleanup_stats['cleanup_count']}")
        if cleanup_stats['last_cleanup_time']:
            self.stdout.write(f"最后清理时间: {cleanup_stats['last_cleanup_time']}")

        # 详细任务列表
        with message_queue.lock:
            sync_tasks = list(message_queue.last_sync_time.keys())
            queue_tasks = list(message_queue.queues.keys())
            all_tasks = set(sync_tasks + queue_tasks)

        if all_tasks:
            self.stdout.write("\n=== 任务详情 ===")
            for task_id in sorted(all_tasks):
                exists = message_queue.task_exists(task_id)
                has_connections = message_queue.has_active_connections(task_id)
                
                status = []
                if not exists:
                    status.append("不存在")
                if not has_connections:
                    status.append("无连接")
                if not status:
                    status.append("正常")
                
                self.stdout.write(f"  {task_id}: {', '.join(status)}")

        self.stdout.write("\n使用 --help 查看清理选项")
