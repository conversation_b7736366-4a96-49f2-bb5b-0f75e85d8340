"""
消息处理模块

提供消息处理函数，用于处理消息队列中的消息。
"""
import logging
import json
from typing import Dict, Any, Optional
from django.utils import timezone
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from .message_queue import message_queue
from .db_utils import get_db_agent_client

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()

def handle_task_status(task_id: str, message: Dict[str, Any]) -> bool:
    """
    处理任务状态消息

    Args:
        task_id: 任务ID
        message: 消息内容

    Returns:
        bool: 是否成功处理
    """
    try:
        # 构建任务组名称
        task_group_name = f'task_{task_id}'

        # 发送消息到任务组
        async_to_sync(channel_layer.group_send)(
            task_group_name,
            {
                'type': 'task_status',
                'content': message
            }
        )

        logger.debug(f"已处理任务状态消息: task_id={task_id}, message_type={message.get('type')}")
        return True
    except Exception as e:
        logger.error(f"处理任务状态消息失败: {str(e)}")
        return False

def handle_notification(task_id: str, message: Dict[str, Any]) -> bool:
    """
    处理通知消息

    Args:
        task_id: 任务ID（对于全局通知，使用"global"）
        message: 消息内容

    Returns:
        bool: 是否成功处理
    """
    try:
        # 发送消息到通知组
        async_to_sync(channel_layer.group_send)(
            'task_notifications',
            {
                'type': 'task_notification',
                'content': message
            }
        )

        logger.debug(f"已处理通知消息: task_id={task_id}, message_type={message.get('type')}")
        return True
    except Exception as e:
        logger.error(f"处理通知消息失败: {str(e)}")
        return False

def handle_full_sync(task_id: str) -> bool:
    """
    处理全量同步

    Args:
        task_id: 任务ID

    Returns:
        bool: 是否成功处理
    """
    try:
        logger.info(f"开始执行任务 {task_id} 的全量同步")

        # 检查任务ID是否有效
        if task_id == "global":
            logger.warning(f"全量同步: 无效的任务ID {task_id}")
            return False

        # 导入任务模型
        from apps.database.models import CompareTask
        from apps.database.serializers import CompareTaskSerializer

        # 查找任务
        try:
            # 先尝试通过ID查找
            task = CompareTask.objects.get(id=task_id)
        except (CompareTask.DoesNotExist, ValueError):
            # 再尝试通过external_id查找
            task = CompareTask.objects.filter(external_id=task_id).first()
            if not task:
                logger.warning(f"全量同步: 未找到任务 {task_id}，清理相关资源")
                # 清理消息队列中的任务
                from utils.message_queue import message_queue
                message_queue.cleanup_task(task_id)
                return False

        # 如果任务有external_id，从DB-Agent获取最新状态
        if task.external_id:
            try:
                # 获取DB-Agent客户端
                db_agent_client = get_db_agent_client()

                # 获取任务状态
                result = db_agent_client.get_task_status(task.external_id)

                # 检查是否成功
                if result.get('success', False):
                    # 获取状态数据
                    status_data = result.get('data', {})

                    # 更新任务状态
                    task.status = status_data.get('status', task.status)
                    task.progress = status_data.get('progress', task.progress)
                    task.message = status_data.get('message', task.message)

                    # 更新统计数据
                    stats = status_data.get('stats', {})
                    if stats:
                        task.total_records = stats.get('source_count', task.total_records)
                        task.processed_records = stats.get('processed_count', task.processed_records)
                        task.matched_records = stats.get('match_count', task.matched_records)
                        task.different_records = stats.get('diff_count', task.different_records)

                    # 如果任务已完成，设置结束时间
                    if task.status in ['completed', 'success', 'failed', 'error', 'stopped', 'canceled']:
                        if task.start_time and not task.end_time:
                            task.end_time = timezone.now()

                    # 保存任务
                    task.save()

                    logger.info(f"全量同步: 已更新任务 {task_id} 的状态")
                else:
                    # 检查是否是404错误（任务不存在）
                    error_message = result.get('message', '未知错误')
                    if '不存在' in error_message or 'NotFound' in str(result.get('detail', {})):
                        logger.warning(f"全量同步: DB-Agent中任务 {task.external_id} 不存在，停止同步")
                        # 清理消息队列中的任务
                        from utils.message_queue import message_queue
                        message_queue.cleanup_task(task_id)
                        return False
                    else:
                        logger.warning(f"全量同步: 获取任务状态失败: {error_message}")
            except Exception as e:
                logger.error(f"全量同步: 从DB-Agent获取任务状态时发生错误: {str(e)}")
                # 如果是连接错误，不清理任务，可能是临时网络问题
                return False

        # 序列化任务数据
        serializer = CompareTaskSerializer(task)
        task_data = serializer.data

        # 发送到特定任务的组
        task_group_name = f'task_{task.id}'
        async_to_sync(channel_layer.group_send)(
            task_group_name,
            {
                'type': 'task_status',
                'content': {
                    'type': 'task_status',
                    'task': task_data
                }
            }
        )

        logger.info(f"全量同步: 已完成任务 {task_id} 的全量同步")
        return True
    except Exception as e:
        logger.error(f"全量同步: 处理任务 {task_id} 的全量同步时发生错误: {str(e)}")
        return False

# 注册消息处理函数
def register_message_handlers():
    """注册消息处理函数"""
    message_queue.register_handler("task_status", handle_task_status)
    message_queue.register_handler("notification", handle_notification)
    message_queue.register_handler("full_sync", handle_full_sync)
    logger.info("已注册消息处理函数")

# 启动消息队列工作线程
def start_message_queue():
    """启动消息队列工作线程"""
    register_message_handlers()
    message_queue.start_worker()

# 停止消息队列工作线程
def stop_message_queue():
    """停止消息队列工作线程"""
    message_queue.stop_worker()
    logger.info("消息队列工作线程已停止")
