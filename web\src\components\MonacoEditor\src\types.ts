import * as Monaco from 'monaco-editor';

export interface MonacoEditorProps {
  /**
   * 编辑器的内容
   */
  value?: string;
  /**
   * 编辑器的v-model值
   */
  modelValue?: string;
  /**
   * 编辑器的语言
   */
  language?: string;
  /**
   * 编辑器的主题
   */
  theme?: string;
  /**
   * 编辑器的高度
   */
  height?: string;
  /**
   * 编辑器的选项
   */
  options?: Monaco.editor.IStandaloneEditorConstructionOptions;
  /**
   * 是否只读
   */
  readOnly?: boolean;
  /**
   * 是否自动聚焦
   */
  autoFocus?: boolean;
  /**
   * 是否显示minimap
   */
  minimap?: boolean;
  /**
   * 编辑器的ID
   */
  id?: string;
  /**
   * 是否使用SQL语法
   */
  sqlMode?: boolean;
}

export interface MonacoEditorExpose {
  formatCode: () => void;
  editor: () => Monaco.editor.IStandaloneCodeEditor | null;
}

export interface MonacoEditorEmits {
  /**
   * v-model更新事件
   */
  (e: 'update:modelValue', value: string): void;
  /**
   * 内容变化事件
   */
  (e: 'change', value: string): void;
  /**
   * 编辑器挂载事件
   */
  (e: 'editor-mounted', editor: { 
    editor: Monaco.editor.IStandaloneCodeEditor, 
    monaco: typeof Monaco 
  }): void;
}

// 添加差异编辑器接口定义
export interface MonacoDiffEditorProps {
  /**
   * 原始内容
   */
  original?: string;
  /**
   * 修改后内容
   */
  modified?: string;
  /**
   * 编辑器的语言
   */
  language?: string;
  /**
   * 编辑器的主题
   */
  theme?: string;
  /**
   * 编辑器的高度
   */
  height?: string;
  /**
   * 编辑器的选项
   */
  options?: Monaco.editor.IDiffEditorConstructionOptions;
  /**
   * 是否只读
   */
  readOnly?: boolean;
  /**
   * 编辑器的ID
   */
  id?: string;
  /**
   * 是否显示差异导航工具
   */
  showDiffNavigator?: boolean;
  /**
   * 是否显示差异统计信息
   */
  showDiffStats?: boolean;
  /**
   * 是否使用内联差异视图(true)还是并排视图(false)
   */
  inlineDiff?: boolean;
  /**
   * 是否启用代码折叠
   */
  codeFolding?: boolean;
  /**
   * 性能模式: 'standard' | 'quickDiff' | 'advanced'
   * - standard: 标准模式，适用于中小型文件
   * - quickDiff: 快速模式，适用于大文件，性能优先
   * - advanced: 高级模式，字符级别的精确差异，适用于需要精确比对的场景
   */
  performanceMode?: 'standard' | 'quickDiff' | 'advanced';
}

/**
 * 差异编辑器组件暴露的方法和属性
 */
export interface MonacoDiffEditorExpose {
  /**
   * 更新编辑器内容
   * @param newOriginal 新的原始内容，如果不提供则使用props中的值
   * @param newModified 新的修改后内容，如果不提供则使用props中的值
   */
  updateContent: (newOriginal?: string, newModified?: string) => void;
  
  /**
   * 获取编辑器实例
   */
  editor: any;
}

export interface MonacoDiffEditorEmits {
  /**
   * 修改后内容更新事件
   */
  (e: 'update:modified', value: string): void;
  /**
   * 内容变化事件
   */
  (e: 'change', value: string): void;
  /**
   * 编辑器挂载事件
   */
  (e: 'editor-mounted', editor: {
    editor: Monaco.editor.IStandaloneDiffEditor,
    monaco: typeof Monaco
  }): void;
  /**
   * 差异统计信息事件
   */
  (e: 'diff-stats', stats: { 
    added: number; 
    removed: number; 
    changed: number; 
    total: number 
  }): void;
  /**
   * 导航到差异事件
   */
  (e: 'navigate-diff', position: { 
    lineNumber: number; 
    column: number 
  }): void;
} 