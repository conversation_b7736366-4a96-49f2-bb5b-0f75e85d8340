"""
WebSocket路由配置

定义WebSocket连接的URL路由，将WebSocket请求映射到相应的消费者(Consumer)。
"""
from django.urls import re_path
from . import consumers

# WebSocket URL模式
websocket_urlpatterns = [
    # 任务状态WebSocket
    re_path(r'ws/database/task/(?P<task_id>[^/]+)/$', consumers.TaskStatusConsumer.as_asgi()),
    
    # 全局任务通知WebSocket
    re_path(r'ws/database/tasks/notifications/$', consumers.TaskNotificationConsumer.as_asgi()),
]
