/**
 * 数据库连接管理API
 *
 * 提供数据库连接的创建、测试、管理、统计等功能
 * 重构版本 - 统一使用DatabaseApiClient和标准化端点
 */

import { databaseApiClient, withDatabaseErrorHandling } from './client';
import { CONNECTION_ENDPOINTS } from './endpoints';
import type { BaseApiResponse } from './types';
import type { DataConnection } from "@/types/database";
import {
  DatabaseErrorCode,
  createDatabaseError
} from './error';

/**
 * 获取数据库连接列表
 * @param params - 查询参数
 */
export const getConnectionList = withDatabaseErrorHandling(
  async (params?: {
    page?: number;
    size?: number;
    keyword?: string;
  }): Promise<BaseApiResponse<DataConnection[]>> => {
    return databaseApiClient.get<DataConnection[]>(CONNECTION_ENDPOINTS.LIST, {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 获取连接详情
 * @param id - 连接ID
 */
export const getConnection = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<DataConnection>> => {
    if (!id) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接ID不能为空'
      );
    }

    return databaseApiClient.get<DataConnection>(CONNECTION_ENDPOINTS.DETAIL(id), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 删除数据库连接
 * @param id - 连接ID
 */
export const deleteConnection = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<null>> => {
    if (!id) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接ID不能为空'
      );
    }

    const response = await databaseApiClient.delete<null>(CONNECTION_ENDPOINTS.DETAIL(id), {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除连接相关缓存
    databaseApiClient.clearCache('connection');

    return response;
  }
);

/**
 * 测试连接
 * @param data - 连接数据
 */
export const testConnection = withDatabaseErrorHandling(
  async (data: Partial<DataConnection>): Promise<BaseApiResponse<{
    connected: boolean;
    message?: string;
    responseTime?: number;
  }>> => {
    if (!data.host || !data.port) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '主机和端口为必填项'
      );
    }

    return databaseApiClient.post(`${CONNECTION_ENDPOINTS.BASE}test/`, data, {
      showErrorMessage: true,
      timeout: 10000 // 连接测试超时10秒
    });
  }
);

/**
 * 测试指定连接
 * @param id - 连接ID
 */
export const testConnectionById = withDatabaseErrorHandling(
  async (id: string): Promise<BaseApiResponse<{
    connected: boolean;
    message?: string;
    responseTime?: number;
  }>> => {
    if (!id) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接ID不能为空'
      );
    }

    return databaseApiClient.post(CONNECTION_ENDPOINTS.TEST(id), {}, {
      showErrorMessage: true,
      timeout: 10000 // 连接测试超时10秒
    });
  }
);

/**
 * 连接调试
 */
export const getConnectionDebug = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<{
    database_connections: number;
    sample_data: any;
    api_url: string;
    timestamp: string;
  }>> => {
    return databaseApiClient.get(CONNECTION_ENDPOINTS.DEBUG);
  }
);

/**
 * 创建连接
 * @param data - 连接数据
 */
export const createConnection = withDatabaseErrorHandling(
  async (data: DataConnection): Promise<BaseApiResponse<DataConnection>> => {
    if (!data.name || !data.host || !data.port) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接名称、主机和端口为必填项'
      );
    }

    const response = await databaseApiClient.post<DataConnection>(CONNECTION_ENDPOINTS.LIST, data, {
      showSuccessMessage: true,
      showErrorMessage: true
    });

    // 清除连接列表缓存
    databaseApiClient.clearCache('connection');

    return response;
  }
);

/**
 * 更新连接
 * @param id - 连接ID
 * @param data - 连接数据
 */
export const updateConnection = withDatabaseErrorHandling(
  async (id: string, data: DataConnection): Promise<BaseApiResponse<DataConnection>> => {
    if (!id) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接ID不能为空'
      );
    }

    if (!data.name || !data.host || !data.port) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接名称、主机和端口为必填项'
      );
    }

    const response = await databaseApiClient.put<DataConnection>(
      CONNECTION_ENDPOINTS.DETAIL(id),
      data,
      {
        showSuccessMessage: true,
        showErrorMessage: true
      }
    );

    // 清除连接相关缓存
    databaseApiClient.clearCache('connection');

    return response;
  }
);

/**
 * 获取连接统计信息
 * 整合连接和数据源的统计信息
 */
export const getConnectionStatistics = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<{
    total: number;
    byType: Record<string, number>;
    connected: number;
    failed: number;
    recentActivity: Array<{
      date: string;
      connections: number;
      tests: number;
    }>;
  }>> => {
    return databaseApiClient.get(`${CONNECTION_ENDPOINTS.BASE}/stats/`, {
      useCache: true,
      cacheTTL: 120000 // 2分钟缓存
    });
  }
);

/**
 * 批量测试连接
 * 批量测试多个连接的状态
 */
export const batchTestConnections = withDatabaseErrorHandling(
  async (connectionIds: string[]): Promise<BaseApiResponse<Array<{
    id: string;
    connected: boolean;
    message?: string;
    responseTime?: number;
  }>>> => {
    if (!connectionIds || connectionIds.length === 0) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '连接ID列表不能为空'
      );
    }

    if (connectionIds.length > 50) {
      throw createDatabaseError(
        DatabaseErrorCode.DATA_VALIDATION_ERROR,
        '批量测试连接数量不能超过50个'
      );
    }

    return databaseApiClient.post(CONNECTION_ENDPOINTS.BATCH_TEST, { connectionIds }, {
      showErrorMessage: true,
      timeout: 30000 // 批量测试超时30秒
    });
  }
);

/**
 * 获取连接健康状态
 * 新增功能：获取所有连接的健康状态概览
 */
export const getConnectionHealth = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<{
    healthy: number;
    unhealthy: number;
    unknown: number;
    details: Array<{
      id: string;
      name: string;
      status: 'healthy' | 'unhealthy' | 'unknown';
      lastChecked: string;
      responseTime?: number;
    }>;
  }>> => {
    return databaseApiClient.get(`${CONNECTION_ENDPOINTS.BASE}/health/`, {
      useCache: true,
      cacheTTL: 30000 // 30秒缓存
    });
  }
);