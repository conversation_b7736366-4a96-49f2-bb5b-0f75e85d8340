# Generated by Django 5.1 on 2025-05-31 01:16

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('database', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='comparetask',
            name='execution_time',
            field=models.FloatField(default=0.0, verbose_name='执行时间(秒)'),
        ),
        migrations.AddField(
            model_name='comparetask',
            name='source_only_records',
            field=models.IntegerField(default=0, verbose_name='源独有记录数'),
        ),
        migrations.AddField(
            model_name='comparetask',
            name='target_only_records',
            field=models.IntegerField(default=0, verbose_name='目标独有记录数'),
        ),
        migrations.AlterField(
            model_name='comparediff',
            name='source_value',
            field=models.JSONField(blank=True, null=True, verbose_name='源值'),
        ),
        migrations.AlterField(
            model_name='comparediff',
            name='target_value',
            field=models.JSONField(blank=True, null=True, verbose_name='目标值'),
        ),
        migrations.CreateModel(
            name='SourceOnlyRecord',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('table_name', models.CharField(max_length=100, verbose_name='表名')),
                ('record_id', models.CharField(max_length=100, verbose_name='记录ID')),
                ('data', models.JSONField(verbose_name='记录数据')),
                ('reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='原因')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_only_data', to='database.comparetask', verbose_name='比对任务')),
            ],
            options={
                'verbose_name': '源独有记录',
                'verbose_name_plural': '源独有记录',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='TargetOnlyRecord',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('table_name', models.CharField(max_length=100, verbose_name='表名')),
                ('record_id', models.CharField(max_length=100, verbose_name='记录ID')),
                ('data', models.JSONField(verbose_name='记录数据')),
                ('reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='原因')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_only_data', to='database.comparetask', verbose_name='比对任务')),
            ],
            options={
                'verbose_name': '目标独有记录',
                'verbose_name_plural': '目标独有记录',
                'ordering': ['-create_time'],
            },
        ),
    ]
