/**
 * SQL示例和格式化工具函数
 */

/**
 * 获取表配置的示例SQL
 * @param {number} tableIndex - 表的索引
 * @param {string} sqlType - SQL类型 (sql_1 或 sql_2)
 * @returns {string} 示例SQL语句
 */
export function getExampleSQL(tableIndex, sqlType) {
  // 基础查询示例
  const basicSelect = `SELECT 
  t.id,
  t.name, 
  t.description,
  t.created_at,
  t.updated_at
FROM 
  table_${tableIndex + 1} t
WHERE 
  t.status = 'active'
ORDER BY 
  t.created_at DESC
LIMIT 10;`;

  // JOIN查询示例
  const joinSelect = `SELECT 
  t1.id, 
  t1.name,
  t1.created_at,
  t2.description,
  t3.category_name
FROM 
  table_${tableIndex + 1} t1
LEFT JOIN 
  relation_table t2 ON t1.id = t2.table_id
LEFT JOIN
  category t3 ON t1.category_id = t3.id
WHERE 
  t1.status = 'active'
  AND t1.created_at > '2023-01-01'
GROUP BY
  t1.id
HAVING
  COUNT(t2.id) > 0
ORDER BY 
  t1.created_at DESC
LIMIT 10;`;

  // 根据SQL类型和表索引生成不同的示例
  if (sqlType === 'sql_2') {
    // 对第二个SQL编辑器，提供略有不同的查询示例
    return joinSelect;
  }
  
  return basicSelect;
}

/**
 * 格式化SQL语句
 * @param {string} sql - 待格式化的SQL
 * @returns {string} 格式化后的SQL
 */
export function formatSQL(sql) {
  if (!sql) return '';
  
  // 简单的SQL格式化逻辑
  let formatted = sql
    // 替换多个空格为单个空格
    .replace(/\s+/g, ' ')
    // 在关键字后添加换行和缩进
    .replace(/\b(SELECT|FROM|WHERE|GROUP BY|HAVING|ORDER BY|LIMIT|LEFT JOIN|RIGHT JOIN|INNER JOIN|JOIN|ON|AND|OR)\b/gi, '\n$1')
    // 分割为行
    .split('\n')
    // 处理每行的缩进
    .map(line => {
      line = line.trim();
      // 为特定关键字添加缩进
      if (/^(FROM|WHERE|GROUP BY|HAVING|ORDER BY|LIMIT)/.test(line)) {
        return line;
      } else if (/^(LEFT JOIN|RIGHT JOIN|INNER JOIN|JOIN|ON|AND|OR)/.test(line)) {
        return '  ' + line;
      } else if (/^SELECT/.test(line)) {
        return line;
      } else if (line && !/^(SELECT|FROM)/.test(line)) {
        return '  ' + line;
      }
      return line;
    })
    .join('\n');
  
  return formatted;
}

/**
 * 计算两个SQL语句之间的差异
 * @param {string} sql1 - 第一个SQL
 * @param {string} sql2 - 第二个SQL
 * @returns {Object} 差异信息
 */
export function calculateDiff(sql1, sql2) {
  if (!sql1 && !sql2) {
    return {
      matchCount: 0,
      diffCount: 0,
      missingCount: 0
    };
  }
  
  const lines1 = (sql1 || '').split('\n');
  const lines2 = (sql2 || '').split('\n');
  
  let matchCount = 0;
  let diffCount = 0;
  let missingCount = 0;
  
  // 计算匹配、差异和缺失的行数
  const maxLines = Math.max(lines1.length, lines2.length);
  
  for (let i = 0; i < maxLines; i++) {
    const line1 = i < lines1.length ? lines1[i].trim() : null;
    const line2 = i < lines2.length ? lines2[i].trim() : null;
    
    if (line1 === null || line2 === null) {
      missingCount++;
    } else if (line1 === line2) {
      matchCount++;
    } else {
      diffCount++;
    }
  }
  
  return {
    matchCount,
    diffCount,
    missingCount
  };
} 