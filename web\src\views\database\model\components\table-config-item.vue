<template>
  <el-collapse-item
    :name="collapseName"
    class="table-config-item"
  >
    <template #title>
      <div class="item-header">
        <div class="item-info">
          <span class="config-index">配置{{ index + 1 }}：</span>
          <el-input 
            v-model="localTable.remark" 
            placeholder="表配置备注" 
            class="remark-input"
            @change="updateTable"
          ></el-input>
        </div>
        <div class="item-actions">
          <el-popconfirm
            title="确定要删除此表配置吗？"
            @confirm="handleRemove"
          >
            <template #reference>
              <el-button
                type="danger"
                size="small"
                circle
                plain
                :icon="Delete"
              ></el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>
    </template>
    
    <div class="item-content">
      <div class="sql-config">
        <!-- SQL配置区域1 -->
        <div class="sql-section">
          <div class="sql-header">
            <span class="sql-title">源SQL(1)</span>
            <div class="sql-actions">
              <span 
                class="text-link"
                @click="handleFormatSql(1)"
              >
                格式化SQL
              </span>
              <span 
                class="text-link"
                @click="handleFillExample(1)"
              >
                插入示例SQL
              </span>
            </div>
          </div>
          
          <div class="editor-container">
            <monaco-editor
              v-model="localTable.sql_1"
              language="sql"
              :theme="monacoTheme"
              :height="'320px'"
              :options="{
                minimap: { enabled: false },
                fontSize: 14,
                tabSize: 2,
                automaticLayout: true,
                scrollBeyondLastLine: false
              }"
              @change="handleSqlUpdate($event, 1)"
              ref="sqlEditor1Ref"
            />
          </div>
        </div>
        
        <!-- SQL配置区域2 -->
        <div class="sql-section">
          <div class="sql-header">
            <span class="sql-title">目标SQL(2)</span>
            <div class="sql-actions">
              <span 
                class="text-link"
                @click="handleFormatSql(2)"
              >
                格式化SQL
              </span>
              <span 
                class="text-link"
                @click="handleFillExample(2)"
              >
                插入示例SQL
              </span>
            </div>
          </div>
          
          <div class="editor-container">
            <monaco-editor
              v-model="localTable.sql_2"
              language="sql"
              :theme="monacoTheme"
              :height="'320px'"
              :options="{
                minimap: { enabled: false },
                fontSize: 14,
                tabSize: 2,
                automaticLayout: true,
                scrollBeyondLastLine: false
              }"
              @change="handleSqlUpdate($event, 2)"
              ref="sqlEditor2Ref"
            />
          </div>
        </div>
      </div>
      
      <!-- 差异对比控制区 -->
      <div class="diff-controls">
        <div class="diff-toggle">
          <el-switch
            v-model="localTable.diffEnabled"
            inline-prompt
            active-text="启用差异对比"
            inactive-text="关闭差异对比"
            @change="handleToggleDiff"
          />
        </div>
        
        <div v-if="localTable.diffEnabled" class="diff-info">
          <div class="diff-stats">
            <el-tag type="success" effect="light">匹配: {{ localTable.diffInfo?.matchCount || 0 }}</el-tag>
            <el-tag type="danger" effect="light">缺失: {{ localTable.diffInfo?.missingCount || 0 }}</el-tag>
            <el-tag type="warning" effect="light">差异: {{ localTable.diffInfo?.diffCount || 0 }}</el-tag>
          </div>
          
          <el-button 
            size="small" 
            type="primary" 
            plain 
            @click="refreshDiff"
          >
            刷新差异
          </el-button>
        </div>
      </div>
      
      <!-- 差异对比视图 -->
      <div v-if="localTable.diffEnabled" class="diff-view">
        <monaco-diff-editor
          :original="localTable.sql_1"
          :modified="localTable.sql_2"
          language="sql"
          :theme="monacoTheme"
          height="320px"
          :options="{
            readOnly: true,
            renderSideBySide: true,
            minimap: { enabled: false },
            fontSize: 14,
            automaticLayout: true,
            scrollBeyondLastLine: false
          }"
          @editor-mounted="handleDiffEditorMounted"
        />
      </div>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { MonacoEditor, MonacoDiffEditor } from '@/components/MonacoEditor/index';
import { formatSQL, getExampleSQL, calculateDiff } from '@/utils/sql-utils';
import { getCurrentTheme } from '@/utils/monaco-theme';

// 定义表格配置接口
interface TableConfig {
  id: string;
  name: string;
  sql_1: string;
  sql_2: string;
  remark?: string;
  diffEnabled?: boolean;
  diffInfo?: {
    matchCount: number;
    missingCount: number;
    diffCount: number;
  };
}

interface DiffInfo {
  matchCount: number;
  missingCount: number;
  diffCount: number;
}

const props = defineProps<{
  table: TableConfig;
  index: number;
  collapseName: string;
  theme?: string;
}>();

const emit = defineEmits<{
  (e: 'update:table', value: TableConfig): void;
  (e: 'remove', index: number): void;
}>();

// 本地状态
const localTable = ref<TableConfig>({
  id: '',
  name: '',
  sql_1: '',
  sql_2: '',
  remark: '',
  diffEnabled: false,
  diffInfo: undefined
});

// Monaco编辑器引用
const sqlEditor1Ref = ref(null);
const sqlEditor2Ref = ref(null);

// 编辑器主题
const monacoTheme = computed(() => props.theme || getCurrentTheme());

// 监听表配置变化
watch(() => props.table, (newValue) => {
  localTable.value = JSON.parse(JSON.stringify(newValue));
}, { deep: true, immediate: true });

// 更新父组件中的表配置
const updateTable = () => {
  emit('update:table', localTable.value);
};

// 处理SQL内容更新
const handleSqlUpdate = (value: string, sqlNum: number) => {
  if (sqlNum === 1) {
    localTable.value.sql_1 = value;
  } else if (sqlNum === 2) {
    localTable.value.sql_2 = value;
  }
  
  // 如果差异对比启用，更新差异信息
  if (localTable.value.diffEnabled) {
    refreshDiff();
  }
  
  updateTable();
};

// 差异编辑器挂载完成
const handleDiffEditorMounted = (event: any) => {
  // 确保编辑器挂载后刷新一次布局
  if (event && event.editor) {
    setTimeout(() => {
      event.editor.layout();
    }, 100);
  }
};

// 格式化SQL
const handleFormatSql = (sqlNum: number) => {
  let editorInstance = null;
  
  // 获取对应编辑器的Monaco实例
  if (sqlNum === 1 && sqlEditor1Ref.value) {
    editorInstance = sqlEditor1Ref.value.editor();
  } else if (sqlNum === 2 && sqlEditor2Ref.value) {
    editorInstance = sqlEditor2Ref.value.editor();
  }
  
  if (editorInstance) {
    // 使用Monaco Editor内置的格式化功能
    editorInstance.getAction('editor.action.formatDocument').run().then(() => {
      // 格式化成功后更新表格配置
      if (sqlNum === 1) {
        localTable.value.sql_1 = editorInstance.getValue();
      } else if (sqlNum === 2) {
        localTable.value.sql_2 = editorInstance.getValue();
      }
      updateTable();
      ElMessage.success('SQL格式化成功');
    }).catch(error => {
      console.error('SQL格式化错误:', error);
      ElMessage.error('SQL格式化失败');
      
      // 内置格式化失败时尝试使用自定义格式化
      try {
        const sql = sqlNum === 1 ? localTable.value.sql_1 : localTable.value.sql_2;
        const formatted = formatSQL(sql);
        if (sqlNum === 1) {
          localTable.value.sql_1 = formatted;
        } else if (sqlNum === 2) {
          localTable.value.sql_2 = formatted;
        }
        updateTable();
        ElMessage.success('SQL格式化成功(备用方法)');
      } catch (backupError) {
        console.error('备用SQL格式化错误:', backupError);
      }
    });
  } else {
    // 找不到编辑器实例时，使用自定义格式化逻辑
    try {
      const sql = sqlNum === 1 ? localTable.value.sql_1 : localTable.value.sql_2;
      if (!sql) {
        ElMessage.warning('请先输入SQL后再格式化');
        return;
      }
      
      const formatted = formatSQL(sql);
      if (sqlNum === 1) {
        localTable.value.sql_1 = formatted;
      } else if (sqlNum === 2) {
        localTable.value.sql_2 = formatted;
      }
      updateTable();
      ElMessage.success('SQL格式化成功');
    } catch (error) {
      console.error('SQL格式化错误:', error);
      ElMessage.error('SQL格式化失败');
    }
  }
};

// 填充示例SQL
const handleFillExample = (sqlNum: number) => {
  try {
    // 获取示例SQL
    const exampleSQL = getExampleSQL(props.index, sqlNum);
    
    // 确保示例SQL不为空
    if (!exampleSQL) {
      ElMessage.warning('获取示例SQL失败');
      return;
    }
    
    // 更新本地数据
    if (sqlNum === 1) {
      localTable.value.sql_1 = exampleSQL;
    } else if (sqlNum === 2) {
      localTable.value.sql_2 = exampleSQL;
    }
    
    // 确保更新到父组件
    updateTable();
    
    // 强制DOM更新后再通知用户
    nextTick(() => {
      // 这里通过强制引用变更来刷新视图
      if (sqlNum === 1) {
        localTable.value = { ...localTable.value, sql_1: exampleSQL };
      } else {
        localTable.value = { ...localTable.value, sql_2: exampleSQL };
      }
      
      // 再次更新父组件
      updateTable();
      
      // 显示成功消息
      ElMessage.success('已填充示例SQL');
    });
  } catch (error) {
    console.error('填充示例SQL时发生错误:', error);
    ElMessage.error('填充示例SQL失败');
  }
};

// 切换差异对比
const handleToggleDiff = () => {
  if (localTable.value.diffEnabled) {
    refreshDiff();
  } else {
    localTable.value.diffInfo = undefined;
  }
  
  updateTable();
};

// 刷新差异分析
const refreshDiff = () => {
  if (localTable.value.diffEnabled) {
    const sql1 = localTable.value.sql_1 || '';
    const sql2 = localTable.value.sql_2 || '';
    
    try {
      localTable.value.diffInfo = calculateDiff(sql1, sql2);
      updateTable();
    } catch (error) {
      console.error('差异计算错误:', error);
      ElMessage.error('差异分析失败');
    }
  }
};

// 删除表配置
const handleRemove = () => {
  emit('remove', props.index);
};

// 组件挂载时
onMounted(() => {
  // 初始化表配置
  if (!localTable.value.sql_1) localTable.value.sql_1 = '';
  if (!localTable.value.sql_2) localTable.value.sql_2 = '';
  if (localTable.value.diffEnabled && !localTable.value.diffInfo) {
    refreshDiff();
  }
});
</script>

<style lang="scss" scoped>
.table-config-item {
  width: 100%;
  
  :deep(.el-collapse-item__content) {
    padding: 0;
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 16px;
  
  .item-info {
    display: flex;
    align-items: center;
    flex: 1;
    
    .config-index {
      font-weight: 600;
      margin-right: 5px;
      white-space: nowrap;
    }
    
    .remark-input {
      width: 320px;
    }
  }
  
  .item-actions {
    display: flex;
    gap: 8px;
  }
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sql-config {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
  
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.sql-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  // background-color: var(--el-fill-color-light);
  border: 0px solid var(--el-border-color-light);
  border-radius: 0px;
  overflow: hidden;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  //background-color: var(--el-fill-color-light);
  border-bottom: 0px solid var(--el-border-color-light);
  
  .sql-title {
    font-weight: 600;
    font-size: 14px;
  }
  
  .sql-actions {
    display: flex;
    gap: 15px;
  }
}

.text-link {
  font-size: 14px;
  color: var(--el-color-primary);
  cursor: pointer;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.editor-container {
  border: none;
  overflow: hidden;
  height: 320px;
  
  :deep(.monaco-editor) {
    border: none !important;
  }
}

.diff-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--el-fill-color-light);
  border: 0px solid var(--el-border-color-light);
  border-radius: 0px;
  margin-bottom: 0px;
}

.diff-toggle {
  margin-right: 10px;
}

.diff-info {
  display: flex;
  align-items: center;
  gap: 15px;
  
  .diff-stats {
    display: flex;
    gap: 8px;
  }
}

.diff-view {
  margin-top: 0;
  overflow: hidden;
  height: 320px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 0px;
  
  :deep(.monaco-editor-container) {
    height: 100% !important;
    border: none !important;
  }
  
  :deep(.monaco-editor) {
    height: 100%;
    border: none !important;
  }
  
  :deep(.monaco-diff-editor) {
    height: 100%;
    border: none !important;
  }
}
</style> 