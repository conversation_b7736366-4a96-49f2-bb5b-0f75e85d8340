<template>
  <div class="maincontent">
    <!-- 页面标题部分 -->
    <div class="page-header">
      <el-page-header :icon="null" @back="goBack">
        <template #content>
          <span class="page-title-text">数据比对执行</span>
          <span class="page-description">选择比对模型，配置参数，执行比对任务</span>
        </template>
      </el-page-header>
    </div>

    <!-- 主要内容区域 -->
    <div class="execute-container" :style="{ height: `${contentHeight}px` }">
      <el-card shadow="never" class="config-card">
        <el-steps :active="activeStep" finish-status="success" simple class="config-steps">
          <el-step title="选择模型" />
          <el-step title="模型预览" />
          <el-step title="执行参数" />
        </el-steps>

        <div class="step-content">
          <!-- 步骤1: 选择数据比对模型 -->
          <div v-if="activeStep === 0" class="step-pane">
            <model-selector
              ref="modelSelectorRef"
              @select-model="handleModelSelect"
            />
          </div>

          <!-- 步骤2: 模型预览 -->
          <div v-if="activeStep === 1" class="step-pane">
            <model-preview
              v-if="selectedModel"
              :model="selectedModel"
            />
            <el-empty v-else description="请先选择比对模型" />
          </div>

          <!-- 步骤3: 执行参数 -->
          <div v-if="activeStep === 2" class="step-pane">
            <execute-params
              ref="paramsRef"
              :model-id="selectedModel?.id"
            />
          </div>
        </div>

        <div class="step-actions">
          <el-button
            v-if="activeStep > 0"
            @click="prevStep"
          >
            上一步
          </el-button>
          <el-button
            v-if="activeStep < 2"
            type="primary"
            @click="nextStep"
          >
            下一步
          </el-button>
          <el-button
            v-if="activeStep === 2"
            type="success"
            :loading="submitting"
            @click="submitForm"
          >
            开始比对
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 任务进度对话框 -->
    <el-dialog
      v-model="progressVisible"
      title="任务执行进度"
      width="750px"
      :close-on-click-modal="false"
      :show-close="false"
      class="progress-dialog"
    >
      <div class="progress-info">
        <div class="progress-status">
          <el-steps :active="progressStep" finish-status="success" simple>
            <el-step title="准备" />
            <el-step title="执行中" />
            <el-step title="完成" />
          </el-steps>
        </div>

        <el-card shadow="never" class="task-detail">
          <div class="task-header">
            <span class="task-name">{{ selectedModel?.name || '比对任务' }}</span>
            <el-tag :type="getStatusType(taskStatus)">{{ getStatusText(taskStatus) }}</el-tag>
          </div>

          <div class="task-time">
            <div class="time-item">
              <span class="time-label">开始时间:</span>
              <span class="time-value">{{ taskStartTime ? formatTime(taskStartTime) : '-' }}</span>
            </div>
            <div class="time-item">
              <span class="time-label">已用时间:</span>
              <span class="time-value">{{ formatDuration(taskDuration) }}</span>
            </div>
          </div>
        </el-card>

        <div class="progress-detail">
          <el-progress
            :percentage="taskProgress"
            :status="taskProgress === 100 ? 'success' : ''"
            :stroke-width="18"
            :show-text="false"
          />
          <div class="progress-text">
            {{ progressInfo }}
          </div>
        </div>

        <!-- 任务错误详情组件 -->
        <task-error-details
          v-if="taskStatus.toLowerCase() === 'failed' && currentTask?.result?.error"
          :error="currentTask.result.error"
          :task-id="currentTask.id"
          @retry="retryTask"
          @close="closeProgressDialog"
        />

        <el-card shadow="never" class="log-output">
          <template #header>
            <div class="log-header">
              <span>执行日志</span>
            </div>
          </template>
          <div class="log-content" ref="logContentRef">
            <div v-for="(log, index) in taskLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message" :class="{'log-error': log.type === 'error'}">{{ log.message }}</span>
            </div>
            <div v-if="taskLogs.length === 0" class="empty-log">
              等待日志输出...
            </div>
          </div>
        </el-card>

        <el-card shadow="never" class="progress-stats">
          <!-- 总体统计 -->
          <div class="stats-header">
            <h4>总体统计</h4>
            <el-button
              v-if="tableProgress.length > 1"
              size="small"
              type="primary"
              text
              @click="showDetailedStats = !showDetailedStats"
            >
              {{ showDetailedStats ? '隐藏详情' : '显示详情' }}
            </el-button>
          </div>

          <div class="stat-grid">
            <div class="stat-item">
              <div class="stat-label">总对象数</div>
              <div class="stat-value">{{ formatNumber(taskStats.totalObjects || 0) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">处理数</div>
              <div class="stat-value">{{ formatNumber(taskStats.processedObjects || 0) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">差异数</div>
              <div class="stat-value">{{ formatNumber(taskStats.diffCount || 0) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">一致率</div>
              <div class="stat-value">{{ calculateMatchRate() }}%</div>
            </div>
          </div>

          <!-- 多表详细统计 -->
          <div v-if="showDetailedStats && tableProgress.length > 1" class="detailed-stats">
            <el-divider />
            <h4>分表统计</h4>
            <div class="table-stats-grid">
              <div
                v-for="table in getTableStatistics()"
                :key="table.tableName"
                class="table-stat-card"
              >
                <div class="table-name">{{ table.tableName }}</div>
                <div class="table-stats">
                  <div class="mini-stat">
                    <span class="label">总数:</span>
                    <span class="value">{{ formatNumber(table.totalRecords) }}</span>
                  </div>
                  <div class="mini-stat">
                    <span class="label">差异:</span>
                    <span class="value">{{ formatNumber(table.diffCount) }}</span>
                  </div>
                  <div class="mini-stat">
                    <span class="label">进度:</span>
                    <span class="value">{{ table.progress }}%</span>
                  </div>
                </div>
                <el-progress
                  :percentage="table.progress"
                  :status="table.progress === 100 ? 'success' : ''"
                  :stroke-width="4"
                />
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!['completed', 'success', 'failed', 'error', 'stopped', 'cancelled'].includes(taskStatus.toLowerCase())"
            type="danger"
            @click="cancelExecution"
          >
            取消执行
          </el-button>
          <el-button
            v-if="['completed', 'success', 'failed', 'error', 'stopped', 'cancelled'].includes(taskStatus.toLowerCase())"
            @click="progressVisible = false"
          >
            关闭
          </el-button>
          <el-button
            v-if="['completed', 'success'].includes(taskStatus.toLowerCase())"
            type="primary"
            @click="viewResults(currentTask?.id)"
          >
            查看结果
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 确认对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认取消"
      width="400px"
    >
      <span>确定要取消当前执行的任务吗？这将无法恢复。</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmCancel">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { RefreshRight } from '@element-plus/icons-vue'
import ModelSelector from './components/model-selector.vue'
import ModelPreview from './components/model-preview.vue'
import ExecuteParams from './components/execute-params.vue'
import TaskErrorDetails from './components/taskerror-details.vue'
import {
  launchCompareTask,
  getCompareTaskStatus,
  cancelCompareTask,
  controlCompareTask,
  exportCompareTaskResult,
  stopTaskPolling
} from '@/api/database/compare'
import {
  createTaskStatusWebSocket,
  closeTaskStatusWebSocket
} from '@/api/database/websocket'
import { formatTime, formatDuration } from '@/utils/timeFormat'
import type { FormInstance } from 'element-plus'
import type { CompareTask, TaskDetails, StartTaskParams } from '@/types/database.d'

const router = useRouter()
const modelSelectorRef = ref(null)
const paramsRef = ref(null)
const logContentRef = ref(null)
const activeStep = ref(0)
const contentHeight = ref(700)
const submitting = ref(false)
const progressVisible = ref(false)
const confirmDialogVisible = ref(false)
const currentTaskId = ref('')
const taskPollingTimeout = ref(null)
const taskStartTime = ref(null)
const selectedModel = ref(null)
const lastMessage = ref('')
const prevStatus = ref('')
const pollingFailCount = ref(0)
const MAX_POLLING_FAILS = 3

// 任务进度相关状态
const taskStatus = ref('')
const taskProgress = ref(0)
const progressStep = ref(1)
const taskDuration = ref(0)
const taskLogs = ref([])
const taskStats = reactive({
  totalObjects: 0,
  processedObjects: 0,
  diffCount: 0,
  matchedCount: 0,
  sourceOnlyCount: 0,
  targetOnlyCount: 0
})

// 详细统计显示控制
const showDetailedStats = ref(false)

// 进度信息计算属性
const progressInfo = computed(() => {
  switch (taskStatus.value.toLowerCase()) {
    case 'queued':
      return '任务排队中，请等待...'
    case 'preparing':
      return '正在准备资源...'
    case 'running':
      return `正在执行比对，已完成 ${taskProgress.value.toFixed(0)}%`
    case 'completed':
    case 'success':
      return '任务已完成，可以查看结果'
    case 'failed':
      return '任务执行失败，请检查配置和日志'
    case 'cancelled':
    case 'stopped':
      return '任务已取消'
    default:
      return '等待任务启动...'
  }
})

// 计算一致率
const calculateMatchRate = () => {
  const total = taskStats.totalObjects
  if (total === 0) return '0.00'

  const matched = taskStats.matchedCount || (taskStats.totalObjects - taskStats.diffCount)
  const rate = (matched / total) * 100
  return rate.toFixed(2)
}

// 格式化数字显示
const formatNumber = (num) => {
  if (num === 0) return '0'
  if (num < 1000) return num.toString()
  if (num < 1000000) return (num / 1000).toFixed(1) + 'K'
  return (num / 1000000).toFixed(1) + 'M'
}

// 获取表统计信息
const getTableStatistics = () => {
  if (!tableProgress.value || tableProgress.value.length === 0) {
    return []
  }

  return tableProgress.value
    .filter(table => table.tableName !== '总计') // 排除总计行
    .map(table => ({
      tableName: table.tableName,
      totalRecords: (table as any).totalRecords || (table as any).total || 0,
      diffCount: (table as any).diffCount || (table as any).different || 0,
      matchedCount: (table as any).matchedCount || (table as any).matched || 0,
      progress: Math.round(table.progress || 0),
      status: (table as any).status || 'unknown'
    }))
}

// 步骤控制
const nextStep = async () => {
  if (activeStep.value === 0) {
    if (!selectedModel.value) {
      ElMessage.warning('请先选择比对模型')
      return
    }
  } else if (activeStep.value === 1) {
    // 从预览到参数，初始化参数组件
    if (paramsRef.value) {
      paramsRef.value.initializeWithModel(selectedModel.value)
    }
  }

  activeStep.value++
}

const prevStep = () => {
  activeStep.value--
}

// 选择模型处理
const handleModelSelect = (model) => {
  selectedModel.value = model
  // 可以在这里预加载模型详情
}

// 表单数据
const form = ref<StartTaskParams>({
  model: '',
  name: '',
  limit: 10000,
  options: {}
})

// 加载状态
const loading = ref(false)

// 当前任务
const currentTask = ref<CompareTask | null>(null)
const tableProgress = ref<TaskDetails['tableProgress']>([])
const remainingTime = ref(0)
const currentSpeed = ref(0)

// WebSocket连接和状态管理
let taskWebSocket = null
const connectionState = reactive({
  isWebSocketConnected: false,
  isPollingActive: false,
  lastStopPollingRequest: null, // 记录最后一次stop_polling请求的任务ID
  cleanupInProgress: false // 标记是否正在清理资源
})

// 开始WebSocket连接
const startWebSocketConnection = (taskId) => {
  // 防止重复连接
  if (connectionState.isWebSocketConnected && taskWebSocket) {
    console.log(`WebSocket已连接，跳过重复连接请求: ${taskId}`)
    return
  }

  // 先关闭现有连接
  closeWebSocketConnection()

  console.log(`开始建立WebSocket连接: ${taskId}`)

  // 创建新的WebSocket连接，添加任务完成检查
  taskWebSocket = createTaskStatusWebSocket(taskId, {
    onTaskUpdate: (task) => {
      // 更新任务状态
      updateTaskStatus(task)
    },
    onTaskCompletion: (task) => {
      // 处理任务完成通知
      console.log(`收到任务完成通知: ${taskId}`, task)

      // 确保任务状态正确映射
      let finalStatus = task.status
      if (task.status === 'completed') {
        finalStatus = 'success'
        console.log('任务完成通知：将completed状态映射为success')
      }

      // 更新任务状态
      updateTaskStatus({ ...task, status: finalStatus })

      // 停止轮询（如果正在进行）
      stopPolling()

      // 添加完成日志
      taskLogs.value.push({
        time: formatTime(new Date()),
        message: `任务已完成: ${finalStatus}`,
        type: finalStatus === 'success' ? 'success' : 'error'
      })

      // 显示完成通知
      if (finalStatus === 'success') {
        ElNotification({
          title: '任务完成',
          message: `数据比对任务已成功完成！`,
          type: 'success',
          duration: 5000
        })
      }
    },
    shouldStopReconnect: () => {
      // 检查任务是否已完成，如果已完成则停止重连
      const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
      const isTaskCompleted = currentTask.value?.status && terminalStates.includes(currentTask.value.status.toLowerCase())
      const isProgressComplete = taskProgress.value >= 100
      const shouldStop = isTaskCompleted || isProgressComplete || connectionState.cleanupInProgress

      if (shouldStop) {
        console.log(`停止WebSocket重连: 任务已完成(${currentTask.value?.status}), 进度${taskProgress.value}%, 清理中${connectionState.cleanupInProgress}`)
      }

      return shouldStop
    },
    onConnected: () => {
      // WebSocket连接成功
      connectionState.isWebSocketConnected = true
      console.log(`WebSocket连接成功: ${taskId}`)
      taskLogs.value.push({
        time: formatTime(new Date()),
        message: 'WebSocket连接已建立，开始接收实时更新',
        type: 'info'
      })
    },
    onDisconnected: () => {
      // WebSocket连接断开
      connectionState.isWebSocketConnected = false
      console.log(`WebSocket连接断开: ${taskId}`)

      // 增强的任务完成检测
      const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
      const isTaskCompleted = currentTask.value?.status && terminalStates.includes(currentTask.value.status.toLowerCase())
      const isProgressComplete = taskProgress.value >= 100

      // 额外检查：如果进度100%，也认为任务完成
      const shouldStopReconnection = isTaskCompleted || isProgressComplete || connectionState.cleanupInProgress

      if (currentTask.value?.id && !shouldStopReconnection) {
        console.log('WebSocket连接断开，切换到轮询模式')
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: 'WebSocket连接断开，切换到轮询模式',
          type: 'warning'
        })
        startPolling()
      } else {
        // 记录停止重连的原因
        let reason = 'unknown'
        if (isTaskCompleted) reason = '任务已完成'
        else if (isProgressComplete) reason = '进度已达100%'
        else if (connectionState.cleanupInProgress) reason = '正在清理资源'

        console.log(`WebSocket连接断开，停止重连: ${reason}`)
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: `WebSocket连接断开，${reason}，停止重连`,
          type: 'info'
        })

        // 确保清理所有连接和定时器
        cleanupTaskResources()
      }
    },
    onError: (error) => {
      // WebSocket错误
      connectionState.isWebSocketConnected = false
      console.error(`WebSocket错误: ${taskId}`, error)

      // 检查任务是否已完成，如果已完成则不记录错误和启动轮询
      const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
      const isTaskCompleted = currentTask.value?.status && terminalStates.includes(currentTask.value.status.toLowerCase())
      const isProgressComplete = taskProgress.value >= 100

      if (!isTaskCompleted && !isProgressComplete && !connectionState.cleanupInProgress) {
        // 只有在任务未完成时才记录WebSocket错误和启动轮询
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: `WebSocket连接错误: ${error.message || '连接服务器时发生错误'}`,
          type: 'error'
        })

        console.log('WebSocket错误，切换到轮询模式')
        startPolling()
      } else {
        // 任务已完成，WebSocket错误是正常的（服务器已清理连接）
        console.log(`任务已完成，忽略WebSocket连接错误: ${error.message || '连接已关闭'}`)

        // 确保清理所有连接和定时器
        cleanupTaskResources()
      }
    }
  })
}

// 关闭WebSocket连接
const closeWebSocketConnection = () => {
  if (taskWebSocket && connectionState.isWebSocketConnected) {
    console.log(`关闭WebSocket连接: ${currentTask.value?.id}`)
    try {
      if (currentTask.value?.id) {
        closeTaskStatusWebSocket(currentTask.value.id)
      }
    } catch (error) {
      console.error('关闭WebSocket连接失败:', error)
    }
  }

  // 重置连接状态
  connectionState.isWebSocketConnected = false
  taskWebSocket = null
}

// 更新任务状态
const updateTaskStatus = (task) => {
  if (!task) return

  // 保存之前的状态以便检测变化
  const oldStatus = currentTask.value?.status || ''
  const oldProgress = taskProgress.value || 0

  // 状态映射：将Agent的completed状态映射为前端的success状态
  let mappedStatus = task.status
  if (task.status === 'completed') {
    mappedStatus = 'success'
    console.log('将Agent状态completed映射为前端状态success')
  }

  // 添加状态更新日志，便于调试
  console.log(`任务状态更新: ${oldStatus} -> ${mappedStatus}, 进度: ${task.progress || 0}%`)

  // 更新当前任务
  currentTask.value = { ...task, status: mappedStatus }
  taskStatus.value = mappedStatus
  taskProgress.value = task.progress || 0

  // 更新进度信息
  if (task.details) {
    const details = task.details as any
    tableProgress.value = details.tableProgress || []
    remainingTime.value = details.remainingTime || 0
    currentSpeed.value = details.currentSpeed || 0

    // 更新统计信息 - 修复字段映射问题
    // 支持多种可能的字段名，确保数据正确显示
    taskStats.totalObjects = details.totalRecords || details.total_records ||
                             details.source_count || details.sourceCount || 0
    taskStats.processedObjects = details.processed || details.processed_records ||
                                details.processedRecords || details.matched_count ||
                                details.matchedCount || 0
    taskStats.diffCount = details.different || details.different_records ||
                         details.differentRecords || details.diff_count ||
                         details.diffCount || 0

    // 更新额外的统计字段
    taskStats.matchedCount = details.matched_count || details.matchedCount ||
                            details.matched || (taskStats.totalObjects - taskStats.diffCount) || 0
    taskStats.sourceOnlyCount = details.source_only_count || details.sourceOnlyCount ||
                               details.sourceOnly || 0
    taskStats.targetOnlyCount = details.target_only_count || details.targetOnlyCount ||
                               details.targetOnly || 0

    // 检查详细进度是否显示任务已完成
    if (details.tableProgress && details.tableProgress.length > 0) {
      const totalProgress = details.tableProgress.find(p => p.tableName === '总计')
      if (totalProgress && totalProgress.progress >= 100 && mappedStatus === 'running') {
        // 任务实际已完成，但状态还是running，需要手动更新
        console.log('检测到任务实际已完成，但状态仍为running，更新状态为success')
        taskStatus.value = 'success'
        currentTask.value.status = 'success'
        mappedStatus = 'success'
        taskProgress.value = 100

        // 添加完成日志
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: '任务执行完成！',
          type: 'success'
        })
      }
    }
  }

  // 如果任务已完成或异常终止，清理资源
  const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
  const taskStatusLower = mappedStatus.toLowerCase()

  if (terminalStates.includes(taskStatusLower) && !connectionState.cleanupInProgress) {
    console.log(`任务已${taskStatusLower === 'success' || taskStatusLower === 'completed' ? '完成' : '终止'}，清理资源`)
    // 清理任务资源（只在未清理时执行）
    cleanupTaskResources()

    // 如果是完成状态，确保进度为100%
    if (taskStatusLower === 'completed' || taskStatusLower === 'success') {
      taskProgress.value = 100

      // 如果任务完成但进度不是100%，添加一条日志
      if (task.progress < 100) {
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: '任务已完成，进度已更新为100%',
          type: 'info'
        })
      }

      // 添加任务完成日志
      if (!taskLogs.value.some(log => log.message.includes('任务执行完成'))) {
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: '任务执行完成！',
          type: 'success'
        })
      }
    }

    // 如果是失败状态，检查是否有错误信息
    if (taskStatusLower === 'error' || taskStatusLower === 'failed') {
      // 确保进度为100%，表示任务已结束
      taskProgress.value = 100

      // 检查多个可能的错误信息来源
      let errorInfo = null
      let errorMessage = '未知错误'
      let errorType = 'UnknownError'
      let errorSuggestion = ''

      // 1. 首先检查任务状态响应中的error字段（新增）
      if (task.error) {
        errorInfo = task.error
        errorMessage = errorInfo.message || errorMessage
        errorType = errorInfo.type || errorType
        errorSuggestion = errorInfo.suggestion || ''
      }
      // 2. 然后检查result中的error字段（兼容旧版本）
      else if (task.result && task.result.error) {
        errorInfo = task.result.error
        errorMessage = errorInfo.message || errorMessage
        errorType = errorInfo.type || errorType
        errorSuggestion = errorInfo.suggestion || ''
      }
      // 3. 最后使用任务消息作为错误信息
      else if (task.message && task.message !== '任务执行失败') {
        errorMessage = task.message
        errorType = 'TaskExecutionError'
      }

      // 添加错误日志
      taskLogs.value.push({
        time: formatTime(new Date()),
        message: `错误类型: ${errorType}`,
        type: 'error'
      })

      taskLogs.value.push({
        time: formatTime(new Date()),
        message: `错误信息: ${errorMessage}`,
        type: 'error'
      })

      // 如果有解决建议，也添加到日志中
      if (errorSuggestion) {
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: `解决建议: ${errorSuggestion}`,
          type: 'warning'
        })
      }

      // 如果有原始错误消息（用于调试），也记录到日志
      if (errorInfo && errorInfo.raw_message && errorInfo.raw_message !== errorMessage) {
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: `详细信息: ${errorInfo.raw_message}`,
          type: 'info'
        })
      }

      // 显示错误通知 - 使用更友好的格式
      const notificationMessage = errorSuggestion
        ? `${errorMessage}\n\n💡 解决建议: ${errorSuggestion}`
        : errorMessage

      ElNotification({
        title: `任务执行失败 (${errorType})`,
        message: notificationMessage,
        type: 'error',
        duration: 0,
        dangerouslyUseHTMLString: false
      })
    }
  }

  // 更新日志
  if (task.message && lastMessage.value !== task.message) {
    lastMessage.value = task.message
    taskLogs.value.push({
      time: formatTime(new Date()),
      message: task.message,
      type: taskStatusLower === 'error' || taskStatusLower === 'failed' ? 'error' : 'info'
    })

    // 滚动日志到底部
    scrollLogsToBottom()
  }

  // 更新进度步骤
  updateProgressStep(mappedStatus)

  // 计算任务持续时间
  if (taskStartTime.value) {
    taskDuration.value = Date.now() - taskStartTime.value
  }

  // 状态变化通知
  if (oldStatus && oldStatus !== mappedStatus) {
    notifyStatusChange(oldStatus, mappedStatus)
  }

  // 进度变化日志（仅记录显著变化）
  if (oldProgress !== taskProgress.value && Math.abs(oldProgress - taskProgress.value) >= 10) {
    taskLogs.value.push({
      time: formatTime(new Date()),
      message: `任务进度: ${taskProgress.value}%`,
      type: 'info'
    })

    // 滚动日志到底部
    scrollLogsToBottom()
  }
}

// 轮询定时器
let pollingTimer: number | null = null

// 开始轮询
const startPolling = () => {
  // 防止重复启动轮询
  if (connectionState.isPollingActive) {
    console.log('轮询已在进行中，跳过重复启动')
    return
  }

  // 检查任务是否已完成
  const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
  if (currentTask.value?.status && terminalStates.includes(currentTask.value.status.toLowerCase())) {
    console.log(`任务已完成(${currentTask.value.status})，不启动轮询`)
    return
  }

  // 如果正在清理资源，不启动轮询
  if (connectionState.cleanupInProgress) {
    console.log('正在清理资源，不启动轮询')
    return
  }

  console.log(`开始轮询任务状态: ${currentTask.value?.id}`)

  // 清除现有的轮询
  stopPolling()

  // 设置轮询状态
  connectionState.isPollingActive = true

  // 重置轮询失败计数
  pollingFailCount.value = 0

  // 立即执行第一次状态查询
  getTaskStatus()

  // 设置定时器持续轮询
  const scheduleNextPoll = () => {
    if (connectionState.isPollingActive) {
      taskPollingTimeout.value = setTimeout(() => {
        getTaskStatus().finally(() => {
          // 无论成功失败，都安排下一次轮询（如果轮询仍然活跃）
          scheduleNextPoll()
        })
      }, 3000) // 3秒轮询一次
    }
  }

  // 开始轮询循环
  scheduleNextPoll()
}

// 停止轮询
const stopPolling = () => {
  // 清除定时器
  if (taskPollingTimeout.value) {
    clearTimeout(taskPollingTimeout.value)
    taskPollingTimeout.value = null
  }

  // 重置轮询状态
  connectionState.isPollingActive = false

  // 如果任务ID存在且未发送过停止请求，通知服务器停止轮询
  if (currentTask.value?.id && connectionState.lastStopPollingRequest !== currentTask.value.id) {
    try {
      // 记录日志
      console.log(`停止轮询任务: ${currentTask.value.id}, 状态: ${taskStatus.value}`)

      // 记录已发送停止请求，避免重复发送
      connectionState.lastStopPollingRequest = currentTask.value.id

      // 发送停止轮询请求
      stopTaskPolling(currentTask.value.id).catch(err => {
        console.error('发送停止轮询请求失败', err)
        // 如果请求失败，重置标记以允许重试
        if (connectionState.lastStopPollingRequest === currentTask.value.id) {
          connectionState.lastStopPollingRequest = null
        }
      })
    } catch (error) {
      console.error('停止轮询通知失败', error)
      // 如果出现异常，重置标记
      connectionState.lastStopPollingRequest = null
    }
  } else if (connectionState.lastStopPollingRequest === currentTask.value?.id) {
    console.log(`已发送过停止轮询请求，跳过重复请求: ${currentTask.value.id}`)
  }
}

// 表单引用
const formRef = ref<FormInstance>();

// 提交表单
const submitForm = async () => {
  if (!paramsRef.value) {
    console.error("参数表单引用不存在");
    ElMessage.error("表单引用不存在，无法提交");
    return;
  }

  try {
    console.log("开始执行提交表单函数...");
    submitting.value = true;

    // 验证参数表单
    const formData = await paramsRef.value.validate().catch(error => {
      console.error("表单验证失败:", error);
      ElMessage.error("表单验证失败: " + (error.message || String(error)));
      throw error;
    });

    console.log("表单验证通过，准备提交数据:", formData);

    if (!formData.model_id) {
      console.error("缺少模型ID");
      ElMessage.error("缺少模型ID，无法创建任务");
      return;
    }

    // 构建请求参数
    const params = {
      model: formData.model_id,
      name: formData.task_name,
      limit: formData.limit || 10000,
      options: {
        compareMode: formData.compareMode,
        sampleRate: formData.sampleRate,
        compareObjects: formData.compareObjects,
        resultStorage: formData.resultStorage,
        executionMode: formData.executionMode,
        errorHandling: formData.errorHandling,
        threadCount: formData.threadCount,
        ignoreCase: formData.ignoreCase,
        ignoreWhitespace: formData.ignoreWhitespace,
        numericPrecision: formData.numericPrecision,
        datePrecision: formData.datePrecision,
        timeoutMinutes: formData.timeoutMinutes
      }
    };

    console.log("准备发送API请求，参数:", params);

    // 使用一步式接口直接启动任务
    console.log("开始创建并执行任务...");
    const res = await launchCompareTask(params);

    console.log("任务创建结果:", res);

    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res.success) {
      console.log("任务成功创建并启动");
      ElMessage.success('任务已启动');

      // 重置连接状态（新任务开始）
      connectionState.isWebSocketConnected = false
      connectionState.isPollingActive = false
      connectionState.lastStopPollingRequest = null
      connectionState.cleanupInProgress = false

      currentTask.value = res.data;
      taskStatus.value = res.data.status;
      progressVisible.value = true; // 显示进度对话框
      taskProgress.value = 0;
      taskStartTime.value = Date.now();
      taskLogs.value = [{
        time: formatTime(new Date()),
        message: '任务已提交，正在执行...',
        type: 'info'
      }];

      // 开始WebSocket连接
      console.log("开始WebSocket连接...");
      startWebSocketConnection(res.data.id);

      // 如果WebSocket不可用，使用轮询作为备用
      setTimeout(() => {
        if (!taskWebSocket || !taskWebSocket.isConnected()) {
          console.log("WebSocket连接失败，使用轮询作为备用...");
          startPolling();
        }
      }, 2000);
    } else {
      console.error("创建任务失败:", res);
      ElMessage.error(res.message || '创建任务失败');
    }
  } catch (error) {
    console.error('提交任务失败:', error);
    const errorMsg = error.response?.data?.message || error.message || '提交任务失败';
    ElMessage.error(errorMsg);
  } finally {
    submitting.value = false;
  }
};

// 获取任务状态
const getTaskStatus = async () => {
  if (!currentTask.value?.id) return

  // 注意：不在这里检查本地状态，因为本地状态可能不准确
  // 总是查询服务器获取最新状态，确保不会错过状态变化

  try {
    const res = await getCompareTaskStatus(currentTask.value.id)
    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res.success) {
      // 重置轮询失败计数
      pollingFailCount.value = 0

      // 更新任务状态
      updateTaskStatus(res.data)

      // 检查更新后的状态是否完成（包括通过详细进度检测到的完成状态）
      const currentStatus = taskStatus.value.toLowerCase()
      const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
      if (terminalStates.includes(currentStatus)) {
        console.log(`轮询检测到任务完成(${currentStatus})，停止轮询`)
        stopPolling()

        // 添加完成日志（如果还没有添加的话）
        const lastLog = taskLogs.value[taskLogs.value.length - 1]
        if (!lastLog || !lastLog.message.includes('任务执行完成')) {
          taskLogs.value.push({
            time: formatTime(new Date()),
            message: `任务已完成: ${currentStatus}`,
            type: currentStatus === 'success' ? 'success' : 'error'
          })
        }

        // 发送任务完成通知
        notifyStatusChange('running', currentStatus)
      }

      // 额外检查：如果详细进度显示100%但状态仍为running，也停止轮询
      else if (res.data.details && res.data.details.tableProgress) {
        const totalProgress = res.data.details.tableProgress.find(p => p.tableName === '总计')
        if (totalProgress && totalProgress.progress >= 100) {
          console.log('详细进度显示100%，任务实际已完成，停止轮询')
          stopPolling()
        }
      }
    } else {
      // 轮询失败计数增加
      pollingFailCount.value++
      console.warn(`获取任务状态失败: ${res.message}, 失败次数: ${pollingFailCount.value}/${MAX_POLLING_FAILS}`)

      // 如果连续失败次数超过阈值，停止轮询
      if (pollingFailCount.value >= MAX_POLLING_FAILS) {
        console.error(`连续${MAX_POLLING_FAILS}次获取任务状态失败，停止轮询`)
        stopPolling()
        closeWebSocketConnection()

        // 添加错误日志
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: `连续${MAX_POLLING_FAILS}次获取任务状态失败，已停止轮询`,
          type: 'error'
        })
      }
    }
  } catch (error) {
    // 轮询失败计数增加
    pollingFailCount.value++
    console.error('获取任务状态失败:', error)

    // 如果连续失败次数超过阈值，停止轮询
    if (pollingFailCount.value >= MAX_POLLING_FAILS) {
      // 连续多次失败，停止轮询并显示错误
      stopPolling()
      closeWebSocketConnection()

      // 将任务状态设置为失败
      if (currentTask.value) {
        taskStatus.value = 'failed'

        // 添加错误信息
        if (!currentTask.value.result) {
          currentTask.value.result = {}
        }

        currentTask.value.result.error = {
          type: 'ConnectionError',
          message: '无法连接到服务器，任务状态检查失败',
          reported_at: new Date().toISOString()
        }

        // 记录错误日志
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: '连接失败：无法获取任务状态，请检查服务是否正常运行',
          type: 'error'
        })

        // 显示错误通知
        ElNotification({
          title: '连接失败',
          message: '无法获取任务状态，请检查服务是否正常运行',
          type: 'error',
          duration: 0
        })
      }
    }
  }
}

// 滚动日志到底部
const scrollLogsToBottom = () => {
  nextTick(() => {
    if (logContentRef.value) {
      logContentRef.value.scrollTop = logContentRef.value.scrollHeight
    }
  })
}

// 更新进度步骤
const updateProgressStep = (status) => {
  const statusLower = status.toLowerCase()
  if (['queued', 'preparing'].includes(statusLower)) {
    progressStep.value = 1
  } else if (['running', 'paused'].includes(statusLower)) {
    progressStep.value = 2
  } else if (['completed', 'success', 'error', 'failed', 'stopped', 'cancelled'].includes(statusLower)) {
    progressStep.value = 3
  }
}

// 状态变化通知
const notifyStatusChange = (oldStatus, newStatus) => {
  const oldStatusLower = oldStatus.toLowerCase()
  const newStatusLower = newStatus.toLowerCase()

  // 只在状态真正变化时通知
  if (oldStatusLower === newStatusLower) return

  // 任务完成通知
  if (['completed', 'success'].includes(newStatusLower)) {
    ElNotification({
      title: '任务完成',
      message: '数据比对任务已成功完成',
      type: 'success',
      duration: 5000
    })

    taskLogs.value.push({
      time: formatTime(new Date()),
      message: '任务执行完成！',
      type: 'info'
    })
  }

  // 任务失败通知
  else if (['error', 'failed'].includes(newStatusLower)) {
    ElNotification({
      title: '任务失败',
      message: '数据比对任务执行失败',
      type: 'error',
      duration: 0
    })

    taskLogs.value.push({
      time: formatTime(new Date()),
      message: '任务执行失败！',
      type: 'error'
    })
  }

  // 任务停止通知
  else if (['stopped', 'cancelled'].includes(newStatusLower)) {
    ElNotification({
      title: '任务已停止',
      message: '数据比对任务已被停止',
      type: 'warning',
      duration: 3000
    })
  }

  // 任务开始运行通知
  else if (newStatusLower === 'running' && ['queued', 'preparing'].includes(oldStatusLower)) {
    ElNotification({
      title: '任务开始执行',
      message: '数据比对任务已开始执行',
      type: 'info',
      duration: 3000
    })
  }
}

// 控制任务
const handleTaskControl = async (action: 'pause' | 'resume' | 'stop') => {
  if (!currentTask.value?.id) return

  try {
    loading.value = true
    const res = await controlCompareTask(currentTask.value.id, action)
    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res.success) {
      ElMessage.success(res.message)
      currentTask.value = res.data
      taskStatus.value = res.data.status
    }
  } catch (error) {
    console.error('控制任务失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消执行
const cancelExecution = () => {
  confirmDialogVisible.value = true
}

// 确认取消
const confirmCancel = async () => {
  confirmDialogVisible.value = false

  if (!currentTask.value?.id) return

  try {
    const res = await cancelCompareTask(currentTask.value.id)
    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res.success) {
      ElMessage.success('任务已取消')
      taskStatus.value = 'CANCELLED'
      progressStep.value = 3
      taskLogs.value.push({
        time: formatTime(new Date()),
        message: '任务已手动取消',
        type: 'info'
      })

      // 清理任务资源
      cleanupTaskResources()
    } else {
      ElMessage.error(`取消任务失败：${res.message}`)
    }
  } catch (error) {
    console.error('取消任务出错', error)
    ElMessage.error('取消任务出错')
  }
}

// 查看结果
const viewResults = (taskId) => {
  if (!taskId) {
    console.warn('任务ID为空，无法查看结果')
    ElMessage.warning('任务ID为空，无法查看结果')
    return
  }

  console.log(`跳转到结果页面: taskId=${taskId}`)

  if (progressVisible.value) {
    progressVisible.value = false
  }

  // 使用统一的URL参数格式
  router.push({
    path: '/database/result',
    query: { taskId: taskId }
  })
}

// 关闭进度对话框
const closeProgressDialog = () => {
  progressVisible.value = false

  // 如果任务已完成或异常终止，停止轮询
  const terminalStates = ['completed', 'success', 'error', 'stopped', 'failed', 'cancelled']
  if (currentTask.value?.id && terminalStates.includes(taskStatus.value.toLowerCase())) {
    // 清理任务资源
    cleanupTaskResources()
  } else if (currentTask.value?.id) {
    // 如果任务仍在运行，提示用户
    ElMessageBox.confirm(
      '任务仍在执行中，关闭窗口将不会停止任务。您可以稍后在任务列表中查看结果。确定要关闭吗？',
      '任务进行中',
      {
        confirmButtonText: '关闭窗口',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      progressVisible.value = false
    }).catch(() => {
      progressVisible.value = true
    })
    return
  }
}

// 重置表单
const resetForm = () => {
  activeStep.value = 0
  selectedModel.value = null
  if (modelSelectorRef.value) {
    modelSelectorRef.value.resetSelection()
  }
  if (paramsRef.value) {
    paramsRef.value.resetForm()
  }
  ElMessage.success('已重置配置')
}

// 返回上一页
const goBack = () => {
  router.push('/database/model')
}

// 获取任务状态样式
const getStatusType = (status) => {
  const typeMap = {
    'QUEUED': 'info',
    'PREPARING': 'info',
    'RUNNING': 'warning',
    'COMPLETED': 'success',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'ERROR': 'danger',
    'CANCELLED': '',
    'STOPPED': 'info'
  }
  return typeMap[status.toUpperCase()] || 'info'
}

// 获取任务状态文本
const getStatusText = (status) => {
  const textMap = {
    'QUEUED': '队列中',
    'PREPARING': '准备中',
    'RUNNING': '执行中',
    'COMPLETED': '已完成',
    'SUCCESS': '已完成',
    'FAILED': '失败',
    'ERROR': '错误',
    'CANCELLED': '已取消',
    'STOPPED': '已停止'
  }
  return textMap[status.toUpperCase()] || status
}

// 调整内容高度
const calculateContentHeight = () => {
  nextTick(() => {
    const viewportHeight = window.innerHeight;
    const headerHeight = 91; // 顶部导航栏高度
    const footerHeight = 30; // 页脚版权信息高度
    const marginHeight = 10; // 边距

    contentHeight.value = viewportHeight - headerHeight - footerHeight - marginHeight;
  });
};

// 初始化
onMounted(() => {
  // 调整内容高度
  calculateContentHeight()
  window.addEventListener('resize', calculateContentHeight)
})

// 清理任务资源
const cleanupTaskResources = async () => {
  // 防止重复清理
  if (connectionState.cleanupInProgress) {
    console.log('资源清理已在进行中，跳过重复清理')
    return
  }

  connectionState.cleanupInProgress = true
  console.log('开始清理任务资源')

  try {
    // 1. 停止轮询
    stopPolling()

    // 2. 关闭WebSocket连接
    closeWebSocketConnection()

    // 3. 通知服务器停止轮询（如果任务ID存在且未发送过请求）
    if (currentTask.value?.id && connectionState.lastStopPollingRequest !== currentTask.value.id) {
      try {
        console.log(`清理资源时通知服务器停止轮询: ${currentTask.value.id}`)

        // 记录已发送停止请求
        connectionState.lastStopPollingRequest = currentTask.value.id

        await stopTaskPolling(currentTask.value.id)
        console.log('已通知服务器停止轮询')

        // 添加日志
        taskLogs.value.push({
          time: formatTime(new Date()),
          message: '已清理任务资源',
          type: 'info'
        })
      } catch (error) {
        console.error('通知服务器停止轮询失败:', error)
        // 如果请求失败，重置标记
        if (connectionState.lastStopPollingRequest === currentTask.value.id) {
          connectionState.lastStopPollingRequest = null
        }
      }
    } else if (connectionState.lastStopPollingRequest === currentTask.value?.id) {
      console.log('已发送过停止轮询请求，跳过重复请求')
    }

    // 4. 清理本地缓存
    if (taskPollingTimeout.value) {
      clearTimeout(taskPollingTimeout.value)
      taskPollingTimeout.value = null
    }

    // 5. 重置连接状态
    connectionState.isWebSocketConnected = false
    connectionState.isPollingActive = false

    console.log('任务资源清理完成')
  } finally {
    // 确保清理标记被重置
    connectionState.cleanupInProgress = false
  }
}

// 清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateContentHeight)

  // 清理任务资源
  cleanupTaskResources()
})

// 导出比对结果
const exportCompareResult = async (taskId: string) => {
  try {
    const res = await exportCompareTaskResult(taskId, 'excel');

    // 创建Blob链接并下载
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `比对结果_${taskId}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error: any) {
    console.error('导出结果失败:', error);
    ElMessage.error(error.message || '导出失败');
  }
};

// 重试任务
const retryTask = () => {
  if (!currentTask.value?.id) return

  ElMessageBox.confirm(
    '确定要重新执行该任务吗？',
    '重试任务',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 关闭当前进度窗口
      progressVisible.value = false

      // 准备参数，使用modelId代替taskId
      const modelId = currentTask.value.model
      if (!modelId) {
        ElMessage.error('无法获取模型ID')
        return
      }

      // 重新提交任务
      await submitForm()

      ElMessage.success('任务已重新提交')
    } catch (error) {
      console.error('重试任务失败:', error)
      ElMessage.error('重试任务失败: ' + (error.message || '未知错误'))
    }
  }).catch(() => {})
}
</script>

<style lang="scss" scoped>

.main-content {
  margin: 10px 10px 0 !important;
}

.maincontent {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  height: calc(100vh - 131px);
  background-color: var(--bg-color, --el-bg-color);
}

.page-header {
  display: flex;
  padding: 16px 15px;
  background-color: var(--el-bg-color);
  margin-bottom: 10px;
  flex-shrink: 0;

  .page-title-text {
    font-size: 18px;
    font-weight: 600;
    margin-right: 12px;
  }

  .page-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.execute-container {
  flex: 1;
  overflow: auto;
  background-color: var(--el-bg-color);

  .config-card {
    height: 100%;
    display: flex;
    border-radius: 0px;
    flex-direction: column;
    transition: all 0.3s ease;
    background-color: var(--el-bg-color);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        .title {
          font-size: 16px;
          font-weight: 500;
        }
      }

      .right {
        .el-button + .el-button {
          margin-left: 12px;
        }
      }
    }

    .config-steps {
      margin: 24px 0;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;
      overflow: auto;
      min-height: 400px;

      .step-pane {
        height: 100%;
      }
    }

    .step-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;

      .el-button + .el-button {
        margin-left: 12px;
      }
    }
  }

  :deep(.el-card__body) {
    padding: 0 24px 24px 24px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-steps) {
    .el-step__title {
      font-weight: 500;

      &.is-success {
        color: var(--el-color-success);
      }

      &.is-process {
        font-weight: 700;
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-button) {
    transition: all 0.3s;

    &:hover {
      transform: translateY(-1px);
    }
  }

  :deep(.progress-dialog) {
    .el-dialog__header {
      border-bottom: 1px solid var(--el-border-color-lighter);
      margin-right: 0;
      padding: 16px 24px;
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      border-top: 1px solid var(--el-border-color-lighter);
      padding: 16px 24px;
    }
  }
}

// 进度对话框内部样式
.progress-info {
  .progress-status {
    margin-bottom: 16px;
  }

  .task-detail {
    margin-bottom: 16px;

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .task-name {
        font-weight: 500;
        font-size: 16px;
      }
    }

    .task-time {
      display: flex;
      justify-content: space-between;

      .time-item {
        .time-label {
          color: var(--el-text-color-secondary);
          margin-right: 8px;
        }
      }
    }
  }

  .progress-detail {
    margin-bottom: 16px;

    .progress-text {
      margin-top: 8px;
      text-align: center;
      color: var(--el-text-color-secondary);
    }
  }

  .log-output {
    margin-bottom: 16px;

    .log-header {
      font-weight: 500;
    }

    .log-content {
      max-height: 180px;
      overflow-y: auto;
      padding: 8px 0;
      font-family: monospace;
      font-size: 13px;

      .log-item {
        padding: 2px 0;
        line-height: 1.5;

        .log-time {
          color: var(--el-text-color-secondary);
          margin-right: 8px;
        }

        .log-error {
          color: var(--el-color-danger);
        }
      }

      .empty-log {
        color: var(--el-text-color-secondary);
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }

  .progress-stats {
    .stats-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: var(--el-text-color-primary);
        font-size: 16px;
      }
    }

    .stat-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      margin-bottom: 16px;
    }

    .stat-item {
      text-align: center;
      padding: 12px;
      background: var(--el-bg-color);
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--el-border-color-light);

      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .detailed-stats {
      margin-top: 16px;

      h4 {
        margin: 16px 0 12px 0;
        color: var(--el-text-color-primary);
        font-size: 14px;
      }

      .table-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 12px;
      }

      .table-stat-card {
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .table-name {
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .table-stats {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .mini-stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;

          .label {
            font-size: 11px;
            color: var(--el-text-color-secondary);
            margin-bottom: 2px;
          }

          .value {
            font-size: 13px;
            font-weight: bold;
            color: var(--el-text-color-primary);
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;

  .el-button + .el-button {
    margin-left: 12px;
  }
}

// 深度选择器修复表格单元格样式
:deep(.el-table .cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>