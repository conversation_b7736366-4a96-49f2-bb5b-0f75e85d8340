# main_v4_refactored.py - v4.0架构完整任务管理工作流
import os
import sys
import logging
import yaml
from typing import Dict, Any, Optional
# 核心组件导入
from utils.config_manager import SmartConfigManager
from connectors.db2_connector import DB2Connector
from reporters.reporter_factory import ReporterFactory
from reporters.base_reporter import BaseReporter
from core.engine import compare_sources, compare_sources_memory_dict

# v4.0架构组件导入
from services.sqlalchemy_service import SQLAlchemyComparisonService
from models.pydantic_models import TaskCreateFromModel, TaskCreateDirect, TableRuleCreate
from models.sqlalchemy_models import TaskStatus

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')
logger = logging.getLogger(__name__)


class ComparisonTaskOrchestrator:
    """比对任务编排器 - v4.0架构的核心协调器"""

    def __init__(self, db_url: str, reporter_config: Optional[Dict[str, Any]] = None):
        """初始化任务编排器"""
        self.default_batch_size = 10000
        self.sqlalchemy_service = SQLAlchemyComparisonService.get_instance(db_url)
        self.reporter_config = reporter_config or {'type': 'sqlite'}

    def create_task_from_config(self, config_path: str = None, user_name: str = "system") -> str:
        """从配置文件创建任务"""
        logger.info("开始从配置文件创建任务...")

        # 1. 配置发现和加载
        config_manager = SmartConfigManager()
        if config_path:
            config_files = [config_path]
        else:
            config_files = config_manager.auto_discover_configs()

        if not config_manager.load_config(config_files[0]):
            raise ValueError("无法加载配置文件")

        # 2. 解析配置数据
        try:
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 解析比对规则
            table_rules = []
            if config_manager.rules is not None:
                for table_elem in config_manager.rules.findall('table'):
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        table_rule = TableRuleCreate(
                            table_id=table_elem.get('table_id', f'table_{len(table_rules)}'),
                            table_name=table_elem.get('remark', f'table_{len(table_rules)}'),
                            sql_1=sql1_elem.text.strip(),
                            sql_2=sql2_elem.text.strip()
                        )
                        table_rules.append(table_rule)

            if not table_rules:
                raise ValueError("在规则文件中找不到有效的表规则")

        except (KeyError, AttributeError) as e:
            raise ValueError(f"配置文件格式错误: {e}")
        
        # 3. 创建TaskCreateDirect对象
        task_data = TaskCreateDirect(
            description=f"从配置文件创建的任务",
            comparison_type="content",
            source_connection=db1_config,
            target_connection=db2_config,
            sql_rules=table_rules
        )

        # 4. 使用SQLAlchemy服务创建任务
        task_id = self.sqlalchemy_service.create_task_direct(user_name=user_name, task_data=task_data)

        return task_id

    def create_task_from_model(self, user_name: str, task_data: TaskCreateFromModel) -> str:
        """从模型创建任务"""
        logger.info(f"从模型创建任务: {task_data.model_id}")

        # 使用SQLAlchemy服务的create_task方法
        task_id = self.sqlalchemy_service.create_task(
            user_name=user_name,
            model_id=task_data.model_id,
            task_name=task_data.task_name,
            description=task_data.description
        )

        logger.info(f"从模型创建任务成功: {task_id}")
        return task_id

    def execute_task(self, task_id: str, progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """执行比对任务（简化版本）"""
        logger.info(f"开始执行任务: {task_id}")

        try:
            # 1. 更新任务状态为运行中
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.RUNNING)

            if progress_callback:
                progress_callback("获取任务信息")

            # 2. 一次性获取任务执行所需的所有信息
            execution_info = self.sqlalchemy_service.get_task_execution_info(task_id)
            if not execution_info:
                raise ValueError(f"任务不存在或配置不完整: {task_id}")

            # 3. 验证执行信息的完整性
            validation_errors = execution_info.validate()
            if validation_errors:
                raise ValueError(f"任务配置验证失败: {'; '.join(validation_errors)}")

            if progress_callback:
                progress_callback(f"配置验证完成，找到 {len(execution_info.table_rules)} 个表规则")

            # 4. 获取兼容格式的配置信息（保持与现有代码的兼容性）
            source_config = execution_info.get_source_config()
            target_config = execution_info.get_target_config()
            rules_list = execution_info.get_rules_list()
            cmp_type = execution_info.get_comparison_algorithm_type()

            # 检查是否有表规则可执行
            if not rules_list:
                logger.warning(f"任务 {task_id} 没有找到可执行的表规则")
                if progress_callback:
                    progress_callback("警告: 没有找到可执行的表规则，任务将以空结果完成")

                # 返回空结果而不是失败
                return {
                    "task_id": task_id,
                    "task_name": execution_info.task_name,
                    "model_name": execution_info.model.name if execution_info.model else execution_info.task_name or "兼容模式任务",
                    "comparison_type": execution_info.model.cmp_type if execution_info.model else "content",
                    "status": "completed",
                    "tables_compared": 0,
                    "source_db": f"{source_config['type']}://{source_config['host']}:{source_config['port']}/{source_config['dbname']}",
                    "target_db": f"{target_config['type']}://{target_config['host']}:{target_config['port']}/{target_config['dbname']}",
                    "task_type": "single_table" if execution_info.is_single_table_task else "model_level",
                    "total_records": 0,
                    "processed_records": 0,
                    "diff_records": 0,
                    "source_only": 0,
                    "target_only": 0,
                    "exec_time": 0.0,
                    "message": "没有找到可执行的表规则"
                }

            # 记录执行信息摘要
            summary = execution_info.get_summary()
            logger.info(f"任务执行摘要: {summary}")

            # 5. 执行比对任务
            total_stats = {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': 0.0
            }

            for i, rule in enumerate(rules_list):
                table_name = rule.get('table_name', rule.get('table_id', f'table_{i}'))
                logger.info(f"执行表规则 {i+1}/{len(rules_list)}: {table_name}")

                # 更新进度
                if progress_callback:
                    progress_callback(f"执行表规则 {i+1}/{len(rules_list)}: {table_name}")

                # 执行单表比对
                table_stats = self._execute_single_table_comparison(
                    task_id, rule, source_config, target_config, cmp_type
                )

                # 累计统计信息
                for key in total_stats:
                    if key in table_stats:
                        total_stats[key] += table_stats[key]

            # 6. 构建详细的执行结果（兼容模式安全处理）
            result = {
                "task_id": task_id,
                "task_name": execution_info.task_name,
                "model_name": execution_info.model.name if execution_info.model else execution_info.task_name or "兼容模式任务",
                "comparison_type": execution_info.model.cmp_type if execution_info.model else "content",
                "status": "completed",
                "tables_compared": len(rules_list),
                "source_db": f"{source_config['type']}://{source_config['host']}:{source_config['port']}/{source_config['dbname']}",
                "target_db": f"{target_config['type']}://{target_config['host']}:{target_config['port']}/{target_config['dbname']}",
                "task_type": "single_table" if execution_info.is_single_table_task else "model_level",
                **total_stats
            }

            # 7. 更新任务完成状态
            self.sqlalchemy_service.update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                **total_stats
            )

            if progress_callback:
                progress_callback("任务完成")

            logger.info(f"任务执行完成: {task_id}")
            return result

        except Exception as e:
            # 更新任务失败状态
            try:
                self.sqlalchemy_service.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_msg=str(e)
                )
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {update_error}")

            if progress_callback:
                progress_callback(f"执行失败: {str(e)}")

            logger.error(f"任务执行失败: {task_id}, 错误: {e}", exc_info=True)
            raise

    def _execute_single_table_comparison(
        self,
        task_id: str,
        rule: Dict[str, Any],
        source_config: Dict[str, Any],
        target_config: Dict[str, Any],
        cmp_type: int
    ) -> Dict[str, Any]:
        """
        执行单表比对

        Args:
            task_id: 任务ID
            rule: 表规则配置
            source_config: 源数据库配置
            target_config: 目标数据库配置
            cmp_type: 比对算法类型

        Returns:
            比对统计信息
        """
        import time
        start_time = time.perf_counter()

        try:
            # 1. 创建数据库连接器
            source_connector = DB2Connector(
                source_config,
                query=rule['sql_1'],
                batch_size=self.default_batch_size
            )
            target_connector = DB2Connector(
                target_config,
                query=rule['sql_2'],
                batch_size=self.default_batch_size
            )

            # 2. 创建报告器
            table_name_for_reporter = rule.get('table_name', rule.get('table_id', 'comparison_results'))
            reporters = ReporterFactory.create_reporters(
                self.reporter_config, 
                task_id, 
                table_name_for_reporter
            )

            if not reporters:
                raise RuntimeError("无法创建任何报告器，任务中止")

            # 3. 根据算法类型执行比对
            if cmp_type == 1:
                logger.info(f"使用流式归并算法比对表: {rule.get('table_name', rule.get('table_id'))}")
                for reporter in reporters:
                    compare_sources(source_connector, target_connector, reporter)
            elif cmp_type == 2:
                logger.info(f"使用内存字典算法比对表: {rule.get('table_name', rule.get('table_id'))}")
                for reporter in reporters:
                    compare_sources_memory_dict(source_connector, target_connector, reporter)
            else:
                raise ValueError(f"不支持的比对算法类型: {cmp_type}")

            # 4. 收集统计信息
            exec_time = time.perf_counter() - start_time

            # 从第一个报告器获取统计信息（假设所有报告器统计信息一致）
            stats = self._collect_comparison_statistics(reporters[0], source_connector, target_connector, exec_time)

            # 5. 将结果关联到任务ID并保存到SQLAlchemy数据库
            self._save_results_to_sqlalchemy(task_id, rule, stats)

            logger.info(f"表比对完成: {rule.get('table_name', rule.get('table_id'))}, 耗时: {exec_time:.2f}秒")
            return stats

        except Exception as e:
            logger.error(f"表比对失败: {rule.get('table_name', rule.get('table_id'))}, 错误: {e}")
            raise

    def _collect_comparison_statistics(
        self,
        reporter: BaseReporter,
        source_connector: DB2Connector,
        target_connector: DB2Connector,
        exec_time: float
    ) -> Dict[str, Any]:
        """
        收集比对统计信息

        Args:
            reporter: 报告器实例
            source_connector: 源连接器
            target_connector: 目标连接器
            exec_time: 执行时间

        Returns:
            统计信息字典
        """
        try:
            # 从reporter获取统计信息（如果支持）
            stats = {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': exec_time
            }

            # 尝试从reporter获取详细统计（如果reporter支持）
            if hasattr(reporter, 'get_statistics'):
                reporter_stats = reporter.get_statistics()
                stats.update(reporter_stats)

            # 尝试从连接器获取记录数（如果支持）
            if hasattr(source_connector, '_stats'):
                stats['source_records'] = source_connector._stats.get('total_fetched', 0)
            if hasattr(target_connector, '_stats'):
                stats['target_records'] = target_connector._stats.get('total_fetched', 0)

            # 计算总处理记录数
            stats['processed_records'] = stats.get('source_records', 0) + stats.get('target_records', 0)

            return stats

        except Exception as e:
            logger.warning(f"收集统计信息失败: {e}")
            # 返回基本统计信息
            return {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': exec_time
            }

    def _save_results_to_sqlalchemy(self, task_id: str, rule: Dict[str, Any], stats: Dict[str, Any]):
        """
        将比对结果保存到SQLAlchemy数据库

        Args:
            task_id: 任务ID
            rule: 表规则
            stats: 统计信息
        """
        try:
            # 这里可以将SQLite报告器的结果迁移到SQLAlchemy数据库
            # 或者直接使用SQLAlchemy报告器

            # 更新任务的统计信息
            self.sqlalchemy_service.update_task_status(
                task_id,
                TaskStatus.RUNNING,  # 保持运行状态，等待所有表完成
                **stats
            )

            logger.info(f"表 {rule.get('table_name', rule.get('table_id'))} 的结果已保存到数据库")

        except Exception as e:
            logger.error(f"保存结果到数据库失败: {e}")
            # 不抛出异常，避免影响其他表的处理

    def migrate_config_to_database(self, config_path: str = None, user_name: str = "system") -> Dict[str, Any]:
        """将配置文件迁移到数据库"""

        # 1. 加载配置文件
        config_manager = SmartConfigManager()
        if config_path:
            config_files = [config_path]

        if not config_manager.load_config(config_files[0]):
            raise ValueError("无法加载配置文件")

        migration_result = {
            'connections_created': 0,
            'models_created': 0,
            'table_rules_created': 0,
            'config_file': config_files[0]
        }

        try:
            # 2. 迁移数据库连接配置
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 迁移源连接
            source_conn = self.sqlalchemy_service.get_connection_by_name(db1_config.get('ip', ''))
            if source_conn:
                source_conn_id = source_conn.id
            else:
                source_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db1_config.get('ip', ''),
                    type=db1_config.get('type', 'DB2'),
                    host=db1_config.get('ip', ''),
                    port=int(db1_config.get('port', 50000)),
                    username=db1_config.get('user_name', ''),
                    password=db1_config.get('password', ''),
                    database=db1_config.get('schema', '')
                )
                migration_result['connections_created'] += 1

            # 迁移目标连接
            target_conn = self.sqlalchemy_service.get_connection_by_name(db2_config.get('ip', ''))
            if target_conn:
                target_conn_id = target_conn.id
            else:
                target_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db2_config.get('ip', ''),
                    type=db2_config.get('type', 'DB2'),
                    host=db2_config.get('ip', ''),
                    port=int(db2_config.get('port', 50000)),
                    username=db2_config.get('user_name', ''),
                    password=db2_config.get('password', ''),
                    database=db2_config.get('schema', '')
                )
                migration_result['connections_created'] += 1

            # 3. 创建比对模型
            common_config = dict(config_manager.config['COMMON'])
            model_id = self._create_model_from_config(user_name, source_conn_id, target_conn_id, common_config)
            migration_result['models_created'] += 1

            # 4. 迁移表规则
            if config_manager.rules is not None:
                table_rules_count = self._migrate_table_rules(model_id, config_manager.rules)
                migration_result['table_rules_created'] = table_rules_count

            logger.info(f"配置迁移完成: {migration_result}")
            return migration_result

        except Exception as e:
            logger.error(f"配置迁移失败: {e}")
            raise

    def _create_model_from_config(self, user_name: str, source_conn_id: int, target_conn_id: int, common_config: Dict[str, str]) -> int:
        """从配置创建比对模型"""
        try:
            # 使用TAB_RULE配置的文件名称作为模型名称
            tab_rule_path = common_config.get('tab_rule', common_config.get('TAB_RULE', '从配置文件迁移的模型'))

            # 从路径中提取文件名
            if tab_rule_path != '从配置文件迁移的模型':
                file_name = os.path.basename(tab_rule_path)
                model_name = os.path.splitext(file_name)[0]
            else:
                model_name = tab_rule_path

            # 检查是否已存在相同名称的模型
            existing_model = self.sqlalchemy_service.get_comparison_model_by_name(model_name)
            if existing_model:
                logger.info(f"比对模型已存在，使用现有模型: {model_name}, ID: {existing_model.id}")
                return existing_model.id

            description = f"从配置文件迁移的比对模型，用户: {user_name}"

            # 处理比对类型
            cmp_type_raw = common_config.get('cmp_type', common_config.get('CMP_TYPE', '2'))
            cmp_type = self._convert_cmp_type(cmp_type_raw)

            # 全局配置默认设为空
            global_config = {}

            # 创建比对模型
            model_id = self.sqlalchemy_service.create_comparison_model(
                name=model_name,
                description=description,
                source_connid=source_conn_id,
                target_connid=target_conn_id,
                cmp_type=cmp_type,
                global_config=global_config
            )

            logger.info(f"从配置创建比对模型成功: {model_name}, ID: {model_id}")
            return model_id

        except Exception as e:
            logger.error(f"从配置创建比对模型失败: {e}", exc_info=True)
            raise

    def _convert_cmp_type(self, cmp_type_raw: str) -> str:
        """转换比对类型"""
        # 将配置文件中的数字类型转换为字符串类型
        type_mapping = {
            '1': 'stream_merge',      # 流式归并算法
            '2': 'memory_dict',       # 内存字典算法
            'stream_merge': 'stream_merge',
            'memory_dict': 'memory_dict',
            'content': 'content'
        }

        return type_mapping.get(str(cmp_type_raw).lower(), 'content')

    def _migrate_table_rules(self, model_id: int, rules_xml) -> int:
        """迁移表规则到数据库"""
        count = 0

        try:
            for table_elem in rules_xml.findall('table'):
                try:
                    # 提取表规则信息
                    table_id = table_elem.get('id')  # 可能为None，让create_table_rule自动生成
                    table_name = table_elem.get('name', table_elem.get('table_id', table_id))
                    remark = table_elem.get('remark', '')

                    # 提取SQL语句
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        sql_1 = sql1_elem.text.strip() if sql1_elem.text else ""
                        sql_2 = sql2_elem.text.strip() if sql2_elem.text else ""

                        if not sql_1 or not sql_2:
                            logger.warning(f"跳过空SQL的表规则: {table_id}")
                            continue

                        # 提取其他配置信息
                        primary_keys = []
                        ignore_fields = []
                        field_mappings = {}

                        # 解析主键配置
                        pk_elem = table_elem.find('primary_keys')
                        if pk_elem is not None and pk_elem.text:
                            primary_keys = [key.strip() for key in pk_elem.text.split(',') if key.strip()]

                        # 解析忽略字段配置
                        ignore_elem = table_elem.find('ignore_fields')
                        if ignore_elem is not None and ignore_elem.text:
                            ignore_fields = [field.strip() for field in ignore_elem.text.split(',') if field.strip()]

                        # 解析字段映射配置
                        mapping_elem = table_elem.find('field_mappings')
                        if mapping_elem is not None:
                            for mapping in mapping_elem.findall('mapping'):
                                source_field = mapping.get('source')
                                target_field = mapping.get('target')
                                if source_field and target_field:
                                    field_mappings[source_field] = target_field

                        # 创建表规则
                        rule_id = self.sqlalchemy_service.create_table_rule(
                            model_id=model_id,
                            table_id=table_id,
                            table_name=table_name,
                            sql_1=sql_1,
                            sql_2=sql_2,
                            remark=remark,
                            primary_keys=primary_keys,
                            ignore_fields=ignore_fields,
                            field_mappings=field_mappings
                        )

                        logger.info(f"迁移表规则成功: {table_name} (ID: {table_id}) -> 规则ID: {rule_id}")
                        count += 1

                    else:
                        logger.warning(f"跳过缺少SQL的表规则: {table_id}")

                except Exception as e:
                    logger.error(f"迁移单个表规则失败: {table_elem.get('id', 'unknown')}, 错误: {e}")
                    continue

            logger.info(f"表规则迁移完成，成功处理 {count} 个规则")
            return count

        except Exception as e:
            logger.error(f"表规则迁移失败: {e}", exc_info=True)
            raise


def main(config_path: str = 'config/run_config.yaml'):
    """主函数，通过YAML配置文件驱动任务执行"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except (FileNotFoundError, yaml.YAMLError) as e:
        logger.error(f"无法加载或解析配置文件 {config_path}: {e}")
        sys.exit(1)

    # 提取数据库URL和报告器配置
    db_url = config.get('database', {}).get('url', 'sqlite:///sqlcompare_v4.db')
    reporter_config = config.get('reporters', {})

    # 初始化任务编排器
    orchestrator = ComparisonTaskOrchestrator(db_url=db_url, reporter_config=reporter_config)

    # 根据配置执行任务
    task_config = config.get('task', {})
    task_type = task_config.get('type', 'from_config')
    user_name = task_config.get('user_name', 'yaml_user')

    try:
        if task_type == 'from_config':
            config_path = task_config.get('config_path')
            if not config_path:
                raise ValueError("当任务类型为 'from_config' 时，必须提供 'config_path'")
            
            task_id = orchestrator.create_task_from_config(config_path=config_path, user_name=user_name)
            logger.info(f"从配置文件创建任务成功，ID: {task_id}。现在开始执行...")
            result = orchestrator.execute_task(task_id=task_id)
            logger.info(f"任务 {task_id} 执行完成。结果: {result}")

        elif task_type == 'from_model':
            model_id = task_config.get('model_id')
            task_name = task_config.get('task_name')
            description = task_config.get('description', '')

            if not all([model_id, task_name]):
                raise ValueError("当任务类型为 'from_model' 时，必须提供 'model_id' 和 'task_name'")

            task_data = TaskCreateFromModel(model_id=model_id, task_name=task_name, description=description)
            task_id = orchestrator.create_task_from_model(user_name=user_name, task_data=task_data)
            logger.info(f"从模型创建任务成功，ID: {task_id}。现在开始执行...")
            result = orchestrator.execute_task(task_id=task_id)
            logger.info(f"任务 {task_id} 执行完成。结果: {result}")

        else:
            raise ValueError(f"不支持的任务类型: {task_type}")

    except Exception as e:
        logger.error(f"任务执行失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == '__main__':
    main()
