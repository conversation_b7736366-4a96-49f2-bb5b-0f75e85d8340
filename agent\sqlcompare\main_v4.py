# main_v4_refactored.py - v4.0架构完整任务管理工作流
import os
import sys
import logging
import argparse
from typing import Dict, Any, Optional, Union, List
# 核心组件导入
from utils.config_manager import SmartConfigManager
from connectors.db2_connector import DB2Connector
from core.engine import compare_sources, compare_sources_memory_dict

# v4.0架构组件导入
from services.sqlalchemy_service import SQLAlchemyComparisonService
from models.pydantic_models import TaskCreateFromModel, TaskCreateDirect, TableRuleCreate
from models.sqlalchemy_models import TaskStatus

# 报告器相关导入
from reporters.reporter_factory import ReporterFactory
from reporters.base_reporter import BaseReporter

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')
logger = logging.getLogger(__name__)


class ComparisonTaskOrchestrator:
    """比对任务编排器 - v4.0架构的核心协调器，支持多种报告器配置"""

    def __init__(self,
                 db_url: str = "sqlite:///sqlcompare_v4.db",
                 reporter_config: Optional[Dict[str, Any]] = None):
        """
        初始化任务编排器

        Args:
            db_url: 数据库连接URL
            reporter_config: 报告器配置，支持多种报告器类型
        """
        self.default_batch_size = 10000
        self.sqlalchemy_service = SQLAlchemyComparisonService.get_instance(db_url)

        # 报告器配置管理
        self.reporter_config = reporter_config or self._get_default_reporter_config()
        self._validate_reporter_config()

        logger.info(f"任务编排器初始化完成")
        logger.info(f"数据库: {self.sqlalchemy_service.database_type}")
        logger.info(f"报告器类型: {self.reporter_config.get('type', 'sqlite')}")

    def _get_default_reporter_config(self) -> Dict[str, Any]:
        """获取默认报告器配置"""
        return {
            'type': 'sqlite',
            'batch_size': self.default_batch_size,
            'high_performance_mode': True,
            'silent_mode': True
        }

    def _validate_reporter_config(self):
        """验证报告器配置"""
        if not isinstance(self.reporter_config, dict):
            raise ValueError("报告器配置必须是字典类型")

        reporter_type = self.reporter_config.get('type', 'sqlite').lower()
        valid_types = ['sqlite', 'postgresql', 'csv', 'multiple']

        if reporter_type not in valid_types:
            raise ValueError(f"不支持的报告器类型: {reporter_type}，支持的类型: {valid_types}")

        # 验证多报告器配置
        if reporter_type == 'multiple':
            reporters = self.reporter_config.get('reporters', [])
            if not reporters:
                raise ValueError("多报告器配置中必须指定子报告器列表")

        logger.debug(f"报告器配置验证通过: {reporter_type}")

    @classmethod
    def create_with_sqlite_reporter(cls, db_url: str = "sqlite:///sqlcompare_v4.db",
                                   sqlite_db_path: str = None) -> 'ComparisonTaskOrchestrator':
        """
        创建使用SQLite报告器的任务编排器

        Args:
            db_url: SQLAlchemy数据库URL
            sqlite_db_path: SQLite报告器数据库路径

        Returns:
            任务编排器实例
        """
        reporter_config = {
            'type': 'sqlite',
            'db_path': sqlite_db_path or 'comparison_results.db',
            'high_performance_mode': True,
            'silent_mode': True
        }
        return cls(db_url=db_url, reporter_config=reporter_config)

    @classmethod
    def create_with_postgresql_reporter(cls, db_url: str = "sqlite:///sqlcompare_v4.db",
                                       pg_host: str = 'localhost', pg_port: int = 5432,
                                       pg_database: str = 'sqlcompare', pg_username: str = 'postgres',
                                       pg_password: str = 'postgres') -> 'ComparisonTaskOrchestrator':
        """
        创建使用PostgreSQL报告器的任务编排器

        Args:
            db_url: SQLAlchemy数据库URL
            pg_host: PostgreSQL主机
            pg_port: PostgreSQL端口
            pg_database: PostgreSQL数据库名
            pg_username: PostgreSQL用户名
            pg_password: PostgreSQL密码

        Returns:
            任务编排器实例
        """
        reporter_config = {
            'type': 'postgresql',
            'host': pg_host,
            'port': pg_port,
            'database': pg_database,
            'username': pg_username,
            'password': pg_password,
            'use_copy': True,
            'high_performance_mode': True,
            'silent_mode': True
        }
        return cls(db_url=db_url, reporter_config=reporter_config)

    @classmethod
    def create_with_csv_reporter(cls, db_url: str = "sqlite:///sqlcompare_v4.db",
                                output_dir: str = './exports/') -> 'ComparisonTaskOrchestrator':
        """
        创建使用CSV报告器的任务编排器

        Args:
            db_url: SQLAlchemy数据库URL
            output_dir: CSV输出目录

        Returns:
            任务编排器实例
        """
        reporter_config = {
            'type': 'csv',
            'output_dir': output_dir,
            'filename': 'comparison_results.csv'
        }
        return cls(db_url=db_url, reporter_config=reporter_config)

    @classmethod
    def create_with_multiple_reporters(cls, db_url: str = "sqlite:///sqlcompare_v4.db",
                                     reporters_config: List[Dict[str, Any]] = None) -> 'ComparisonTaskOrchestrator':
        """
        创建使用多个报告器的任务编排器

        Args:
            db_url: SQLAlchemy数据库URL
            reporters_config: 报告器配置列表

        Returns:
            任务编排器实例
        """
        if not reporters_config:
            # 默认配置：SQLite + CSV
            reporters_config = [
                {'type': 'sqlite', 'db_path': 'comparison_results.db'},
                {'type': 'csv', 'output_dir': './exports/'}
            ]

        reporter_config = {
            'type': 'multiple',
            'reporters': reporters_config
        }
        return cls(db_url=db_url, reporter_config=reporter_config)

    def create_task_from_config(self, config_path: str = None, user_name: str = "system") -> str:
        """从配置文件创建任务"""
        logger.info("开始从配置文件创建任务...")

        # 1. 配置发现和加载
        config_manager = SmartConfigManager()
        if config_path:
            config_files = [config_path]
        else:
            config_files = config_manager.auto_discover_configs()

        if not config_manager.load_config(config_files[0]):
            raise ValueError("无法加载配置文件")

        # 2. 解析配置数据
        try:
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 解析比对规则
            table_rules = []
            if config_manager.rules is not None:
                for table_elem in config_manager.rules.findall('table'):
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        table_rule = TableRuleCreate(
                            table_id=table_elem.get('table_id', f'table_{len(table_rules)}'),
                            table_name=table_elem.get('remark', f'table_{len(table_rules)}'),
                            sql_1=sql1_elem.text.strip(),
                            sql_2=sql2_elem.text.strip()
                        )
                        table_rules.append(table_rule)

            if not table_rules:
                raise ValueError("在规则文件中找不到有效的表规则")

        except (KeyError, AttributeError) as e:
            raise ValueError(f"配置文件格式错误: {e}")
        
        # 3. 创建TaskCreateDirect对象
        task_data = TaskCreateDirect(
            description=f"从配置文件创建的任务",
            comparison_type="content",
            source_connection=db1_config,
            target_connection=db2_config,
            sql_rules=table_rules
        )

        # 4. 使用SQLAlchemy服务创建任务
        task_id = self.sqlalchemy_service.create_task_direct(user_name=user_name, task_data=task_data)

        return task_id

    def create_task_from_model(self, user_name: str, task_data: TaskCreateFromModel) -> str:
        """从模型创建任务"""
        logger.info(f"从模型创建任务: {task_data.model_id}")

        # 使用SQLAlchemy服务的create_task方法
        task_id = self.sqlalchemy_service.create_task(
            user_name=user_name,
            model_id=task_data.model_id,
            task_name=task_data.task_name,
            description=task_data.description
        )

        logger.info(f"从模型创建任务成功: {task_id}")
        return task_id

    def execute_task(self, task_id: str, progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """执行比对任务（简化版本）"""
        logger.info(f"开始执行任务: {task_id}")

        try:
            # 1. 更新任务状态为运行中
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.RUNNING)

            if progress_callback:
                progress_callback("获取任务信息")

            # 2. 一次性获取任务执行所需的所有信息
            execution_info = self.sqlalchemy_service.get_task_execution_info(task_id)
            if not execution_info:
                raise ValueError(f"任务不存在或配置不完整: {task_id}")

            # 3. 验证执行信息的完整性
            validation_errors = execution_info.validate()
            if validation_errors:
                raise ValueError(f"任务配置验证失败: {'; '.join(validation_errors)}")

            if progress_callback:
                progress_callback(f"配置验证完成，找到 {len(execution_info.table_rules)} 个表规则")

            # 4. 获取兼容格式的配置信息（保持与现有代码的兼容性）
            source_config = execution_info.get_source_config()
            target_config = execution_info.get_target_config()
            rules_list = execution_info.get_rules_list()
            cmp_type = execution_info.get_comparison_algorithm_type()

            # 检查是否有表规则可执行
            if not rules_list:
                logger.warning(f"任务 {task_id} 没有找到可执行的表规则")
                if progress_callback:
                    progress_callback("警告: 没有找到可执行的表规则，任务将以空结果完成")

                # 返回空结果而不是失败
                return {
                    "task_id": task_id,
                    "task_name": execution_info.task_name,
                    "model_name": execution_info.model.name if execution_info.model else execution_info.task_name or "兼容模式任务",
                    "comparison_type": execution_info.model.cmp_type if execution_info.model else "content",
                    "status": "completed",
                    "tables_compared": 0,
                    "source_db": f"{source_config['type']}://{source_config['host']}:{source_config['port']}/{source_config['dbname']}",
                    "target_db": f"{target_config['type']}://{target_config['host']}:{target_config['port']}/{target_config['dbname']}",
                    "task_type": "single_table" if execution_info.is_single_table_task else "model_level",
                    "total_records": 0,
                    "processed_records": 0,
                    "diff_records": 0,
                    "source_only": 0,
                    "target_only": 0,
                    "exec_time": 0.0,
                    "message": "没有找到可执行的表规则"
                }

            # 记录执行信息摘要
            summary = execution_info.get_summary()
            logger.info(f"任务执行摘要: {summary}")

            # 5. 执行比对任务
            total_stats = {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': 0.0
            }

            for i, rule in enumerate(rules_list):
                table_name = rule.get('table_name', rule.get('table_id', f'table_{i}'))
                logger.info(f"执行表规则 {i+1}/{len(rules_list)}: {table_name}")

                # 更新进度
                if progress_callback:
                    progress_callback(f"执行表规则 {i+1}/{len(rules_list)}: {table_name}")

                # 执行单表比对
                table_stats = self._execute_single_table_comparison(
                    task_id, rule, source_config, target_config, cmp_type
                )

                # 累计统计信息
                for key in total_stats:
                    if key in table_stats:
                        total_stats[key] += table_stats[key]

            # 6. 构建详细的执行结果（兼容模式安全处理）
            result = {
                "task_id": task_id,
                "task_name": execution_info.task_name,
                "model_name": execution_info.model.name if execution_info.model else execution_info.task_name or "兼容模式任务",
                "comparison_type": execution_info.model.cmp_type if execution_info.model else "content",
                "status": "completed",
                "tables_compared": len(rules_list),
                "source_db": f"{source_config['type']}://{source_config['host']}:{source_config['port']}/{source_config['dbname']}",
                "target_db": f"{target_config['type']}://{target_config['host']}:{target_config['port']}/{target_config['dbname']}",
                "task_type": "single_table" if execution_info.is_single_table_task else "model_level",
                **total_stats
            }

            # 7. 更新任务完成状态
            self.sqlalchemy_service.update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                **total_stats
            )

            if progress_callback:
                progress_callback("任务完成")

            logger.info(f"任务执行完成: {task_id}")
            return result

        except Exception as e:
            # 更新任务失败状态
            try:
                self.sqlalchemy_service.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_msg=str(e)
                )
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {update_error}")

            if progress_callback:
                progress_callback(f"执行失败: {str(e)}")

            logger.error(f"任务执行失败: {task_id}, 错误: {e}", exc_info=True)
            raise

    def _execute_single_table_comparison(
        self,
        task_id: str,
        rule: Dict[str, Any],
        source_config: Dict[str, Any],
        target_config: Dict[str, Any],
        cmp_type: int
    ) -> Dict[str, Any]:
        """
        执行单表比对 - 支持多种报告器配置

        Args:
            task_id: 任务ID
            rule: 表规则配置
            source_config: 源数据库配置
            target_config: 目标数据库配置
            cmp_type: 比对算法类型

        Returns:
            比对统计信息
        """
        import time
        start_time = time.perf_counter()

        try:
            # 1. 创建数据库连接器
            source_connector = DB2Connector(
                source_config,
                query=rule['sql_1'],
                batch_size=self.default_batch_size
            )
            target_connector = DB2Connector(
                target_config,
                query=rule['sql_2'],
                batch_size=self.default_batch_size
            )

            # 2. 创建报告器（使用工厂模式，支持多种类型）
            reporter = self._create_reporter_for_task(task_id, rule)

            # 3. 执行比对（使用上下文管理器确保资源清理）
            with reporter:
                # 根据算法类型执行比对
                if cmp_type == 1:
                    logger.info(f"使用流式归并算法比对表: {rule.get('table_name', rule.get('table_id'))}")
                    compare_sources(source_connector, target_connector, reporter)
                elif cmp_type == 2:
                    logger.info(f"使用内存字典算法比对表: {rule.get('table_name', rule.get('table_id'))}")
                    compare_sources_memory_dict(source_connector, target_connector, reporter)
                else:
                    raise ValueError(f"不支持的比对算法类型: {cmp_type}")

                # 强制提交剩余数据（如果报告器支持）
                if hasattr(reporter, 'force_commit'):
                    reporter.force_commit()

            # 4. 收集统计信息
            exec_time = time.perf_counter() - start_time
            stats = self._collect_comparison_statistics(reporter, source_connector, target_connector, exec_time)

            # 5. 将结果关联到任务ID并保存到SQLAlchemy数据库
            self._save_results_to_sqlalchemy(task_id, rule, stats)

            return stats

        except Exception as e:
            logger.error(f"单表比对执行失败: {e}")
            raise

    def _create_reporter_for_task(self, task_id: str, rule: Dict[str, Any]) -> BaseReporter:
        """
        为任务创建报告器实例

        Args:
            task_id: 任务ID
            rule: 表规则配置

        Returns:
            报告器实例
        """
        try:
            # 准备报告器配置
            table_name = rule.get('table_name', rule.get('table_id', 'unknown'))

            # 合并基础配置和用户配置
            reporter_config = {
                **self.reporter_config,  # 用户配置
                'task_id': task_id,
                'comparison_table': table_name,
                'batch_size': self.default_batch_size
            }

            # 为不同类型的报告器设置特定的默认值
            reporter_type = reporter_config.get('type', 'sqlite').lower()

            if reporter_type == 'sqlite':
                reporter_config.setdefault('db_path', f'task_{task_id}_results.db')
                reporter_config.setdefault('table_name', 'comparison_results')
            elif reporter_type == 'csv':
                reporter_config.setdefault('output_dir', './exports/')
                reporter_config.setdefault('filename', f'{table_name}_results.csv')
            elif reporter_type == 'multiple':
                # 为多报告器的每个子报告器设置任务相关信息
                for sub_config in reporter_config.get('reporters', []):
                    sub_config['task_id'] = task_id
                    sub_config['comparison_table'] = table_name

            # 使用工厂创建报告器，支持降级机制
            fallback_config = {
                'type': 'sqlite',
                'task_id': task_id,
                'comparison_table': table_name,
                'db_path': f'task_{task_id}_fallback_results.db',
                'batch_size': self.default_batch_size,
                'silent_mode': True
            }

            reporter = ReporterFactory.create_with_fallback(reporter_config, fallback_config)

            logger.info(f"报告器创建成功: {type(reporter).__name__} for table {table_name}")
            return reporter

        except Exception as e:
            logger.error(f"报告器创建失败: {e}")
            raise

    def _collect_comparison_statistics(
        self,
        reporter: BaseReporter,
        source_connector: DB2Connector,
        target_connector: DB2Connector,
        exec_time: float
    ) -> Dict[str, Any]:
        """
        收集比对统计信息

        Args:
            reporter: 报告器实例
            source_connector: 源连接器
            target_connector: 目标连接器
            exec_time: 执行时间

        Returns:
            统计信息字典
        """
        try:
            # 基础统计信息
            stats = {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': exec_time,
                'reporter_type': type(reporter).__name__
            }

            # 从不同类型的报告器获取统计信息
            if hasattr(reporter, 'get_performance_stats'):
                # 支持性能统计的报告器（如SqliteReporter、PostgresReporter）
                perf_stats = reporter.get_performance_stats()
                stats['total_records'] = perf_stats.get('total_records', 0)
                stats['processed_records'] = perf_stats.get('total_records', 0)

                # 尝试获取详细统计（如果支持）
                if hasattr(reporter, 'get_table_summary'):
                    try:
                        table_name = getattr(reporter, 'comparison_table', 'unknown')
                        summary = reporter.get_table_summary(table_name)
                        stats['diff_records'] = summary.get('DIFF', 0) + summary.get('FIELD_DIFF', 0)
                        stats['source_only'] = summary.get('SOURCE_ONLY', 0)
                        stats['target_only'] = summary.get('TARGET_ONLY', 0)
                    except Exception as e:
                        logger.debug(f"获取表摘要失败: {e}")

            elif hasattr(reporter, 'get_statistics'):
                # 支持基础统计的报告器
                reporter_stats = reporter.get_statistics()
                stats.update(reporter_stats)

            elif hasattr(reporter, 'record_count'):
                # CSV报告器等简单统计
                stats['total_records'] = getattr(reporter, 'record_count', 0)
                stats['processed_records'] = getattr(reporter, 'record_count', 0)

            # 处理多报告器的情况
            if hasattr(reporter, 'reporters'):
                # MultiReporter的情况
                multi_stats = {}
                for i, sub_reporter in enumerate(reporter.reporters):
                    try:
                        sub_stats = self._collect_comparison_statistics(
                            sub_reporter, source_connector, target_connector, exec_time
                        )
                        multi_stats[f'reporter_{i+1}'] = sub_stats

                        # 使用第一个报告器的统计作为主要统计
                        if i == 0:
                            stats.update({k: v for k, v in sub_stats.items()
                                        if k not in ['reporter_type', 'exec_time']})
                    except Exception as e:
                        logger.debug(f"收集子报告器 {i+1} 统计失败: {e}")

                stats['multi_reporter_stats'] = multi_stats

            # 尝试从连接器获取记录数（如果支持）
            if hasattr(source_connector, '_stats'):
                stats['source_records'] = source_connector._stats.get('total_fetched', 0)
            if hasattr(target_connector, '_stats'):
                stats['target_records'] = target_connector._stats.get('total_fetched', 0)

            logger.debug(f"统计信息收集完成: {stats}")
            return stats

        except Exception as e:
            logger.error(f"收集统计信息失败: {e}")
            # 返回基础统计信息
            return {
                'total_records': 0,
                'processed_records': 0,
                'diff_records': 0,
                'source_only': 0,
                'target_only': 0,
                'exec_time': exec_time,
                'reporter_type': type(reporter).__name__,
                'error': str(e)
            }

    def _save_results_to_sqlalchemy(self, task_id: str, rule: Dict[str, Any], stats: Dict[str, Any]):
        """
        将比对结果保存到SQLAlchemy数据库

        Args:
            task_id: 任务ID
            rule: 表规则
            stats: 统计信息
        """
        try:
            # 这里可以将SQLite报告器的结果迁移到SQLAlchemy数据库
            # 或者直接使用SQLAlchemy报告器

            # 更新任务的统计信息
            self.sqlalchemy_service.update_task_status(
                task_id,
                TaskStatus.RUNNING,  # 保持运行状态，等待所有表完成
                **stats
            )

            logger.info(f"表 {rule.get('table_name', rule.get('table_id'))} 的结果已保存到数据库")

        except Exception as e:
            logger.error(f"保存结果到数据库失败: {e}")
            # 不抛出异常，避免影响其他表的处理

    def migrate_config_to_database(self, config_path: str = None, user_name: str = "system") -> Dict[str, Any]:
        """将配置文件迁移到数据库"""

        # 1. 加载配置文件
        config_manager = SmartConfigManager()
        if config_path:
            config_files = [config_path]

        if not config_manager.load_config(config_files[0]):
            raise ValueError("无法加载配置文件")

        migration_result = {
            'connections_created': 0,
            'models_created': 0,
            'table_rules_created': 0,
            'config_file': config_files[0]
        }

        try:
            # 2. 迁移数据库连接配置
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 迁移源连接
            source_conn = self.sqlalchemy_service.get_connection_by_name(db1_config.get('ip', ''))
            if source_conn:
                source_conn_id = source_conn.id
            else:
                source_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db1_config.get('ip', ''),
                    type=db1_config.get('type', 'DB2'),
                    host=db1_config.get('ip', ''),
                    port=int(db1_config.get('port', 50000)),
                    username=db1_config.get('user_name', ''),
                    password=db1_config.get('password', ''),
                    database=db1_config.get('schema', '')
                )
                migration_result['connections_created'] += 1

            # 迁移目标连接
            target_conn = self.sqlalchemy_service.get_connection_by_name(db2_config.get('ip', ''))
            if target_conn:
                target_conn_id = target_conn.id
            else:
                target_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db2_config.get('ip', ''),
                    type=db2_config.get('type', 'DB2'),
                    host=db2_config.get('ip', ''),
                    port=int(db2_config.get('port', 50000)),
                    username=db2_config.get('user_name', ''),
                    password=db2_config.get('password', ''),
                    database=db2_config.get('schema', '')
                )
                migration_result['connections_created'] += 1

            # 3. 创建比对模型
            common_config = dict(config_manager.config['COMMON'])
            model_id = self._create_model_from_config(user_name, source_conn_id, target_conn_id, common_config)
            migration_result['models_created'] += 1

            # 4. 迁移表规则
            if config_manager.rules is not None:
                table_rules_count = self._migrate_table_rules(model_id, config_manager.rules)
                migration_result['table_rules_created'] = table_rules_count

            logger.info(f"配置迁移完成: {migration_result}")
            return migration_result

        except Exception as e:
            logger.error(f"配置迁移失败: {e}")
            raise

    def _create_model_from_config(self, user_name: str, source_conn_id: int, target_conn_id: int, common_config: Dict[str, str]) -> int:
        """从配置创建比对模型"""
        try:
            # 使用TAB_RULE配置的文件名称作为模型名称
            tab_rule_path = common_config.get('tab_rule', common_config.get('TAB_RULE', '从配置文件迁移的模型'))

            # 从路径中提取文件名
            if tab_rule_path != '从配置文件迁移的模型':
                file_name = os.path.basename(tab_rule_path)
                model_name = os.path.splitext(file_name)[0]
            else:
                model_name = tab_rule_path

            # 检查是否已存在相同名称的模型
            existing_model = self.sqlalchemy_service.get_comparison_model_by_name(model_name)
            if existing_model:
                logger.info(f"比对模型已存在，使用现有模型: {model_name}, ID: {existing_model.id}")
                return existing_model.id

            description = f"从配置文件迁移的比对模型，用户: {user_name}"

            # 处理比对类型
            cmp_type_raw = common_config.get('cmp_type', common_config.get('CMP_TYPE', '2'))
            cmp_type = self._convert_cmp_type(cmp_type_raw)

            # 全局配置默认设为空
            global_config = {}

            # 创建比对模型
            model_id = self.sqlalchemy_service.create_comparison_model(
                name=model_name,
                description=description,
                source_connid=source_conn_id,
                target_connid=target_conn_id,
                cmp_type=cmp_type,
                global_config=global_config
            )

            logger.info(f"从配置创建比对模型成功: {model_name}, ID: {model_id}")
            return model_id

        except Exception as e:
            logger.error(f"从配置创建比对模型失败: {e}", exc_info=True)
            raise

    def _convert_cmp_type(self, cmp_type_raw: str) -> str:
        """转换比对类型"""
        # 将配置文件中的数字类型转换为字符串类型
        type_mapping = {
            '1': 'stream_merge',      # 流式归并算法
            '2': 'memory_dict',       # 内存字典算法
            'stream_merge': 'stream_merge',
            'memory_dict': 'memory_dict',
            'content': 'content'
        }

        return type_mapping.get(str(cmp_type_raw).lower(), 'content')

    def _migrate_table_rules(self, model_id: int, rules_xml) -> int:
        """迁移表规则到数据库"""
        count = 0

        try:
            for table_elem in rules_xml.findall('table'):
                try:
                    # 提取表规则信息
                    table_id = table_elem.get('id')  # 可能为None，让create_table_rule自动生成
                    table_name = table_elem.get('name', table_elem.get('table_id', table_id))
                    remark = table_elem.get('remark', '')

                    # 提取SQL语句
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        sql_1 = sql1_elem.text.strip() if sql1_elem.text else ""
                        sql_2 = sql2_elem.text.strip() if sql2_elem.text else ""

                        if not sql_1 or not sql_2:
                            logger.warning(f"跳过空SQL的表规则: {table_id}")
                            continue

                        # 提取其他配置信息
                        primary_keys = []
                        ignore_fields = []
                        field_mappings = {}

                        # 解析主键配置
                        pk_elem = table_elem.find('primary_keys')
                        if pk_elem is not None and pk_elem.text:
                            primary_keys = [key.strip() for key in pk_elem.text.split(',') if key.strip()]

                        # 解析忽略字段配置
                        ignore_elem = table_elem.find('ignore_fields')
                        if ignore_elem is not None and ignore_elem.text:
                            ignore_fields = [field.strip() for field in ignore_elem.text.split(',') if field.strip()]

                        # 解析字段映射配置
                        mapping_elem = table_elem.find('field_mappings')
                        if mapping_elem is not None:
                            for mapping in mapping_elem.findall('mapping'):
                                source_field = mapping.get('source')
                                target_field = mapping.get('target')
                                if source_field and target_field:
                                    field_mappings[source_field] = target_field

                        # 创建表规则
                        rule_id = self.sqlalchemy_service.create_table_rule(
                            model_id=model_id,
                            table_id=table_id,
                            table_name=table_name,
                            sql_1=sql_1,
                            sql_2=sql_2,
                            remark=remark,
                            primary_keys=primary_keys,
                            ignore_fields=ignore_fields,
                            field_mappings=field_mappings
                        )

                        logger.info(f"迁移表规则成功: {table_name} (ID: {table_id}) -> 规则ID: {rule_id}")
                        count += 1

                    else:
                        logger.warning(f"跳过缺少SQL的表规则: {table_id}")

                except Exception as e:
                    logger.error(f"迁移单个表规则失败: {table_elem.get('id', 'unknown')}, 错误: {e}")
                    continue

            logger.info(f"表规则迁移完成，成功处理 {count} 个规则")
            return count

        except Exception as e:
            logger.error(f"表规则迁移失败: {e}", exc_info=True)
            raise


def main():
    """
    主函数 - v4.0架构的统一入口点

    支持多种运行模式：
    1. 兼容模式：从配置文件创建并执行任务
    2. 任务模式：执行指定的任务ID
    3. 交互模式：提供命令行交互界面
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='SQLCompare v4.0 - 数据库比对工具')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--task-id', '-t', help='执行指定的任务ID')
    parser.add_argument('--user-name', '-u', default='system', help='用户名（默认：system）')
    parser.add_argument('--db-url', '-d', default='sqlite:///sqlcompare_v4.db', help='数据库连接URL')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')

    args = parser.parse_args()

    try:
        # 初始化任务编排器
        orchestrator = ComparisonTaskOrchestrator(args.db_url)

        if args.task_id:
            # 任务模式
            logger.info(f"指定任务模式: {args.task_id}")

            def progress_callback(message):
                logger.info(f"进度: {message}")

            result = orchestrator.execute_task(args.task_id, progress_callback)
            logger.info(f"任务执行完成，统计信息: {result}")

        elif args.interactive:
            # 交互模式
            logger.info("交互任务模式：提供命令行交互界面")
            run_interactive_mode(orchestrator, args.user_name)

        else:
            # 兼容模式
            logger.info("兼容任务模式：从配置文件创建并执行任务")

            task_id = orchestrator.create_task_from_config(args.config, args.user_name)
            logger.info(f"任务创建成功: {task_id}")

            def progress_callback(message):
                logger.info(f"进度: {message}")

            result = orchestrator.execute_task(task_id, progress_callback)
            logger.info(f"任务执行完成，统计信息: {result}")

    except Exception as e:
        logger.error(f"程序执行失败: {e}", exc_info=True)
        sys.exit(1)


def run_interactive_mode(orchestrator: ComparisonTaskOrchestrator, user_name: str):
    """交互模式 - 提供命令行交互界面"""
    logger.info("进入交互模式，输入 'help' 查看可用命令")

    while True:
        try:
            command = input("\nsqlcompare> ").strip().lower()

            if command == 'help':
                print("""
可用命令：
  create-config [配置文件路径]  - 从配置文件创建任务
  migrate-config [配置文件路径] - 将配置文件迁移到数据库
  execute <任务ID>            - 执行指定任务
  list-tasks                  - 列出所有任务
  task-info <任务ID>          - 查看任务详情
  quit/exit                   - 退出程序
                """)

            elif command.startswith('create-config'):
                parts = command.split()
                config_path = parts[1] if len(parts) > 1 else None
                task_id = orchestrator.create_task_from_config(config_path, user_name)
                print(f"任务创建成功: {task_id}")

            elif command.startswith('migrate-config'):
                parts = command.split()
                config_path = parts[1] if len(parts) > 1 else None
                result = orchestrator.migrate_config_to_database(config_path, user_name)
                print(f"配置迁移完成: {result}")

            elif command.startswith('execute'):
                parts = command.split()
                if len(parts) < 2:
                    print("错误：请指定任务ID")
                    continue

                task_id = parts[1]
                def progress_callback(message):
                    print(f"进度: {message}")

                result = orchestrator.execute_task(task_id, progress_callback)
                print(f"任务执行完成，统计信息: {result}")

            elif command == 'list-tasks':
                tasks = orchestrator.sqlalchemy_service.get_user_tasks(user_name)
                if tasks:
                    print("\n任务列表：")
                    for task in tasks:
                        print(f"  {task.task_id}: {task.task_name} ({task.status})")
                else:
                    print("没有找到任务")

            elif command.startswith('task-info'):
                parts = command.split()
                if len(parts) < 2:
                    print("错误：请指定任务ID")
                    continue

                task_id = parts[1]
                task_info = orchestrator.sqlalchemy_service.get_task_summary(task_id)
                if task_info:
                    print(f"\n任务信息：")
                    for key, value in task_info.items():
                        print(f"  {key}: {value}")
                else:
                    print(f"任务 {task_id} 不存在")

            elif command in ['quit', 'exit']:
                print("退出程序")
                break

            else:
                print(f"未知命令: {command}，输入 'help' 查看可用命令")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"命令执行失败: {e}")


if __name__ == '__main__':
    main()
