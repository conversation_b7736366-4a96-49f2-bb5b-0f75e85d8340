from django.urls import path, include, re_path
from django.views.decorators.csrf import csrf_exempt
from rest_framework.routers import DefaultRouter


class OptionalSlashRouter(DefaultRouter):
    """支持可选斜杠的路由器"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 禁用尾部斜杠要求，让URL同时支持带斜杠和不带斜杠
        self.trailing_slash = '/?'


from .views import (
    CompareModelViewSet,
    CompareTaskViewSet,
    CompareDiffViewSet,
    DataConnectionViewSet,
    task_error_report,
    task_status_update,
    system_error_report,
    task_cleanup_status,
    manual_task_cleanup,
    get_comparison_result,
    get_differences,
    get_source_only_records,
    get_target_only_records,
    sync_comparison_results
)

router = OptionalSlashRouter()
router.register(r'model', CompareModelViewSet)
router.register(r'task', CompareTaskViewSet)
router.register(r'diff', CompareDiffViewSet)
router.register(r'connection', DataConnectionViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # DB-Agent通知API端点
    path('tasks/<str:task_id>/error', task_error_report, name='task-error-report'),
    path('tasks/<str:task_id>/status/update', task_status_update, name='task-status-update'),
    path('system/error', system_error_report, name='system-error-report'),
    # 任务清理API端点
    path('system/cleanup/status', task_cleanup_status, name='task-cleanup-status'),
    path('system/cleanup/manual', manual_task_cleanup, name='manual-task-cleanup'),
    # 比对结果API端点
    path('task/<str:task_id>/result/', get_comparison_result, name='comparison-result'),
    path('task/<str:task_id>/differences/', get_differences, name='differences'),
    path('task/<str:task_id>/source-only/', get_source_only_records, name='source-only'),
    path('task/<str:task_id>/target-only/', get_target_only_records, name='target-only'),
    path('comparison/sync/', sync_comparison_results, name='sync-results'),
]
