// 数据库比对模块状态管理
import { defineStore } from 'pinia';
import {
  startCompareTask,
  controlCompareTask
} from '@/api/database/index';
import {
  createCompareTask,
  getTaskStatus,
  executeDirectComparison,
  preCheckCompareTask
} from '@/api/database/compare';
import { getCompareDiffList } from '@/api/database/differences';
import type { CompareTask, CompareTaskRequest, StartTaskParams } from '@/types/database';
import type { BaseApiResponse } from '@/api/database/types';
import { ElMessage, ElNotification } from 'element-plus';

// 扩展CompareDiffQuery类型
interface ExtendedCompareDiffQuery {
  page?: number;
  size?: number;
  sort?: string;
  taskId?: string;
  tableName?: string;
  diffType?: string;
  keyword?: string;
  severity?: string;
}

export const useDatabaseStore = defineStore('database', {
  state: () => ({
    // 当前任务状态
    currentTask: null as CompareTask | null,
    taskStatus: 'waiting', // waiting, running, paused, success, failed, stopped
    progress: 0,
    elapsedTime: 0,
    remainingTime: 0,
    totalRecords: 0,
    processedRecords: 0,
    matchedRecords: 0,
    differentRecords: 0,
    errorRecords: 0,
    // 进度详情
    tableProgress: [] as any[],
    processingSpeed: 0, // 处理速度（记录/秒）
    speedHistory: [] as number[], // 保存历史处理速度用于图表
    timeHistory: [] as string[], // 对应的时间点
    // 系统资源使用
    resources: {
      cpu: 0,
      memory: 0,
      diskIO: 0,
      networkIO: 0
    },
    resourceHistory: {
      cpu: [] as number[],
      memory: [] as number[],
      time: [] as string[]
    },
    // 最近的差异记录（用于实时预览）
    recentDiffs: [] as any[],
    // 执行日志
    executionLogs: [] as Array<{level: string, message: string, timestamp: string}>,
    // 轮询控制
    polling: null as number | null,
    pollingInterval: 2000, // 缩短为2秒，提高实时性
    webSocket: null as WebSocket | null,
    // 执行表单状态
    executeFormState: null as any | null,
    // 是否处于分享模式（从外部链接访问）
    isSharedView: false,
  }),

  getters: {
    // 获取任务完成率
    completionRate: (state) => {
      if (state.totalRecords <= 0) return 0;
      return Math.min(100, Math.round((state.processedRecords / state.totalRecords) * 100));
    },

    // 获取差异率
    differenceRate: (state) => {
      if (state.processedRecords <= 0) return 0;
      return ((state.differentRecords / state.processedRecords) * 100).toFixed(2);
    },

    // 格式化的耗时
    formattedElapsedTime: (state) => {
      const hours = Math.floor(state.elapsedTime / 3600);
      const minutes = Math.floor((state.elapsedTime % 3600) / 60);
      const seconds = state.elapsedTime % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },

    // 格式化的剩余时间
    formattedRemainingTime: (state) => {
      if (state.remainingTime <= 0) return '00:00:00';
      const hours = Math.floor(state.remainingTime / 3600);
      const minutes = Math.floor((state.remainingTime % 3600) / 60);
      const seconds = state.remainingTime % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },

    // 任务是否正在运行
    isRunning: (state) => {
      return state.taskStatus === 'running';
    },

    // 任务是否已暂停
    isPaused: (state) => {
      return state.taskStatus === 'paused';
    },

    // 任务是否已完成（成功或失败）
    isCompleted: (state) => {
      return ['success', 'failed', 'stopped'].includes(state.taskStatus);
    },

    // 任务状态文本
    statusText: (state) => {
      const statusMap = {
        'waiting': '等待中',
        'running': '运行中',
        'paused': '已暂停',
        'success': '已完成',
        'failed': '失败',
        'stopped': '已停止'
      };
      return statusMap[state.taskStatus] || state.taskStatus;
    }
  },

  actions: {
    // 根据任务ID从服务器获取任务状态
    async restoreTask(taskId: string, isShared = false) {
      if (!taskId) return;

      try {
        // 标记是否是通过分享链接访问
        this.isSharedView = isShared;

        // 从服务器获取任务状态
        const response = await getTaskStatus(taskId);

        if (response && response.data) {
          this.currentTask = response.data;
          this.taskStatus = response.data.status;
          this.progress = response.data.progress || 0;
          this.totalRecords = response.data.totalRecords || 0;
          this.processedRecords = response.data.processedRecords || 0;
          this.matchedRecords = response.data.matchedRecords || 0;
          this.differentRecords = response.data.differentRecords || 0;
          this.errorRecords = response.data.errorRecords || 0;

          if (response.details) {
            this.tableProgress = response.details.tableProgress || [];
            this.remainingTime = response.details.remainingTime || 0;
            this.processingSpeed = response.details.currentSpeed || 0;

            // 更新执行日志
            if (response.details.logs && response.details.logs.length > 0) {
              this.addLogs(response.details.logs);
            }

            // 更新系统资源使用情况
            if (response.details.resources) {
              this.resources = response.details.resources;
              this.updateResourceHistory();
            }
          }

          // 计算已运行时间
          if (response.data.startTime) {
            const startTime = new Date(response.data.startTime).getTime();
            const now = Date.now();
            this.elapsedTime = Math.floor((now - startTime) / 1000);
          }

          // 如果任务正在运行，启动状态更新
          if (['running', 'waiting', 'paused'].includes(response.data.status)) {
            this.startRealTimeMonitoring();
          }

          // 获取最近的差异数据用于预览
          this.fetchRecentDiffs();

          return response.data;
        } else {
          ElMessage.warning('无法获取任务状态，任务可能已结束');
        }
      } catch (error: any) {
        console.error('获取任务状态失败:', error);
        ElMessage.error(`获取任务状态失败: ${error.message || '未知错误'}`);
      }
    },

    // 保存表单状态
    saveFormState(formState: any) {
      this.executeFormState = formState;
      // 保存到localStorage，以便在页面刷新后恢复
      localStorage.setItem('database_execute_form', JSON.stringify(formState));
    },

    // 加载表单状态
    async loadFormState() {
      if (!this.executeFormState) {
        const savedState = localStorage.getItem('database_execute_form');
        if (savedState) {
          try {
            this.executeFormState = JSON.parse(savedState);
          } catch (e) {
            console.error('解析保存的表单状态失败', e);
          }
        }
      }
      return this.executeFormState;
    },

    // 清除表单状态
    clearFormState() {
      this.executeFormState = null;
      localStorage.removeItem('database_execute_form');
    },

    // 任务预检查
    async preCheckTask(params: CompareTaskRequest) {
      try {
        const response = await preCheckCompareTask(params);
        return response;
      } catch (error: any) {
        console.error('预检查失败:', error);
        ElMessage.error(`预检查失败: ${error.message || '未知错误'}`);
        throw error;
      }
    },

    // 任务预检查（使用统一客户端）
    async preCheckTaskUnified(params: CompareTaskRequest): Promise<BaseApiResponse<{valid: boolean; messages: string[]}>> {
      try {
        const response = await preCheckCompareTask(params);
        return response;
      } catch (error: any) {
        console.error('预检查失败:', error);
        throw error; // 统一客户端已经处理了错误消息显示
      }
    },

    // 启动任务
    async startTask(params: CompareTaskRequest) {
      try {
        // 构造符合StartTaskParams的参数
        const taskParams: StartTaskParams = {
          model: params.model,
          name: params.name || `task-${new Date().toLocaleString()}`,
          limit: params.limit || 0, // 默认不限制
          options: params.options || {}
        };

        const { data } = await startCompareTask(taskParams);
        if (data && data.id) {
          this.currentTask = data;
          this.taskStatus = data.status;

          // 启动实时监控
          this.startRealTimeMonitoring();

          // 显示通知
          ElNotification({
            title: '任务已启动',
            message: `比对任务「${data.name || data.id}」已开始执行`,
            type: 'success',
            duration: 3000
          });

          return data;
        } else {
          throw new Error('启动任务失败: 返回结果不包含任务信息');
        }
      } catch (error: any) {
        console.error('启动任务失败:', error);
        ElMessage.error(`启动任务失败: ${error.message || '未知错误'}`);
        throw error;
      }
    },

    // 启动任务（使用统一客户端）
    async startTaskUnified(params: CompareTaskRequest): Promise<BaseApiResponse<CompareTask>> {
      try {
        const response = await createCompareTask(params);

        if (response.success && response.data) {
          // 更新本地状态
          this.currentTask = {
            id: response.data.id || '',
            status: response.data.status || 'pending',
            name: params.name || `task-${new Date().toLocaleString()}`,
            startTime: response.data.createTime || new Date().toISOString(),
            progress: 0
          } as CompareTask;

          this.taskStatus = response.data.status || 'pending';

          // 启动实时监控
          this.startRealTimeMonitoring();

          return response;
        } else {
          throw new Error('启动任务失败: 返回结果不包含任务信息');
        }
      } catch (error: any) {
        console.error('启动任务失败:', error);
        throw error; // 统一客户端已经处理了错误消息显示
      }
    },

    // 执行直接比对（使用统一客户端）
    async executeDirectComparisonUnified(params: CompareTaskRequest): Promise<BaseApiResponse<any>> {
      try {
        const response = await executeDirectComparison(params);

        if (response.success) {
          // 更新本地状态以显示结果
          this.taskStatus = 'success';
          this.progress = 100;

          if (response.summary) {
            this.totalRecords = response.summary.source_count || 0;
            this.processedRecords = response.summary.source_count || 0;
            this.matchedRecords = response.summary.matched_count || 0;
            this.differentRecords = response.summary.diff_count || 0;
          }
        }

        return response;
      } catch (error: any) {
        console.error('直接比对失败:', error);
        throw error; // 统一客户端已经处理了错误消息显示
      }
    },

    // 暂停任务
    async pauseTask() {
      if (!this.currentTask?.id) return;

      try {
        const response = await controlCompareTask(this.currentTask.id, 'pause');
        if (response && response.success) {
          this.taskStatus = 'paused';

          // 显示通知
          ElNotification({
            title: '任务已暂停',
            message: '比对任务已暂停，可随时恢复',
            type: 'warning',
            duration: 3000
          });

          return true;
        }
      } catch (error: any) {
        console.error('暂停任务失败:', error);
        ElMessage.error(`暂停任务失败: ${error.message || '未知错误'}`);
        throw error;
      }
    },

    // 恢复任务
    async resumeTask() {
      if (!this.currentTask?.id) return;

      try {
        const response = await controlCompareTask(this.currentTask.id, 'resume');
        if (response && response.success) {
          this.taskStatus = 'running';

          // 显示通知
          ElNotification({
            title: '任务已恢复',
            message: '比对任务已恢复执行',
            type: 'success',
            duration: 3000
          });

          return true;
        }
      } catch (error: any) {
        console.error('恢复任务失败:', error);
        ElMessage.error(`恢复任务失败: ${error.message || '未知错误'}`);
        throw error;
      }
    },

    // 停止任务
    async stopTask() {
      if (!this.currentTask?.id) return;

      try {
        const response = await controlCompareTask(this.currentTask.id, 'stop');
        if (response && response.success) {
          this.taskStatus = 'stopped';
          this.stopRealTimeMonitoring();

          // 显示通知
          ElNotification({
            title: '任务已停止',
            message: '比对任务已手动停止',
            type: 'warning',
            duration: 3000
          });

          return true;
        }
      } catch (error: any) {
        console.error('停止任务失败:', error);
        ElMessage.error(`停止任务失败: ${error.message || '未知错误'}`);
        throw error;
      }
    },

    // 获取最近的差异数据用于预览
    async fetchRecentDiffs() {
      if (!this.currentTask?.id) return;

      try {
        const params: ExtendedCompareDiffQuery = {
          page: 1,
          size: 5,
          // 按时间倒序，获取最新的差异
          sort: 'createTime,desc'
        };

        const response = await getCompareDiffList(this.currentTask.id, params);

        if (response && response.data) {
          // 将新的差异记录添加到队列前面
          const newDiffs = response.data.filter(diff =>
            !this.recentDiffs.some(existing => existing.id === diff.id)
          );

          if (newDiffs.length > 0) {
            // 合并差异并保持最多10条
            this.recentDiffs = [...newDiffs, ...this.recentDiffs].slice(0, 10);
          }
        }
      } catch (error) {
        console.error('获取差异预览失败:', error);
      }
    },

    // 添加执行日志
    addLogs(logs: Array<{level: string, message: string, timestamp: string}>) {
      // 合并日志并保留最近的1000条
      this.executionLogs = [...this.executionLogs, ...logs]
        .slice(-1000);
    },

    // 更新资源使用历史
    updateResourceHistory() {
      const now = new Date();
      const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

      // 更新处理速度历史
      this.speedHistory.push(this.processingSpeed);
      this.timeHistory.push(timeStr);

      // 保持最多30个数据点
      if (this.speedHistory.length > 30) {
        this.speedHistory.shift();
        this.timeHistory.shift();
      }

      // 更新资源使用历史
      this.resourceHistory.cpu.push(this.resources.cpu);
      this.resourceHistory.memory.push(this.resources.memory);
      this.resourceHistory.time.push(timeStr);

      // 保持最多30个数据点
      if (this.resourceHistory.cpu.length > 30) {
        this.resourceHistory.cpu.shift();
        this.resourceHistory.memory.shift();
        this.resourceHistory.time.shift();
      }
    },

    // 更新任务状态
    async updateTaskStatus() {
      if (!this.currentTask?.id) return;

      try {
        const response = await getTaskStatus(this.currentTask.id);

        if (response && response.data) {
          this.currentTask = response.data;
          this.taskStatus = response.data.status;
          this.progress = response.data.progress || 0;
          this.totalRecords = response.data.totalRecords || 0;
          this.processedRecords = response.data.processedRecords || 0;
          this.matchedRecords = response.data.matchedRecords || 0;
          this.differentRecords = response.data.differentRecords || 0;
          this.errorRecords = response.data.errorRecords || 0;

          if (response.details) {
            this.tableProgress = response.details.tableProgress || [];
            this.remainingTime = response.details.remainingTime || 0;
            this.processingSpeed = response.details.currentSpeed || 0;

            // 更新执行日志
            if (response.details.logs && response.details.logs.length > 0) {
              this.addLogs(response.details.logs);
            }

            // 更新系统资源使用情况
            if (response.details.resources) {
              this.resources = response.details.resources;
              this.updateResourceHistory();
            }
          }

          // 计算已运行时间
          if (response.data.startTime) {
            const startTime = new Date(response.data.startTime).getTime();
            const now = Date.now();
            this.elapsedTime = Math.floor((now - startTime) / 1000);
          }

          // 如果任务已完成，获取差异并停止轮询
          if (['success', 'failed', 'stopped'].includes(response.data.status)) {
            this.fetchRecentDiffs();
            this.stopRealTimeMonitoring();

            // 显示完成通知
            if (response.data.status === 'success') {
              ElNotification({
                title: '任务完成',
                message: `比对任务已完成，共发现 ${this.differentRecords} 条差异`,
                type: 'success',
                duration: 0
              });
            } else if (response.data.status === 'failed') {
              ElNotification({
                title: '任务失败',
                message: response.data.message || '比对任务执行失败',
                type: 'error',
                duration: 0
              });
            }
          } else {
            // 更新预览的差异数据
            this.fetchRecentDiffs();
          }

          return response.data;
        }
      } catch (error: any) {
        console.error('获取任务状态失败:', error);
        // 出错时不显示错误通知，避免频繁弹窗
        console.error(`获取任务状态失败: ${error.message || '未知错误'}`);

        // 如果连续失败多次，停止轮询
        this.stopRealTimeMonitoring();
      }
    },

    // 启动实时监控（结合WebSocket和轮询）
    startRealTimeMonitoring() {
      // 停止已有的监控
      this.stopRealTimeMonitoring();

      // 尝试建立WebSocket连接
      this.connectWebSocket();

      // 同时启动轮询作为备份机制
      this.startPolling();
    },

    // 建立WebSocket连接
    connectWebSocket() {
      if (!this.currentTask?.id) return;

      try {
        // 确保之前的连接已关闭
        this.closeWebSocket();

        // 获取当前协议
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const wsUrl = `${protocol}//${host}/api/database/ws/task/${this.currentTask.id}`;

        this.webSocket = new WebSocket(wsUrl);

        this.webSocket.onopen = () => {
          console.log('WebSocket 连接已建立');
        };

        this.webSocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            // 处理不同类型的消息
            if (data.type === 'status') {
              // 状态更新
              Object.assign(this, data.data);

              // 更新图表数据
              this.updateResourceHistory();
            } else if (data.type === 'log') {
              // 日志消息
              this.addLogs([data.data]);
            } else if (data.type === 'diff') {
              // 新的差异记录
              if (!this.recentDiffs.some(diff => diff.id === data.data.id)) {
                this.recentDiffs = [data.data, ...this.recentDiffs].slice(0, 10);
              }
            }
          } catch (error) {
            console.error('处理WebSocket消息出错:', error);
          }
        };

        this.webSocket.onclose = () => {
          console.log('WebSocket 连接已关闭');
          this.webSocket = null;

          // 如果任务仍在运行，尝试重新连接
          if (['running', 'waiting', 'paused'].includes(this.taskStatus)) {
            setTimeout(() => this.connectWebSocket(), 5000);
          }
        };

        this.webSocket.onerror = (error) => {
          console.error('WebSocket 错误:', error);
          // WebSocket连接失败，依赖轮询机制
        };
      } catch (error) {
        console.error('建立WebSocket连接失败:', error);
      }
    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.webSocket) {
        try {
          this.webSocket.close();
        } catch (e) {
          console.error('关闭WebSocket连接出错:', e);
        }
        this.webSocket = null;
      }
    },

    // 开始轮询
    startPolling() {
      this.stopPolling(); // 先停止现有的轮询

      this.polling = window.setInterval(() => {
        // 只有在WebSocket不可用或者最近未更新状态时才使用轮询
        if (!this.webSocket || this.webSocket.readyState !== WebSocket.OPEN) {
          this.updateTaskStatus();
        }
      }, this.pollingInterval);
    },

    // 停止轮询
    stopPolling() {
      if (this.polling) {
        window.clearInterval(this.polling);
        this.polling = null;
      }
    },

    // 停止所有实时监控
    stopRealTimeMonitoring() {
      this.stopPolling();
      this.closeWebSocket();
    },

    // 重置任务状态
    resetTaskState() {
      this.stopRealTimeMonitoring();
      this.currentTask = null;
      this.taskStatus = 'waiting';
      this.progress = 0;
      this.elapsedTime = 0;
      this.remainingTime = 0;
      this.totalRecords = 0;
      this.processedRecords = 0;
      this.matchedRecords = 0;
      this.differentRecords = 0;
      this.errorRecords = 0;
      this.tableProgress = [];
      this.processingSpeed = 0;
      this.speedHistory = [];
      this.timeHistory = [];
      this.resources = {
        cpu: 0,
        memory: 0,
        diskIO: 0,
        networkIO: 0
      };
      this.resourceHistory = {
        cpu: [],
        memory: [],
        time: []
      };
      this.recentDiffs = [];
      this.executionLogs = [];
      this.isSharedView = false;
    }
  }
});