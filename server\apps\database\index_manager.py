"""
索引管理器模块

负责管理比对结果的多维度索引，包括：
- KEY索引：支持快速KEY查找
- 类型索引：支持按差异类型过滤
- 字段索引：支持按字段名过滤
- 时间索引：支持时间范围查询
- 增量索引更新机制
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
from datetime import datetime
import logging
import hashlib

from django.conf import settings

logger = logging.getLogger(__name__)

class IndexManager:
    """索引管理器"""
    
    def __init__(self):
        """初始化索引管理器"""
        self.comparison_results_path = Path(getattr(settings, 'COMPARISON_RESULTS_PATH', 'comparison_results'))
        self.index_cache = {}  # 内存中的索引缓存
        self.cache_ttl = 600  # 索引缓存10分钟
        
    async def build_indexes(self, task_id: str) -> Dict[str, Any]:
        """
        构建任务的所有索引
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 构建的索引信息
        """
        try:
            logger.info(f"开始构建索引 - 任务ID: {task_id}")
            start_time = datetime.now()
            
            workspace = self.comparison_results_path / task_id
            data_dir = workspace / 'data'
            indexes_dir = workspace / 'indexes'
            
            if not data_dir.exists():
                logger.warning(f"数据目录不存在: {data_dir}")
                return {}
            
            # 确保索引目录存在
            indexes_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化索引结构
            indexes = {
                'key_index': {},      # KEY值 -> 记录位置信息
                'type_index': {},     # 差异类型 -> KEY列表
                'field_index': {},    # 字段名 -> KEY列表
                'time_index': {},     # 时间戳 -> KEY列表
                'metadata': {
                    'build_time': start_time.isoformat(),
                    'total_records': 0,
                    'files_processed': [],
                    'version': '1.0'
                }
            }
            
            # 扫描所有数据文件
            total_records = 0
            for file_path in data_dir.glob('*.jsonl'):
                diff_type = file_path.stem
                logger.debug(f"处理文件: {file_path}")
                
                file_records = await self._process_file_for_index(
                    file_path, diff_type, indexes
                )
                total_records += file_records
                indexes['metadata']['files_processed'].append({
                    'file': diff_type,
                    'records': file_records,
                    'path': str(file_path)
                })
            
            indexes['metadata']['total_records'] = total_records
            
            # 保存索引文件
            await self._save_indexes(task_id, indexes)
            
            # 更新缓存
            self._update_index_cache(task_id, indexes)
            
            build_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"索引构建完成 - 任务ID: {task_id}, 耗时: {build_time:.2f}秒, 记录数: {total_records}")
            
            return indexes
            
        except Exception as e:
            logger.error(f"构建索引失败 - 任务ID: {task_id}, 错误: {str(e)}")
            raise
    
    async def _process_file_for_index(self, file_path: Path, diff_type: str, 
                                     indexes: Dict[str, Any]) -> int:
        """处理单个文件构建索引"""
        record_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_no, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    try:
                        record = json.loads(line)
                        key = record.get('key', '')
                        
                        if not key:
                            logger.warning(f"记录缺少key字段: {file_path}:{line_no}")
                            continue
                        
                        # 构建KEY索引
                        indexes['key_index'][key] = {
                            'file': diff_type,
                            'line': line_no,
                            'type': diff_type,
                            'timestamp': record.get('_timestamp', ''),
                            'size': len(line)
                        }
                        
                        # 构建类型索引
                        if diff_type not in indexes['type_index']:
                            indexes['type_index'][diff_type] = []
                        indexes['type_index'][diff_type].append(key)
                        
                        # 构建字段索引
                        await self._build_field_index(record, key, indexes)
                        
                        # 构建时间索引
                        await self._build_time_index(record, key, indexes)
                        
                        record_count += 1
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败: {file_path}:{line_no}, 错误: {str(e)}")
                        continue
                        
        except Exception as e:
            logger.error(f"处理文件失败: {file_path}, 错误: {str(e)}")
            raise
        
        return record_count
    
    async def _build_field_index(self, record: Dict[str, Any], key: str, 
                                indexes: Dict[str, Any]):
        """构建字段索引"""
        try:
            # 处理差异字段
            diff_fields = record.get('diff_fields', [])
            for field in diff_fields:
                if field not in indexes['field_index']:
                    indexes['field_index'][field] = []
                if key not in indexes['field_index'][field]:
                    indexes['field_index'][field].append(key)
            
            # 处理记录中的所有字段
            for field_name in record.keys():
                if field_name.startswith('_'):  # 跳过内部字段
                    continue
                    
                if field_name not in indexes['field_index']:
                    indexes['field_index'][field_name] = []
                if key not in indexes['field_index'][field_name]:
                    indexes['field_index'][field_name].append(key)
            
            # 处理嵌套记录中的字段
            for nested_field in ['source_record', 'target_record', 'record']:
                if nested_field in record and isinstance(record[nested_field], dict):
                    for field_name in record[nested_field].keys():
                        if field_name not in indexes['field_index']:
                            indexes['field_index'][field_name] = []
                        if key not in indexes['field_index'][field_name]:
                            indexes['field_index'][field_name].append(key)
                            
        except Exception as e:
            logger.warning(f"构建字段索引失败: {str(e)}")
    
    async def _build_time_index(self, record: Dict[str, Any], key: str, 
                               indexes: Dict[str, Any]):
        """构建时间索引"""
        try:
            timestamp = record.get('_timestamp', '')
            if timestamp:
                # 按小时分组时间索引
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    hour_key = dt.strftime('%Y-%m-%d-%H')
                    
                    if hour_key not in indexes['time_index']:
                        indexes['time_index'][hour_key] = []
                    indexes['time_index'][hour_key].append(key)
                    
                except ValueError:
                    logger.warning(f"无效的时间戳格式: {timestamp}")
                    
        except Exception as e:
            logger.warning(f"构建时间索引失败: {str(e)}")
    
    async def _save_indexes(self, task_id: str, indexes: Dict[str, Any]):
        """保存索引文件"""
        try:
            workspace = self.comparison_results_path / task_id
            indexes_dir = workspace / 'indexes'
            
            # 分别保存各类索引
            for index_name, index_data in indexes.items():
                if index_name == 'metadata':
                    continue
                    
                index_file = indexes_dir / f"{index_name}.json"
                with open(index_file, 'w', encoding='utf-8') as f:
                    json.dump(index_data, f, ensure_ascii=False, indent=2)
            
            # 保存元数据
            metadata_file = indexes_dir / 'metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(indexes['metadata'], f, ensure_ascii=False, indent=2)
                
            logger.debug(f"索引文件已保存 - 任务ID: {task_id}")
            
        except Exception as e:
            logger.error(f"保存索引文件失败: {str(e)}")
            raise
    
    async def load_indexes(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        加载任务索引
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 索引数据，如果不存在返回None
        """
        try:
            # 检查缓存
            cached_indexes = self._get_cached_indexes(task_id)
            if cached_indexes:
                return cached_indexes
            
            workspace = self.comparison_results_path / task_id
            indexes_dir = workspace / 'indexes'
            
            if not indexes_dir.exists():
                return None
            
            indexes = {}
            
            # 加载各类索引
            for index_type in ['key_index', 'type_index', 'field_index', 'time_index']:
                index_file = indexes_dir / f"{index_type}.json"
                if index_file.exists():
                    with open(index_file, 'r', encoding='utf-8') as f:
                        indexes[index_type] = json.load(f)
                else:
                    indexes[index_type] = {}
            
            # 加载元数据
            metadata_file = indexes_dir / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    indexes['metadata'] = json.load(f)
            else:
                indexes['metadata'] = {}
            
            # 更新缓存
            self._update_index_cache(task_id, indexes)
            
            logger.debug(f"索引已加载 - 任务ID: {task_id}")
            return indexes
            
        except Exception as e:
            logger.error(f"加载索引失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return None
    
    def _get_cached_indexes(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的索引"""
        try:
            cache_entry = self.index_cache.get(task_id)
            if cache_entry:
                cached_time, indexes = cache_entry
                # 检查缓存是否过期
                if (datetime.now() - cached_time).total_seconds() < self.cache_ttl:
                    logger.debug(f"索引缓存命中 - 任务ID: {task_id}")
                    return indexes
                else:
                    # 缓存过期，删除
                    del self.index_cache[task_id]
            
            return None
            
        except Exception as e:
            logger.warning(f"获取缓存索引失败: {str(e)}")
            return None
    
    def _update_index_cache(self, task_id: str, indexes: Dict[str, Any]):
        """更新索引缓存"""
        try:
            self.index_cache[task_id] = (datetime.now(), indexes)
            logger.debug(f"索引缓存已更新 - 任务ID: {task_id}")
        except Exception as e:
            logger.warning(f"更新索引缓存失败: {str(e)}")
    
    async def rebuild_indexes_if_needed(self, task_id: str) -> bool:
        """
        如果需要，重建索引
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否重建了索引
        """
        try:
            workspace = self.comparison_results_path / task_id
            data_dir = workspace / 'data'
            indexes_dir = workspace / 'indexes'
            
            if not data_dir.exists():
                return False
            
            # 检查索引是否存在
            if not indexes_dir.exists() or not (indexes_dir / 'key_index.json').exists():
                logger.info(f"索引不存在，开始构建 - 任务ID: {task_id}")
                await self.build_indexes(task_id)
                return True
            
            # 检查索引是否过期
            metadata_file = indexes_dir / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    
                # 获取数据文件的最新修改时间
                latest_data_time = max(
                    f.stat().st_mtime for f in data_dir.glob('*.jsonl')
                ) if list(data_dir.glob('*.jsonl')) else 0
                
                # 获取索引构建时间
                build_time_str = metadata.get('build_time', '')
                if build_time_str:
                    build_time = datetime.fromisoformat(build_time_str)
                    index_time = build_time.timestamp()
                    
                    # 如果数据文件比索引新，重建索引
                    if latest_data_time > index_time:
                        logger.info(f"数据文件已更新，重建索引 - 任务ID: {task_id}")
                        await self.build_indexes(task_id)
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查索引状态失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return False
    
    async def get_index_stats(self, task_id: str) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 索引统计信息
        """
        try:
            indexes = await self.load_indexes(task_id)
            if not indexes:
                return {'exists': False}
            
            stats = {
                'exists': True,
                'task_id': task_id,
                'metadata': indexes.get('metadata', {}),
                'index_counts': {
                    'keys': len(indexes.get('key_index', {})),
                    'types': len(indexes.get('type_index', {})),
                    'fields': len(indexes.get('field_index', {})),
                    'time_buckets': len(indexes.get('time_index', {}))
                },
                'type_distribution': {},
                'field_distribution': {}
            }
            
            # 统计类型分布
            type_index = indexes.get('type_index', {})
            for diff_type, keys in type_index.items():
                stats['type_distribution'][diff_type] = len(keys)
            
            # 统计字段分布（前10个最常见的字段）
            field_index = indexes.get('field_index', {})
            field_counts = [(field, len(keys)) for field, keys in field_index.items()]
            field_counts.sort(key=lambda x: x[1], reverse=True)
            stats['field_distribution'] = dict(field_counts[:10])
            
            return stats
            
        except Exception as e:
            logger.error(f"获取索引统计失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return {'exists': False, 'error': str(e)}
    
    async def clear_index_cache(self, task_id: Optional[str] = None):
        """
        清理索引缓存
        
        Args:
            task_id: 任务ID，如果为None则清理所有缓存
        """
        try:
            if task_id:
                if task_id in self.index_cache:
                    del self.index_cache[task_id]
                    logger.debug(f"索引缓存已清理 - 任务ID: {task_id}")
            else:
                self.index_cache.clear()
                logger.debug("所有索引缓存已清理")
                
        except Exception as e:
            logger.warning(f"清理索引缓存失败: {str(e)}")
    
    async def delete_indexes(self, task_id: str) -> bool:
        """
        删除任务索引
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功删除
        """
        try:
            workspace = self.comparison_results_path / task_id
            indexes_dir = workspace / 'indexes'
            
            if indexes_dir.exists():
                import shutil
                shutil.rmtree(indexes_dir)
                logger.info(f"索引已删除 - 任务ID: {task_id}")
            
            # 清理缓存
            await self.clear_index_cache(task_id)
            
            return True
            
        except Exception as e:
            logger.error(f"删除索引失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return False
