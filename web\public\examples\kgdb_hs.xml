<!--
   
-->
<rules>
<table table_id="1_orders" remark = "委托">
    <sql_1>  
        select    
		ORDER_ID||'-'||MARKET||'-'||BOARD||'-'||IS_WITHDRAW||'-'||TRD_DATE ||'-'|| CUST_CODE ||'-'|| SECU_INTL as key,
		CUST_CODE,ACC_CLS,BRANCH,EXT_INST,DCL_SECU_ACC,EXT_CLS,
		TRD_ID,SECU_INTL,SECU_CLS,ORDER_PRICE,ORDER_QTY,DCL_QTY,
		ORDER_AMT,ORDER_FRZ_AMT,IS_WITHDRAW,IS_WITHDRAWN,CAN_WITHDRAW,
		SEAT,DCL_TIMESLICE,DC<PERSON>_FLAG,VALID_FLAG,RET_MSG,MATCHED_QTY,WITHDRAWN_QTY,
		MATCHED_AMT, RLT_FRZ_AMT,RLT_FRZ_QTY,RLT_SETT_AMT,RLT_SETT_QTY,REMARK,EXT_REC_NUM,
		EXT_ORDER_ID,EXT_BIZ_NO,EXT_FRZ_AMT,EXT_SETT_AMT,EXT_ACC,EXT_SUB_ACC ,EXT_APP_SN,
		DCL_SEAT 
		from kgdb.orders_A
		where IS_WITHDRAW != '1'
    </sql_1>
    
    <sql_2>  
        select    
		ORDER_ID||'-'||MARKET||'-'||BOARD||'-'||IS_WITHDRAW||'-'||TRD_DATE ||'-'|| CUST_CODE ||'-'|| SECU_INTL as key,
		CUST_CODE,ACC_CLS,BRANCH,EXT_INST,DCL_SECU_ACC,EXT_CLS,
		TRD_ID,SECU_INTL,SECU_CLS,ORDER_PRICE,ORDER_QTY,DCL_QTY,
		ORDER_AMT,ORDER_FRZ_AMT,IS_WITHDRAW,IS_WITHDRAWN,CAN_WITHDRAW,
		SEAT,DCL_TIMESLICE,DCL_FLAG,VALID_FLAG,RET_MSG,MATCHED_QTY,WITHDRAWN_QTY,
		MATCHED_AMT, RLT_FRZ_AMT,RLT_FRZ_QTY,RLT_SETT_AMT,RLT_SETT_QTY,REMARK,EXT_REC_NUM,
		EXT_ORDER_ID,EXT_BIZ_NO,EXT_FRZ_AMT,EXT_SETT_AMT,EXT_ACC,EXT_SUB_ACC,EXT_APP_SN,
		DCL_SEAT
		from kgdb.orders
		where IS_WITHDRAW != '1'
    </sql_2>
    
</table>

<table table_id="2_shares" remark = "持仓">
    <sql_1>  
        select 
        ACCOUNT|| '-' ||SECU_ACC|| '-' ||SECU_INTL|| '-' ||SEAT|| '-' ||EXT_INST as KEY,
        CUST_CODE,SECU_ACC,SECU_INTL,BRANCH,EXT_INST,MARKET,BOARD,
        SEAT,SECU_CLS,CURRENCY,SHARE_BLN,SHARE_AVL,SHARE_TRD_FRZ,
        SHARE_OTD,SHARE_OTD_AVL,SHARE_FRZ,SHARE_UNTRADE_QTY,SHARE_DECL
        from kgdb.shares_A
    </sql_1>
    
    <sql_2>  
        select 
        ACCOUNT|| '-' ||SECU_ACC|| '-' ||SECU_INTL|| '-' ||SEAT|| '-' ||EXT_INST as KEY,
        CUST_CODE,SECU_ACC,SECU_INTL,BRANCH,EXT_INST,MARKET,BOARD,
        SEAT,SECU_CLS,CURRENCY,SHARE_BLN,SHARE_AVL,SHARE_TRD_FRZ,
        SHARE_OTD,SHARE_OTD_AVL,SHARE_FRZ,SHARE_UNTRADE_QTY,SHARE_DECL
        from kgdb.shares
    </sql_2>
    
</table>

<table table_id="3_capital" remark = "资金">
    <sql_1>  
        select 
        ACCOUNT|| '-' ||CURRENCY|| '-' ||FUND_ACCOUNT as KEY,
        USER_CODE,ACCOUNT,CURRENCY,BALANCE,AVAILABLE,FROZEN,
        TRD_FRZ,OUTSTANDING,OTD_AVL,CR_AMT,DR_AMT,OTD_DR,CR_BLN,
        CR_AVL,SAVING_ACCRUAL,LAST_INT_BLN,CAL_INT_DATE,SAVING_INT,
        DR_INT,CASH_ACCRUAL,CHQ_ACCRUAL,MKT_VAL,ASSETS_FLOOR,RELATED_ACC_FLAG,
        STATUS,CERTIFIED_AMT,SAVING_INT_TAX
        from kgdb.CAPITAL_A
    </sql_1>
    
    <sql_2>  
        select 
        ACCOUNT|| '-' ||CURRENCY|| '-' ||FUND_ACCOUNT as KEY,
        USER_CODE,ACCOUNT,CURRENCY,BALANCE,AVAILABLE,FROZEN,
        TRD_FRZ,OUTSTANDING,OTD_AVL,CR_AMT,DR_AMT,OTD_DR,CR_BLN,
        CR_AVL,SAVING_ACCRUAL,LAST_INT_BLN,CAL_INT_DATE,SAVING_INT,
        DR_INT,CASH_ACCRUAL,CHQ_ACCRUAL,MKT_VAL,ASSETS_FLOOR,RELATED_ACC_FLAG,
        STATUS,CERTIFIED_AMT,SAVING_INT_TAX
        from kgdb.CAPITAL
    </sql_2>
    
</table>

</rules> 