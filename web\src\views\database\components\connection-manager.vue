<template>
  <div class="connection-manager">
    <div class="toolbar">
      <el-input
        v-model="searchKey"
        placeholder="搜索连接名称、主机、数据库等"
        clearable
        style="width: 320px; margin-right: 10px;"
        @keyup.enter="loadConnections"
        @clear="loadConnections"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <el-button type="primary" @click="handleCreateConnection" :icon="Plus">
        创建连接
      </el-button>
      <el-button type="info" @click="refreshList" :icon="Refresh">
        刷新
      </el-button>
      <el-button type="warning" @click="debugApiConnection" plain>
        调试API
      </el-button>
    </div>

    <el-table
      ref="tableRef"
      :data="filteredConnections"
      v-loading="loading"
      style="width: 100%; margin-top: 16px;"
      border
      stripe
      highlight-current-row
    >
      <el-table-column type="selection" width="50" />

      <el-table-column prop="name" label="连接名称" min-width="150" show-overflow-tooltip />

      <el-table-column prop="type" label="数据库类型" width="120">
        <template v-slot:default="{ row }">
          <el-tag v-if="getDbTypeColor(row.type)" :type="getDbTypeColor(row.type)">{{ getDbTypeName(row.type) }}</el-tag>
          <el-tag v-else>{{ getDbTypeName(row.type) }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="连接配置" min-width="200" show-overflow-tooltip>
        <template v-slot:default="{ row }">
          {{ `${row.host}:${row.port} / ${row.database}` }}
        </template>
      </el-table-column>

      <el-table-column prop="username" label="用户名" width="120" show-overflow-tooltip />

      <el-table-column label="创建时间" width="180" show-overflow-tooltip sortable>
        <template v-slot:default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100">
        <template v-slot:default="{ row }">
          <el-tag :type="row.status === 'online' ? 'success' : 'danger'">
            {{ row.status === 'online' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="220" fixed="right">
        <template v-slot:default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="primary" @click="handleTest(row)">测试</el-button>
          <el-button link type="primary" @click="handleClone(row)">克隆</el-button>
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalConnections"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 连接配置对话框组件 -->
    <connection-dialog
      v-model:visible="dialogVisible"
      :connection-data="currentConnection"
      @submit="handleDialogSubmit"
      @test-connection="handleDialogTest"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh, Search } from '@element-plus/icons-vue';
import {
  getConnectionList,
  createConnection,
  updateConnection,
  testConnection,
  deleteConnection,
  getConnectionDebug
} from '@/api/database/index';
import ConnectionDialog from './connection-dialog.vue';
import type { DataConnection } from '@/types/database';
import { useConnectionStore } from '@/store/modules/connection';
import { emitter } from '@/utils/mitt';
import { getDbTypeColor, getDbTypeName } from '@/utils/db-utils'; // 导入通用数据库工具函数

// 连接列表数据
const connections = ref<DataConnection[]>([]);
const loading = ref(false);
const totalConnections = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const searchKey = ref('');

// 当前操作的连接
const currentConnection = ref<DataConnection | undefined>(undefined);

// 对话框显示状态
const dialogVisible = ref(false);

// 测试连接加载状态
const testLoading = ref('');

// 在顶部的script setup中
const connectionStore = useConnectionStore();

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 过滤后的连接列表
const filteredConnections = computed(() => {
  if (!searchKey.value) return connections.value;

  const keyword = searchKey.value.toLowerCase();
  return connections.value.filter(conn =>
    conn.name.toLowerCase().includes(keyword) ||
    conn.host.toLowerCase().includes(keyword) ||
    conn.database.toLowerCase().includes(keyword) ||
    conn.username.toLowerCase().includes(keyword) ||
    getDbTypeName(conn.type).toLowerCase().includes(keyword)
  );
});



// 加载连接列表
const loadConnections = async () => {
  loading.value = true;

  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchKey.value
    };

    const res = await getConnectionList(params);

    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res && res.success) {
      // 从BaseApiResponse格式中提取data字段
      const extractedData = Array.isArray(res.data) ? res.data : [];
      const extractedTotal = res.total || (res.data ? res.data.length : 0);

      // 获取有效的已删除ID列表
      const deletedIds = connectionStore.getValidDeletedIds();
      const deletedIdStrings = deletedIds.map(item => item.id);

      // 过滤掉已删除的连接
      const filteredConnections = extractedData.filter(conn =>
        !deletedIdStrings.includes(String(conn.id))
      );

      connections.value = filteredConnections;
      totalConnections.value = extractedTotal;

      // 更新Store中的连接数据，确保也过滤掉已删除的连接
      connectionStore.connections = [...filteredConnections];
      connectionStore.connectionTotal = filteredConnections.length;
      connectionStore.loaded = true;

      // 验证连接数据的有效性
      if (connections.value.length > 0) {
        const sampleConnection = connections.value[0];

        // 检查必要字段
        const requiredFields = ['id', 'name', 'type', 'host'];
        const missingFields = requiredFields.filter(field => !sampleConnection.hasOwnProperty(field));

        if (missingFields.length > 0) {
          console.warn(`连接数据缺少必要字段: ${missingFields.join(', ')}`);
          ElMessage.warning('连接数据格式不完整，可能影响显示效果');
        }
      }
    } else {
      // 处理业务逻辑错误
      ElMessage.error(res?.message || '获取连接列表失败');
      connections.value = [];
      totalConnections.value = 0;
      connectionStore.clearConnections();
    }
  } catch (error) {
    console.error('加载连接列表失败:', error);
    ElMessage.error('获取连接列表失败: ' + (error.message || String(error)));
    connections.value = [];
    totalConnections.value = 0;
    // 清空Store中的连接数据
    connectionStore.clearConnections();
  } finally {
    loading.value = false;
  }
};

// 刷新列表
const refreshList = () => {
  loadConnections();
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadConnections();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  loadConnections();
};

// 创建新连接
const handleCreateConnection = () => {
  currentConnection.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑连接
const handleEdit = (row: DataConnection) => {
  // 检查ID是否在已删除列表中
  const deletedIds = connectionStore.getValidDeletedIds();
  const deletedIdStrings = deletedIds.map(item => item.id);

  if (deletedIdStrings.includes(String(row.id))) {
    ElMessageBox.alert(
      '该连接记录已被删除，无法编辑。',
      '连接不存在',
      { type: 'warning' }
    );
    // 刷新列表以移除已删除的项
    loadConnections();
    return;
  }

  // 从Store获取最新数据
  const storeConnection = connectionStore.getConnectionById(row.id);
  if (!storeConnection) {
    ElMessageBox.alert(
      '该连接记录不存在或已被删除，无法编辑。',
      '连接不存在',
      { type: 'warning' }
    );
    // 刷新列表以移除不存在的项
    loadConnections();
    return;
  }

  currentConnection.value = { ...storeConnection };
  dialogVisible.value = true;
};

// 测试连接
const handleTest = async (row: any) => {
  // 检查ID是否在已删除列表中
  const deletedIds = connectionStore.getValidDeletedIds();
  const deletedIdStrings = deletedIds.map(item => item.id);

  if (deletedIdStrings.includes(String(row.id))) {
    ElMessageBox.alert(
      '该连接记录已被删除，无法测试。',
      '连接不存在',
      { type: 'warning' }
    );
    // 刷新列表以移除已删除的项
    loadConnections();
    return;
  }

  testLoading.value = row.id || '';
  try {
    // 构建测试数据 - 不需要重新获取密码，因为：
    // 1. 如果直接从列表点击测试，可以传递ID，后端会自动获取密码
    // 2. 如果从编辑对话框中点击测试，表单中已经有密码信息
    const testData = {
      id: row.id, // 传递ID，让后端能查询完整信息
      type: row.type,
      host: row.host,
      port: row.port,
      username: row.username,
      password: row.password, // 可能为空，此时后端会根据ID查询
      database: row.database,
      parameters: row.parameters || {},
      get_credentials: true // 告诉后端需要补充凭证
    };

    const res = await testConnection(testData);

    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res && res.success) {
      ElMessage.success(res.message || '连接测试成功');
      row.status = 'online';  // 确保状态更新为在线
    } else {
      ElMessage.error(res.message || '连接测试失败');
      row.status = 'offline';  // 确保状态更新为离线
    }
  } catch (error: any) {
    console.error('连接测试失败:', error);
    let errorMsg = '未知错误';

    if (error.response && error.response.data) {
      errorMsg = error.response.data.message || error.response.data.detail || JSON.stringify(error.response.data);
    } else if (error.message) {
      errorMsg = error.message;
    } else if (typeof error === 'string') {
      errorMsg = error;
    } else {
      errorMsg = JSON.stringify(error);
    }

    ElMessage.error(`连接测试失败: ${errorMsg}`);
    row.status = 'offline';  // 确保状态更新为离线
  } finally {
    testLoading.value = '';
  }
};

// 克隆连接
const handleClone = (row: DataConnection) => {
  // 检查ID是否在已删除列表中
  const deletedIds = connectionStore.getValidDeletedIds();
  const deletedIdStrings = deletedIds.map(item => item.id);

  if (deletedIdStrings.includes(String(row.id))) {
    ElMessageBox.alert(
      '该连接记录已被删除，无法克隆。',
      '连接不存在',
      { type: 'warning' }
    );
    // 刷新列表以移除已删除的项
    loadConnections();
    return;
  }

  // 从Store获取最新数据进行克隆
  const storeConnection = connectionStore.getConnectionById(row.id);
  if (!storeConnection) {
    ElMessageBox.alert(
      '该连接记录不存在或已被删除，无法克隆。',
      '连接不存在',
      { type: 'warning' }
    );
    // 刷新列表以移除不存在的项
    loadConnections();
    return;
  }

  const clonedConnection = { ...storeConnection };
  delete clonedConnection.id;
  clonedConnection.name = `${clonedConnection.name} - 副本`;
  currentConnection.value = clonedConnection;
  dialogVisible.value = true;
};

// 处理删除连接
const handleDelete = (row: DataConnection) => {
  ElMessageBox.confirm(
    `确定要删除连接"${row.name}"吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 转换ID为字符串类型
      const id = String(row.id);

      // 发送删除请求
      const res = await deleteConnection(id);

      // 修复：使用success字段判断，与比对模型页面保持一致
      if (res && res.success) {
        ElMessage.success(res.message || '连接删除成功');

        // 从本地列表和Store中移除
        connectionStore.removeConnection(id);

        // 触发连接删除事件
        emitter.emit('connectionDeleted', id);

        // 刷新列表
        await loadConnections();
      } else {
        // 处理业务逻辑失败
        ElMessage.error(res.message || '删除失败');
      }
    } catch (error: any) {
      console.error('删除连接失败', error);
      let errorMsg = '未知错误';

      // 尝试从错误响应中提取消息
      if (error.response && error.response.data) {
        if (typeof error.response.data === 'object') {
          errorMsg = error.response.data.message || error.response.data.detail || JSON.stringify(error.response.data);
        } else if (typeof error.response.data === 'string') {
          errorMsg = error.response.data;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      ElMessage.error(`删除失败: ${errorMsg}`);
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 处理对话框提交
const handleDialogSubmit = async (data: DataConnection) => {
  try {
    let response: any;

    // 根据是否有ID判断是创建还是更新
    if (currentConnection.value && currentConnection.value.id) {
      // 更新连接
      const id = String(currentConnection.value.id);
      response = await updateConnection(id, data);

      if (response.success) {
        // 更新Store中的连接记录
        connectionStore.updateConnection(id, data);

        // 触发连接更新事件
        emitter.emit('connectionUpdated', { id, data });

        ElMessage.success(response.message || '连接更新成功');
      } else {
        ElMessage.error(response.message || '更新失败');
        return; // 失败则退出
      }
    } else {
      // 创建新连接
      response = await createConnection(data);

      if (response.success) {
        // 从响应中获取新创建的连接数据
        if (response.data && response.data.id) {
          // 添加到Store
          const newConn = {
            ...data,
            id: response.data.id
          };
          connectionStore.addConnection(newConn);

          // 触发连接添加事件
          emitter.emit('connectionAdded', newConn);

          ElMessage.success(response.message || '连接创建成功');
        } else {
          console.warn('创建成功但API未返回连接数据');
          ElMessage.warning('连接创建成功，但未返回完整数据');
        }
      } else {
        ElMessage.error(response.message || '创建失败');
        return; // 失败则退出
      }
    }

    // 关闭对话框
    dialogVisible.value = false;

    // 重新加载连接列表
    await loadConnections();
  } catch (error: any) {
    console.error('连接操作失败:', error);
    let errorMsg = '未知错误';

    // 尝试从错误响应中提取消息
    if (error.response && error.response.data) {
      if (typeof error.response.data === 'object') {
        errorMsg = error.response.data.message || error.response.data.detail || JSON.stringify(error.response.data);
      } else if (typeof error.response.data === 'string') {
        errorMsg = error.response.data;
      }
    } else if (error.message) {
      errorMsg = error.message;
    }

    ElMessage.error(`连接操作失败: ${errorMsg}`);
  }
};

// 处理对话框测试连接
const handleDialogTest = async (data: any) => {
  try {
    const res = await testConnection(data);

    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res && res.success) {
      ElMessage.success(res.message || '连接测试成功');
      return true;
    } else {
      ElMessage.error(res.message || '连接测试失败');
      return false;
    }
  } catch (error: any) {
    console.error('测试连接失败', error);
    let errorMsg = '未知错误';

    if (error.response && error.response.data) {
      errorMsg = error.response.data.message || error.response.data.detail || JSON.stringify(error.response.data);
    } else if (error.message) {
      errorMsg = error.message;
    }

    ElMessage.error(`测试连接失败: ${errorMsg}`);
    return false;
  }
};

// 调试API连接
const debugApiConnection = async () => {
  try {
    ElMessage.info('正在调试API连接...');

    const res = await getConnectionDebug();

    // 修复：使用success字段判断，与比对模型页面保持一致
    if (res && res.success) {
      ElMessage.success(res.message || 'API调试成功');
      console.log('API调试结果:', res);
    } else {
      ElMessage.warning(res.message || 'API调试失败');
    }
  } catch (error: any) {
    console.error('调试API连接失败', error);

    let errorMsg = '未知错误';
    if (error.response && error.response.data) {
      if (typeof error.response.data === 'object') {
        errorMsg = error.response.data.message || JSON.stringify(error.response.data);
      } else {
        errorMsg = String(error.response.data);
      }
    } else if (error.message) {
      errorMsg = error.message;
    }

    ElMessage.error(`调试API连接失败: ${errorMsg}`);
  }
};

// 初始化加载
onMounted(() => {
  console.log('连接管理组件已挂载，准备加载数据...');

  // 调用connectionStore的验证方法，会自动清理过期ID
  connectionStore.getValidDeletedIds();

  // 设置状态监听，帮助调试
  watch(connections, (newVal) => {
    if (newVal.length > 0) {
      //console.log('首条连接数据:', newVal[0]);
    }
  });

  // 立即加载数据
  loadConnections();
});

// 暴露给父组件的方法
defineExpose({
  refreshList,
  handleCreateConnection
});
</script>

<style lang="scss" scoped>
.connection-manager {
  padding: 16px;

  .toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>