/**
 * SQL工具函数
 * 用于SQL格式化、生成示例SQL和计算差异
 */
import { format } from 'sql-formatter';

/**
 * 格式化SQL语句
 * @param sql 原始SQL语句
 * @returns 格式化后的SQL语句
 */
export function formatSQL(sql: string): string {
  if (!sql) return '';
  
  try {
    // 使用sql-formatter库格式化SQL，并指定配置确保类型声明不会被拆分
    return format(sql, {
      language: 'sql',
      tabWidth: 2,
      keywordCase: 'upper', // 关键字大写
      linesBetweenQueries: 2, // 查询之间的空行数
      expressionWidth: 80, // 表达式长度阈值
      denseOperators: false, // 操作符周围的空格
      indentStyle: 'standard', // 标准缩进风格
      logicalOperatorNewline: 'before', // 逻辑运算符在新行之前
      // 通过这个选项确保VARCHAR等类型参数不会被拆分
      params: {
        // 默认空对象，防止参数被替换
      }
    });
  } catch (error) {
    console.error('SQL格式化错误:', error);
    return sql;
  }
}

/**
 * 解析SQL语句中的表名
 * @param sql SQL语句
 * @returns 提取的表名，包含schema信息
 */
export function extractTableName(sql: string): { schema: string; table: string } | null {
  if (!sql) return null;
  
  // 将SQL转换为小写并移除多余空格，便于统一处理
  const normalizedSql = sql.trim().replace(/\s+/g, ' ');
  
  // 使用正则表达式匹配FROM子句后的表名
  // 支持以下格式：
  // - schema.table
  // - "schema"."table"
  // - table
  const fromRegex = /\bfrom\s+(?:([a-zA-Z0-9_]+)\.)?([a-zA-Z0-9_]+)(?:\s+|$|\(|\)|\;)/i;
  const matches = normalizedSql.match(fromRegex);
  
  if (matches && matches[2]) {
    // 返回提取的schema和表名
    return {
      schema: matches[1] || '',
      table: matches[2]
    };
  }
  
  return null;
}

/**
 * 解析SQL语句中的主键字段
 * @param sql SQL语句
 * @returns 主键字段数组
 */
export function extractPrimaryKey(sql: string): string[] {
  if (!sql) return [];
  
  // 查找第一个包含"as key"的字段表达式
  const keyRegex = /select\s+(.+?)\s+as\s+key\s*,/i;
  const matches = sql.match(keyRegex);
  
  if (matches && matches[1]) {
    // 提取主键表达式并分解组合主键
    const keyExpression = matches[1];
    
    // 检查是否有连接操作符(||)
    if (keyExpression.includes('||')) {
      // 提取组合主键中的各个字段
      const keyParts = keyExpression.split('||').map(part => {
        // 移除引号和空格，保留字段名
        return part.replace(/['"]/g, '').trim();
      });
      
      // 移除连接符-等其他非字段部分
      return keyParts.filter(part => !part.match(/^['"]-['"]$/));
    }
    
    // 返回单一主键
    return [keyExpression];
  }
  
  return [];
}

/**
 * 提取SQL中的字段列表
 * @param sql SQL语句
 * @returns 字段名数组
 */
export function extractFields(sql: string): string[] {
  if (!sql) return [];
  
  // 查找SELECT和FROM之间的部分
  const selectRegex = /select\s+(.+?)\s+from/is;
  const matches = sql.match(selectRegex);
  
  if (!matches || !matches[1]) return [];
  
  const fieldsSection = matches[1];
  
  // 分割字段列表，处理as别名和函数调用等复杂情况
  const fields: string[] = [];
  let currentField = '';
  let inParentheses = 0;
  
  for (let i = 0; i < fieldsSection.length; i++) {
    const char = fieldsSection[i];
    
    if (char === '(') {
      inParentheses++;
      currentField += char;
    } else if (char === ')') {
      inParentheses--;
      currentField += char;
    } else if (char === ',' && inParentheses === 0) {
      // 只有在不在括号内时才分割字段
      if (currentField.trim()) {
        fields.push(currentField.trim());
      }
      currentField = '';
    } else {
      currentField += char;
    }
  }
  
  // 添加最后一个字段
  if (currentField.trim()) {
    fields.push(currentField.trim());
  }
  
  // 处理字段别名，提取实际字段名
  return fields.map(field => {
    // 移除as关键字和别名
    const asIndex = field.toLowerCase().indexOf(' as ');
    if (asIndex !== -1) {
      return field.substring(0, asIndex).trim();
    }
    return field.trim();
  });
}

/**
 * 解析SQL为表配置对象
 * @param sql1 源SQL
 * @param sql2 目标SQL
 * @returns 表映射配置对象
 */
export function parseSqlToTableMapping(sql1: string, sql2: string): {
  source_table: string;
  source_schema: string;
  target_table: string;
  target_schema: string;
  primary_keys: string[];
  field_mappings: Array<{
    source_field: string;
    target_field: string;
    is_primary_key: boolean;
  }>;
} {
  // 提取表名
  const sourceTableInfo = extractTableName(sql1);
  const targetTableInfo = extractTableName(sql2);
  
  // 提取主键
  const primaryKeys = extractPrimaryKey(sql1);
  
  // 提取字段
  const sourceFields = extractFields(sql1);
  const targetFields = extractFields(sql2);
  
  // 构建字段映射
  const fieldMappings = sourceFields.map((field, index) => {
    const targetField = index < targetFields.length ? targetFields[index] : field;
    return {
      source_field: field,
      target_field: targetField,
      is_primary_key: primaryKeys.some(key => field.includes(key))
    };
  });
  
  return {
    source_table: sourceTableInfo?.table || '',
    source_schema: sourceTableInfo?.schema || '',
    target_table: targetTableInfo?.table || '',
    target_schema: targetTableInfo?.schema || '',
    primary_keys: primaryKeys,
    field_mappings: fieldMappings
  };
}

/**
 * 生成示例SQL语句
 * @param index 表配置索引
 * @param sqlNum SQL编号（1或2）
 * @returns 示例SQL语句
 */
export function getExampleSQL(index: number, sqlNum: number): string {
  // 基础表名
  const tableName = `table_${index + 1}`;
  
  // 基础字段列表
  const baseColumns = [
    'id INT PRIMARY KEY',
    'name VARCHAR(100) NOT NULL',
    'description TEXT',
    'status VARCHAR(20) DEFAULT \'active\'',
    'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
  ];
  
  let result = '';
  
  // SQL1与SQL2的差异字段
  if (sqlNum === 1) {
    result = `CREATE TABLE ${tableName} (\n  ${baseColumns.join(',\n  ')}\n);`;
  } else {
    // SQL2包含一些额外的字段或修改
    const extendedColumns = [
      'id INT PRIMARY KEY',
      'name VARCHAR(100) NOT NULL',
      'description TEXT',
      'status VARCHAR(50) DEFAULT \'active\'', // 修改了长度
      'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      'updated_at TIMESTAMP', // 新增字段
      'is_deleted BOOLEAN DEFAULT FALSE' // 新增字段
    ];
    result = `CREATE TABLE ${tableName} (\n  ${extendedColumns.join(',\n  ')}\n);`;
  }
  
  return result;
}

/**
 * 计算两个SQL语句之间的差异
 * @param sql1 第一个SQL语句
 * @param sql2 第二个SQL语句
 * @returns 差异统计信息
 */
export function calculateDiff(sql1: string, sql2: string): { matchCount: number; missingCount: number; diffCount: number } {
  if (!sql1 && !sql2) {
    return {
      matchCount: 0,
      missingCount: 0,
      diffCount: 0
    };
  }
  
  if (!sql1 || !sql2) {
    return {
      matchCount: 0,
      missingCount: Math.max(
        sql1 ? sql1.split('\n').filter(line => line.trim()).length : 0,
        sql2 ? sql2.split('\n').filter(line => line.trim()).length : 0
      ),
      diffCount: 0
    };
  }
  
  // 将SQL语句分解为行，并去除空行
  const lines1 = sql1.split('\n').filter(line => line.trim());
  const lines2 = sql2.split('\n').filter(line => line.trim());
  
  let matchCount = 0;
  let missingCount = 0;
  let diffCount = 0;
  
  // 比较两个SQL语句的行
  const maxLines = Math.max(lines1.length, lines2.length);
  for (let i = 0; i < maxLines; i++) {
    if (i >= lines1.length) {
      // sql2有额外的行
      missingCount++;
    } else if (i >= lines2.length) {
      // sql1有额外的行
      missingCount++;
    } else if (lines1[i].trim() === lines2[i].trim()) {
      // 行匹配
      matchCount++;
    } else {
      // 行不匹配
      diffCount++;
    }
  }
  
  return {
    matchCount,
    missingCount,
    diffCount
  };
} 