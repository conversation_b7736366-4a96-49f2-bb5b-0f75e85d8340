"""
比对结果同步服务
负责将Agent的文件结果同步到Server数据库
"""

import json
import logging
from typing import Dict, Any, List, Optional
from django.db import transaction
from django.utils import timezone
from ..models.comparison import (
    ComparisonTask,
    ComparisonSummary,
    ComparisonDifference,
    ComparisonSourceOnly,
    ComparisonTargetOnly,
    ComparisonMetadata
)

logger = logging.getLogger(__name__)


class ComparisonSyncService:
    """比对结果同步服务"""
    
    def __init__(self):
        self.logger = logger
    
    @transaction.atomic
    def sync_comparison_results(self, task_id: str, results_data: Dict[str, Any]) -> bool:
        """
        同步比对结果到数据库
        
        Args:
            task_id: 任务ID
            results_data: 标准化的结果数据
            
        Returns:
            bool: 同步是否成功
        """
        try:
            # 获取任务
            task = ComparisonTask.objects.get(task_id=task_id)
            
            # 更新任务状态
            task.status = 'completed'
            task.completed_at = timezone.now()
            task.save()
            
            # 同步摘要信息
            self._sync_summary(task, results_data.get('summary', {}))
            
            # 同步元数据
            self._sync_metadata(task, results_data.get('metadata', {}))
            
            # 同步差异记录
            if 'differences' in results_data:
                self._sync_differences(task, results_data['differences'])
            
            # 同步源独有记录
            if 'source_only' in results_data:
                self._sync_source_only(task, results_data['source_only'])
            
            # 同步目标独有记录
            if 'target_only' in results_data:
                self._sync_target_only(task, results_data['target_only'])
            
            self.logger.info(f"成功同步任务 {task_id} 的比对结果")
            return True
            
        except ComparisonTask.DoesNotExist:
            self.logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            self.logger.error(f"同步任务 {task_id} 结果失败: {str(e)}")
            return False
    
    def _sync_summary(self, task: ComparisonTask, summary_data: Dict[str, Any]):
        """同步摘要信息"""
        summary, created = ComparisonSummary.objects.get_or_create(
            task=task,
            defaults={
                'total_records': summary_data.get('total_records', 0),
                'matched_records': summary_data.get('matched_records', 0),
                'diff_records': summary_data.get('diff_records', 0),
                'source_only_records': summary_data.get('source_only_records', 0),
                'target_only_records': summary_data.get('target_only_records', 0),
                'execution_time': summary_data.get('execution_time', 0),
                'algorithm_used': summary_data.get('algorithm', ''),
            }
        )
        
        if not created:
            # 更新现有记录
            summary.total_records = summary_data.get('total_records', 0)
            summary.matched_records = summary_data.get('matched_records', 0)
            summary.diff_records = summary_data.get('diff_records', 0)
            summary.source_only_records = summary_data.get('source_only_records', 0)
            summary.target_only_records = summary_data.get('target_only_records', 0)
            summary.execution_time = summary_data.get('execution_time', 0)
            summary.algorithm_used = summary_data.get('algorithm', '')
            summary.save()
    
    def _sync_metadata(self, task: ComparisonTask, metadata: Dict[str, Any]):
        """同步元数据"""
        meta, created = ComparisonMetadata.objects.get_or_create(
            task=task,
            defaults={
                'source_type': metadata.get('source_type', ''),
                'target_type': metadata.get('target_type', ''),
                'algorithm': metadata.get('algorithm', ''),
                'batch_size': metadata.get('batch_size', 1000),
                'agent_version': metadata.get('agent_version', ''),
                'schema_version': metadata.get('schema_version', '1.0'),
            }
        )
        
        if not created:
            # 更新现有记录
            meta.source_type = metadata.get('source_type', '')
            meta.target_type = metadata.get('target_type', '')
            meta.algorithm = metadata.get('algorithm', '')
            meta.batch_size = metadata.get('batch_size', 1000)
            meta.agent_version = metadata.get('agent_version', '')
            meta.schema_version = metadata.get('schema_version', '1.0')
            meta.save()
    
    def _sync_differences(self, task: ComparisonTask, differences: List[Dict[str, Any]]):
        """同步差异记录"""
        # 清除现有差异记录
        ComparisonDifference.objects.filter(task=task).delete()
        
        # 批量创建新记录
        diff_objects = []
        for diff in differences:
            diff_obj = ComparisonDifference(
                task=task,
                record_id=diff.get('id', ''),
                source_data=diff.get('source_data', {}),
                target_data=diff.get('target_data', {}),
                diff_fields=diff.get('diff_fields', []),
                diff_type=diff.get('diff_type', 'value_diff')
            )
            diff_objects.append(diff_obj)
        
        # 分批插入，避免内存问题
        batch_size = 1000
        for i in range(0, len(diff_objects), batch_size):
            batch = diff_objects[i:i + batch_size]
            ComparisonDifference.objects.bulk_create(batch)
        
        self.logger.info(f"同步了 {len(differences)} 条差异记录")
    
    def _sync_source_only(self, task: ComparisonTask, source_only: List[Dict[str, Any]]):
        """同步源独有记录"""
        # 清除现有记录
        ComparisonSourceOnly.objects.filter(task=task).delete()
        
        # 批量创建新记录
        source_objects = []
        for record in source_only:
            source_obj = ComparisonSourceOnly(
                task=task,
                record_id=record.get('id', ''),
                data=record.get('data', {}),
                reason=record.get('reason', '')
            )
            source_objects.append(source_obj)
        
        # 分批插入
        batch_size = 1000
        for i in range(0, len(source_objects), batch_size):
            batch = source_objects[i:i + batch_size]
            ComparisonSourceOnly.objects.bulk_create(batch)
        
        self.logger.info(f"同步了 {len(source_only)} 条源独有记录")
    
    def _sync_target_only(self, task: ComparisonTask, target_only: List[Dict[str, Any]]):
        """同步目标独有记录"""
        # 清除现有记录
        ComparisonTargetOnly.objects.filter(task=task).delete()
        
        # 批量创建新记录
        target_objects = []
        for record in target_only:
            target_obj = ComparisonTargetOnly(
                task=task,
                record_id=record.get('id', ''),
                data=record.get('data', {}),
                reason=record.get('reason', '')
            )
            target_objects.append(target_obj)
        
        # 分批插入
        batch_size = 1000
        for i in range(0, len(target_objects), batch_size):
            batch = target_objects[i:i + batch_size]
            ComparisonTargetOnly.objects.bulk_create(batch)
        
        self.logger.info(f"同步了 {len(target_only)} 条目标独有记录")
    
    def update_task_status(self, task_id: str, status: str, progress: int = None, error_message: str = None):
        """更新任务状态"""
        try:
            task = ComparisonTask.objects.get(task_id=task_id)
            task.status = status
            
            if progress is not None:
                task.progress = progress
            
            if error_message:
                task.error_message = error_message
            
            if status == 'running' and not task.started_at:
                task.started_at = timezone.now()
            elif status in ['completed', 'failed', 'canceled']:
                task.completed_at = timezone.now()
            
            task.save()
            return True
            
        except ComparisonTask.DoesNotExist:
            self.logger.error(f"任务 {task_id} 不存在")
            return False
        except Exception as e:
            self.logger.error(f"更新任务 {task_id} 状态失败: {str(e)}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            task = ComparisonTask.objects.get(task_id=task_id)
            return {
                'task_id': task.task_id,
                'status': task.status,
                'progress': task.progress,
                'error_message': task.error_message,
                'created_at': task.created_at.isoformat(),
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            }
        except ComparisonTask.DoesNotExist:
            return None


# 创建全局实例
comparison_sync_service = ComparisonSyncService()
