/**
 * 数据库基础操作API
 *
 * 提供数据库类型查询、对象树获取、表结构查询、SQL执行等基础操作功能
 * 重构版本 - 统一使用DatabaseApiClient和标准化端点
 */

import { databaseApiClient, withDatabaseErrorHandling } from './client';
import { DATABASE_ENDPOINTS } from './endpoints';
import type { BaseApiResponse } from './types';
import type { DatabaseType, DatabaseObject, QueryResult } from "@/types/database";

/**
 * 获取数据库类型列表
 */
export const getDatabaseTypes = withDatabaseErrorHandling(
  async (): Promise<BaseApiResponse<DatabaseType[]>> => {
    return databaseApiClient.get<DatabaseType[]>(DATABASE_ENDPOINTS.TYPES, {
      useCache: true,
      cacheTTL: 3600000 // 1小时缓存，数据库类型变化较少
    });
  }
);

/**
 * 获取数据库对象树
 * @param sourceId - 数据源ID
 * @param params - 查询参数
 */
export const getDatabaseObjects = withDatabaseErrorHandling(
  async (sourceId: string, params?: {
    schemaName?: string;
    objectType?: string;
    filter?: string;
    page?: number;
    size?: number;
  }): Promise<BaseApiResponse<DatabaseObject[]>> => {
    if (!sourceId) {
      throw new Error('数据源ID不能为空');
    }

    return databaseApiClient.get<DatabaseObject[]>(DATABASE_ENDPOINTS.SOURCE_OBJECTS(sourceId), {
      params,
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取表结构
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param tableName - 表名称
 */
export const getTableStructure = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, tableName: string): Promise<BaseApiResponse<{
    columns: Array<{
      name: string;
      type: string;
      nullable: boolean;
      defaultValue: any;
      comment?: string;
      isPrimary?: boolean;
      isUnique?: boolean;
      isForeign?: boolean;
    }>;
    indexes: Array<{
      name: string;
      columns: string[];
      isUnique: boolean;
      isPrimary?: boolean;
    }>;
    comment?: string;
    createTime?: number;
    updateTime?: number;
  }>> => {
    if (!sourceId || !schemaName || !tableName) {
      throw new Error('数据源ID、模式名称和表名称不能为空');
    }

    return databaseApiClient.get(DATABASE_ENDPOINTS.TABLE_STRUCTURE(sourceId, schemaName, tableName), {
      useCache: true,
      cacheTTL: 600000 // 10分钟缓存，表结构变化较少
    });
  }
);

/**
 * 获取表数据预览
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param tableName - 表名称
 * @param params - 查询参数
 */
export const getTableDataPreview = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, tableName: string, params?: {
    page?: number;
    size?: number;
    orderBy?: string;
    sort?: 'asc' | 'desc';
  }): Promise<BaseApiResponse<QueryResult>> => {
    if (!sourceId || !schemaName || !tableName) {
      throw new Error('数据源ID、模式名称和表名称不能为空');
    }

    return databaseApiClient.get<QueryResult>(
      DATABASE_ENDPOINTS.TABLE_DATA(sourceId, schemaName, tableName),
      { params }
    );
  }
);

/**
 * 执行SQL查询
 * @param sourceId - 数据源ID
 * @param data - 查询数据
 */
export const executeQuery = withDatabaseErrorHandling(
  async (sourceId: string, data: {
    sql: string;
    timeout?: number;
    maxRows?: number;
  }): Promise<BaseApiResponse<QueryResult>> => {
    if (!sourceId) {
      throw new Error('数据源ID不能为空');
    }

    if (!data.sql || data.sql.trim() === '') {
      throw new Error('SQL语句不能为空');
    }

    return databaseApiClient.post<QueryResult>(DATABASE_ENDPOINTS.EXECUTE_QUERY(sourceId), data, {
      timeout: data.timeout || 30000,
      showErrorMessage: true
    });
  }
);

/**
 * 取消查询执行
 * @param queryId - 查询ID
 */
export const cancelQuery = withDatabaseErrorHandling(
  async (queryId: string): Promise<BaseApiResponse<null>> => {
    if (!queryId) {
      throw new Error('查询ID不能为空');
    }

    return databaseApiClient.post<null>(`/database/query/${queryId}/cancel/`, {}, {
      showSuccessMessage: true,
      showErrorMessage: true
    });
  }
);

/**
 * 获取查询历史
 * @param params - 查询参数
 */
export const getQueryHistory = withDatabaseErrorHandling(
  async (params?: {
    sourceId?: string;
    page?: number;
    size?: number;
    startTime?: number;
    endTime?: number;
  }): Promise<BaseApiResponse<Array<{
    id: string;
    sql: string;
    sourceId: string;
    sourceName?: string;
    status: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    rowCount?: number;
    message?: string;
  }>>> => {
    return databaseApiClient.get("/database/query/history/", {
      params,
      useCache: true,
      cacheTTL: 60000 // 1分钟缓存
    });
  }
);

/**
 * 获取表的DDL
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param tableName - 表名称
 */
export const getTableDDL = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, tableName: string): Promise<BaseApiResponse<{
    ddl: string;
  }>> => {
    if (!sourceId || !schemaName || !tableName) {
      throw new Error('数据源ID、模式名称和表名称不能为空');
    }

    return databaseApiClient.get(
      `${DATABASE_ENDPOINTS.TABLE_STRUCTURE(sourceId, schemaName, tableName)}ddl/`,
      {
        useCache: true,
        cacheTTL: 600000 // 10分钟缓存
      }
    );
  }
);

/**
 * 获取视图定义
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param viewName - 视图名称
 */
export const getViewDefinition = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, viewName: string): Promise<BaseApiResponse<{
    definition: string;
  }>> => {
    if (!sourceId || !schemaName || !viewName) {
      throw new Error('数据源ID、模式名称和视图名称不能为空');
    }

    return databaseApiClient.get(
      `/database/source/${sourceId}/view/${encodeURIComponent(schemaName)}/${encodeURIComponent(viewName)}/`,
      {
        useCache: true,
        cacheTTL: 600000 // 10分钟缓存
      }
    );
  }
);

/**
 * 获取存储过程定义
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param procedureName - 存储过程名称
 */
export const getProcedureDefinition = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, procedureName: string): Promise<BaseApiResponse<{
    definition: string;
    parameters: Array<{
      name: string;
      type: string;
      mode: string;
      defaultValue?: any;
    }>;
  }>> => {
    if (!sourceId || !schemaName || !procedureName) {
      throw new Error('数据源ID、模式名称和存储过程名称不能为空');
    }

    return databaseApiClient.get(
      `/database/source/${sourceId}/procedure/${encodeURIComponent(schemaName)}/${encodeURIComponent(procedureName)}/`,
      {
        useCache: true,
        cacheTTL: 600000 // 10分钟缓存
      }
    );
  }
);

/**
 * 获取函数定义
 * @param sourceId - 数据源ID
 * @param schemaName - 模式名称
 * @param functionName - 函数名称
 */
export const getFunctionDefinition = withDatabaseErrorHandling(
  async (sourceId: string, schemaName: string, functionName: string): Promise<BaseApiResponse<{
    definition: string;
    parameters: Array<{
      name: string;
      type: string;
      mode: string;
      defaultValue?: any;
    }>;
    returnType: string;
  }>> => {
    if (!sourceId || !schemaName || !functionName) {
      throw new Error('数据源ID、模式名称和函数名称不能为空');
    }

    return databaseApiClient.get(
      `/database/source/${sourceId}/function/${encodeURIComponent(schemaName)}/${encodeURIComponent(functionName)}/`,
      {
        useCache: true,
        cacheTTL: 600000 // 10分钟缓存
      }
    );
  }
);

/**
 * 导出查询结果
 * @param queryId - 查询ID
 * @param format - 导出格式
 */
export const exportQueryResult = withDatabaseErrorHandling(
  async (queryId: string, format: string = "csv"): Promise<Blob> => {
    if (!queryId) {
      throw new Error('查询ID不能为空');
    }

    const response = await databaseApiClient.get(`/database/query/${queryId}/export/`, {
      params: { format },
      responseType: "blob"
    });

    return response as any; // Blob类型特殊处理
  }
);

/**
 * 获取数据库模式列表
 * @param sourceId - 数据源ID
 */
export const getDatabaseSchemas = withDatabaseErrorHandling(
  async (sourceId: string): Promise<BaseApiResponse<string[]>> => {
    if (!sourceId) {
      throw new Error('数据源ID不能为空');
    }

    return databaseApiClient.get<string[]>(DATABASE_ENDPOINTS.SCHEMAS(sourceId), {
      useCache: true,
      cacheTTL: 300000 // 5分钟缓存
    });
  }
);

/**
 * 获取推荐的SQL补全
 * @param sourceId - 数据源ID
 * @param data - 补全数据
 */
export const getSqlCompletions = withDatabaseErrorHandling(
  async (sourceId: string, data: {
    sql: string;
    position: {
      lineNumber: number;
      column: number;
    };
    schemaName?: string;
  }): Promise<BaseApiResponse<Array<{
    label: string;
    kind: string;
    detail?: string;
    insertText: string;
    range?: {
      startLineNumber: number;
      startColumn: number;
      endLineNumber: number;
      endColumn: number;
    };
  }>>> => {
    if (!sourceId) {
      throw new Error('数据源ID不能为空');
    }

    if (!data.sql) {
      throw new Error('SQL语句不能为空');
    }

    return databaseApiClient.post(DATABASE_ENDPOINTS.SQL_COMPLETION(sourceId), data);
  }
);