/**
 * 数据转换工具类
 * 
 * 提供前后端数据格式转换功能，主要解决 snake_case 和 camelCase 命名约定的差异
 * 支持嵌套对象、数组的递归转换，并提供完整的错误处理机制
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

// ===== 类型定义 =====

/**
 * 转换选项配置
 */
export interface TransformOptions {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否严格模式（遇到未知字段时抛出错误） */
  strict?: boolean;
  /** 自定义字段映射 */
  customMappings?: Record<string, string>;
  /** 最大递归深度，防止循环引用 */
  maxDepth?: number;
}

/**
 * 转换结果
 */
export interface TransformResult<T = any> {
  /** 转换后的数据 */
  data: T;
  /** 警告信息 */
  warnings: string[];
  /** 是否有错误 */
  hasErrors: boolean;
}

/**
 * 字段映射配置类型
 */
export interface FieldMappingConfig {
  /** snake_case 到 camelCase 的映射 */
  snakeToCamel: Record<string, string>;
  /** camelCase 到 snake_case 的映射 */
  camelToSnake: Record<string, string>;
}

// ===== 字段映射配置 =====

/**
 * 完整的字段映射配置
 * 按模块分类管理字段映射关系
 */
export const FIELD_MAPPINGS: FieldMappingConfig = {
  snakeToCamel: {
    // 比对结果相关
    'task_id': 'taskId',
    'external_id': 'externalId',
    'total_records': 'totalRecords',
    'matched_records': 'matchedRecords',
    'different_records': 'diffRecords',
    'source_only_records': 'sourceOnlyRecords',
    'target_only_records': 'targetOnlyRecords',
    'error_records': 'errorRecords',
    'execution_time': 'executionTime',
    
    // 时间相关
    'created_at': 'createdAt',
    'updated_at': 'updatedAt',
    'completed_at': 'completedAt',
    'start_time': 'startTime',
    'end_time': 'endTime',
    'create_time': 'createTime',
    'update_time': 'updateTime',
    
    // 任务状态相关
    'task_status': 'taskStatus',
    'task_type': 'taskType',
    'task_name': 'taskName',
    'task_description': 'taskDescription',
    'progress_percentage': 'progressPercentage',
    'current_step': 'currentStep',
    'total_steps': 'totalSteps',
    
    // 连接信息相关
    'connection_id': 'connectionId',
    'connection_name': 'connectionName',
    'connection_type': 'connectionType',
    'host_name': 'hostName',
    'port_number': 'portNumber',
    'database_name': 'databaseName',
    'user_name': 'userName',
    'schema_name': 'schemaName',
    'table_name': 'tableName',
    
    // 差异记录相关
    'diff_id': 'diffId',
    'diff_type': 'diffType',
    'field_name': 'fieldName',
    'source_value': 'sourceValue',
    'target_value': 'targetValue',
    'record_id': 'recordId',
    'source_record': 'sourceRecord',
    'target_record': 'targetRecord',
    'field_diffs': 'fieldDiffs',
    'primary_key': 'primaryKey',
    
    // 通用字段
    'is_active': 'isActive',
    'is_deleted': 'isDeleted',
    'is_default': 'isDefault',
    'sort_order': 'sortOrder',
    'display_name': 'displayName',
    'full_name': 'fullName',
    'first_name': 'firstName',
    'last_name': 'lastName',
    'email_address': 'emailAddress',
    'phone_number': 'phoneNumber',
  },
  
  camelToSnake: {
    // 自动生成反向映射
  }
};

// 自动生成反向映射
Object.entries(FIELD_MAPPINGS.snakeToCamel).forEach(([snake, camel]) => {
  FIELD_MAPPINGS.camelToSnake[camel] = snake;
});

// ===== 工具函数 =====

/**
 * 检查是否为普通对象
 */
function isPlainObject(obj: any): boolean {
  return obj !== null && 
         typeof obj === 'object' && 
         !Array.isArray(obj) && 
         !(obj instanceof Date) && 
         !(obj instanceof RegExp);
}

/**
 * 检查循环引用
 */
function hasCircularReference(obj: any, seen = new WeakSet()): boolean {
  if (obj === null || typeof obj !== 'object') {
    return false;
  }
  
  if (seen.has(obj)) {
    return true;
  }
  
  seen.add(obj);
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && hasCircularReference(obj[key], seen)) {
      return true;
    }
  }
  
  seen.delete(obj);
  return false;
}

/**
 * 自动转换 snake_case 到 camelCase
 */
function autoSnakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * 自动转换 camelCase 到 snake_case
 */
function autoCamelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

// ===== 主要转换类 =====

/**
 * 数据转换工具类
 */
export class DataTransformUtil {
  private static defaultOptions: Required<TransformOptions> = {
    debug: false,
    strict: false,
    customMappings: {},
    maxDepth: 10
  };

  /**
   * 将 snake_case 字段转换为 camelCase
   * @param data 要转换的数据
   * @param options 转换选项
   * @returns 转换结果
   */
  static snakeToCamel<T = any>(
    data: any, 
    options: TransformOptions = {}
  ): TransformResult<T> {
    const opts = { ...this.defaultOptions, ...options };
    const warnings: string[] = [];
    let hasErrors = false;

    try {
      // 检查循环引用
      if (hasCircularReference(data)) {
        throw new Error('检测到循环引用，无法进行转换');
      }

      const result = this.transformKeys(
        data,
        { ...FIELD_MAPPINGS.snakeToCamel, ...opts.customMappings },
        autoSnakeToCamel,
        opts,
        warnings,
        0
      );

      if (opts.debug) {
        console.log('🔄 DataTransformUtil.snakeToCamel 转换完成:', {
          originalData: data,
          transformedData: result,
          warnings
        });
      }

      return {
        data: result,
        warnings,
        hasErrors
      };
    } catch (error) {
      hasErrors = true;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      warnings.push(`转换失败: ${errorMessage}`);
      
      if (opts.debug) {
        console.error('❌ DataTransformUtil.snakeToCamel 转换失败:', error);
      }

      return {
        data: data,
        warnings,
        hasErrors
      };
    }
  }

  /**
   * 将 camelCase 字段转换为 snake_case
   * @param data 要转换的数据
   * @param options 转换选项
   * @returns 转换结果
   */
  static camelToSnake<T = any>(
    data: any, 
    options: TransformOptions = {}
  ): TransformResult<T> {
    const opts = { ...this.defaultOptions, ...options };
    const warnings: string[] = [];
    let hasErrors = false;

    try {
      // 检查循环引用
      if (hasCircularReference(data)) {
        throw new Error('检测到循环引用，无法进行转换');
      }

      const result = this.transformKeys(
        data,
        { ...FIELD_MAPPINGS.camelToSnake, ...opts.customMappings },
        autoCamelToSnake,
        opts,
        warnings,
        0
      );

      if (opts.debug) {
        console.log('🔄 DataTransformUtil.camelToSnake 转换完成:', {
          originalData: data,
          transformedData: result,
          warnings
        });
      }

      return {
        data: result,
        warnings,
        hasErrors
      };
    } catch (error) {
      hasErrors = true;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      warnings.push(`转换失败: ${errorMessage}`);
      
      if (opts.debug) {
        console.error('❌ DataTransformUtil.camelToSnake 转换失败:', error);
      }

      return {
        data: data,
        warnings,
        hasErrors
      };
    }
  }

  /**
   * 递归转换对象的键名
   * @param obj 要转换的对象
   * @param mappings 字段映射表
   * @param autoTransform 自动转换函数
   * @param options 转换选项
   * @param warnings 警告信息收集器
   * @param depth 当前递归深度
   * @returns 转换后的对象
   */
  private static transformKeys(
    obj: any,
    mappings: Record<string, string>,
    autoTransform: (str: string) => string,
    options: Required<TransformOptions>,
    warnings: string[],
    depth: number
  ): any {
    // 检查递归深度
    if (depth > options.maxDepth) {
      const warning = `达到最大递归深度 ${options.maxDepth}，停止转换`;
      warnings.push(warning);
      if (options.debug) {
        console.warn('⚠️ DataTransformUtil:', warning);
      }
      return obj;
    }

    // 处理 null 和 undefined
    if (obj === null || obj === undefined) {
      return obj;
    }

    // 处理数组
    if (Array.isArray(obj)) {
      return obj.map(item =>
        this.transformKeys(item, mappings, autoTransform, options, warnings, depth + 1)
      );
    }

    // 处理普通对象
    if (isPlainObject(obj)) {
      const transformed: any = {};
      const processedKeys = new Set<string>(); // 跟踪已处理的目标键名

      for (const [originalKey, value] of Object.entries(obj)) {
        let newKey: string;

        // 优先使用映射表
        if (mappings[originalKey]) {
          newKey = mappings[originalKey];
        } else {
          // 使用自动转换
          newKey = autoTransform(originalKey);

          // 如果转换后的键与原键不同，记录警告
          if (newKey !== originalKey) {
            const warning = `字段 '${originalKey}' 使用自动转换为 '${newKey}'，建议添加到映射表中`;
            warnings.push(warning);

            if (options.debug) {
              console.warn('⚠️ DataTransformUtil:', warning);
            }

            // 严格模式下抛出错误
            if (options.strict) {
              throw new Error(`严格模式下不允许自动转换未知字段: ${originalKey}`);
            }
          }
        }

        // 处理混合格式数据：如果目标键已存在，优先保留映射表中的值
        if (processedKeys.has(newKey)) {
          // 检查是否是已知的混合格式情况
          const isKnownMixedFormat = this.isKnownMixedFormatField(originalKey, newKey, mappings);

          if (isKnownMixedFormat) {
            const warning = `检测到混合格式字段: '${originalKey}' 和 '${newKey}' 都存在，优先使用映射表中的值`;
            warnings.push(warning);

            if (options.debug) {
              console.warn('⚠️ DataTransformUtil 混合格式:', warning);
            }

            // 如果当前字段在映射表中，则覆盖之前的值
            if (mappings[originalKey]) {
              transformed[newKey] = this.transformKeys(
                value,
                mappings,
                autoTransform,
                options,
                warnings,
                depth + 1
              );
            }
            // 否则跳过这个字段，保留之前的值
            continue;
          } else {
            // 如果不是已知的混合格式，但键名冲突，记录警告并跳过
            const warning = `键名冲突: '${originalKey}' 转换为 '${newKey}' 但该键已存在，跳过此字段`;
            warnings.push(warning);

            if (options.debug) {
              console.warn('⚠️ DataTransformUtil 键名冲突:', warning);
            }
            continue;
          }
        }

        // 递归转换值
        transformed[newKey] = this.transformKeys(
          value,
          mappings,
          autoTransform,
          options,
          warnings,
          depth + 1
        );

        processedKeys.add(newKey);
      }

      return transformed;
    }

    // 其他类型直接返回
    return obj;
  }

  /**
   * 检查是否是已知的混合格式字段
   * @param originalKey 原始键名
   * @param targetKey 目标键名
   * @param mappings 映射表
   * @returns 是否是已知的混合格式
   */
  private static isKnownMixedFormatField(
    originalKey: string,
    targetKey: string,
    mappings: Record<string, string>
  ): boolean {
    // 检查是否存在 snake_case 和 camelCase 的对应关系
    const snakeVersion = targetKey.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    const camelVersion = originalKey.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

    return (
      (originalKey === snakeVersion && targetKey === camelVersion) ||
      (mappings[snakeVersion] === targetKey) ||
      (mappings[originalKey] === targetKey)
    );
  }

  /**
   * 添加自定义字段映射
   * @param snakeKey snake_case 字段名
   * @param camelKey camelCase 字段名
   */
  static addFieldMapping(snakeKey: string, camelKey: string): void {
    FIELD_MAPPINGS.snakeToCamel[snakeKey] = camelKey;
    FIELD_MAPPINGS.camelToSnake[camelKey] = snakeKey;
  }

  /**
   * 批量添加字段映射
   * @param mappings 字段映射对象
   */
  static addFieldMappings(mappings: Record<string, string>): void {
    Object.entries(mappings).forEach(([snake, camel]) => {
      this.addFieldMapping(snake, camel);
    });
  }

  /**
   * 获取当前所有字段映射
   * @returns 字段映射配置
   */
  static getFieldMappings(): FieldMappingConfig {
    return {
      snakeToCamel: { ...FIELD_MAPPINGS.snakeToCamel },
      camelToSnake: { ...FIELD_MAPPINGS.camelToSnake }
    };
  }

  /**
   * 重置字段映射为默认配置
   */
  static resetFieldMappings(): void {
    // 清空当前映射
    Object.keys(FIELD_MAPPINGS.snakeToCamel).forEach(key => {
      delete FIELD_MAPPINGS.snakeToCamel[key];
    });
    Object.keys(FIELD_MAPPINGS.camelToSnake).forEach(key => {
      delete FIELD_MAPPINGS.camelToSnake[key];
    });

    // 重新加载默认映射
    const defaultMappings = {
      'task_id': 'taskId',
      'total_records': 'totalRecords',
      'matched_records': 'matchedRecords',
      'different_records': 'diffRecords',
      'source_only_records': 'sourceOnlyRecords',
      'target_only_records': 'targetOnlyRecords',
      'error_records': 'errorRecords',
      'execution_time': 'executionTime',
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'completed_at': 'completedAt',
      'start_time': 'startTime',
      'end_time': 'endTime'
    };

    this.addFieldMappings(defaultMappings);
  }

  /**
   * 验证数据结构是否符合预期
   * @param data 要验证的数据
   * @param expectedFields 期望的字段列表
   * @returns 验证结果
   */
  static validateStructure(
    data: any,
    expectedFields: string[]
  ): { isValid: boolean; missingFields: string[]; extraFields: string[] } {
    if (!isPlainObject(data)) {
      return {
        isValid: false,
        missingFields: expectedFields,
        extraFields: []
      };
    }

    const actualFields = Object.keys(data);
    const missingFields = expectedFields.filter(field => !actualFields.includes(field));
    const extraFields = actualFields.filter(field => !expectedFields.includes(field));

    return {
      isValid: missingFields.length === 0,
      missingFields,
      extraFields
    };
  }
}

// ===== 便捷函数 =====

/**
 * 快速转换比对结果数据（snake_case 到 camelCase）
 * @param data 比对结果数据
 * @param debug 是否启用调试模式
 * @returns 转换后的数据
 */
export function transformComparisonResult(data: any, debug: boolean = false): any {
  console.log('🔄 transformComparisonResult 开始转换:', data);

  const result = DataTransformUtil.snakeToCamel(data, {
    debug: debug,
    strict: false,
    maxDepth: 10
  });

  if (result.warnings.length > 0) {
    console.warn('⚠️ 比对结果数据转换警告:', result.warnings);
  }

  if (result.hasErrors) {
    console.error('❌ 比对结果数据转换失败:', result.warnings);
    throw new Error(`数据转换失败: ${result.warnings.join(', ')}`);
  }

  console.log('✅ transformComparisonResult 转换完成:', result.data);

  // 验证关键字段
  if (result.data && typeof result.data === 'object') {
    const validation = validateComparisonResultStructure(result.data);
    if (!validation.isValid) {
      console.warn('⚠️ 比对结果数据结构验证警告:', validation);
    }
  }

  return result.data;
}

/**
 * 验证比对结果数据结构
 * @param data 转换后的数据
 * @returns 验证结果
 */
function validateComparisonResultStructure(data: any): {
  isValid: boolean;
  missingFields: string[];
  issues: string[];
} {
  const issues: string[] = [];
  const requiredFields = ['taskId', 'status'];

  const missingFields = requiredFields.filter(field => !(field in data));

  // 检查 summary 字段结构
  if (data.summary && typeof data.summary === 'object') {
    const summaryFields = ['totalRecords', 'matchedRecords', 'diffRecords'];
    const missingSummaryFields = summaryFields.filter(field => !(field in data.summary));

    if (missingSummaryFields.length > 0) {
      issues.push(`summary 缺少字段: ${missingSummaryFields.join(', ')}`);
    }

    // 检查数值字段
    summaryFields.forEach(field => {
      if (field in data.summary && typeof data.summary[field] !== 'number') {
        issues.push(`summary.${field} 应该是数字类型，当前是: ${typeof data.summary[field]}`);
      }
    });
  } else if ('summary' in data) {
    issues.push('summary 字段应该是对象类型');
  }

  return {
    isValid: missingFields.length === 0 && issues.length === 0,
    missingFields,
    issues
  };
}

/**
 * 快速转换任务数据（snake_case 到 camelCase）
 * @param data 任务数据
 * @returns 转换后的数据
 */
export function transformTaskData(data: any): any {
  const result = DataTransformUtil.snakeToCamel(data, { debug: false });

  if (result.warnings.length > 0) {
    console.warn('🔄 任务数据转换警告:', result.warnings);
  }

  return result.data;
}

/**
 * 快速转换连接数据（snake_case 到 camelCase）
 * @param data 连接数据
 * @returns 转换后的数据
 */
export function transformConnectionData(data: any): any {
  const result = DataTransformUtil.snakeToCamel(data, { debug: false });

  if (result.warnings.length > 0) {
    console.warn('🔄 连接数据转换警告:', result.warnings);
  }

  return result.data;
}

export default DataTransformUtil;
