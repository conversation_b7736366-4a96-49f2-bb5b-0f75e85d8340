"""
URL configuration for arkdrf project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.urls import path, include, re_path
from django.contrib import admin
from django.views.generic.base import RedirectView, TemplateView
from utils.authenticator import CustomTokenRefreshView
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve
from rest_framework.documentation import include_docs_urls
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView

urlpatterns = [
    path("admin/", admin.site.urls),
    path("", TemplateView.as_view(template_name="index.html")),
    path("api/", include("rest_framework.urls")),
    path("api/system/", include("apps.system.urls")),    # 添加系统API
    path("api/user/", include("apps.user.urls")),        # 添加用户API
    path("api/monitor/", include("apps.monitor.urls")),  # 添加监控API
    path("api/test/", include("apps.functiontest.urls")),  # 添加功能测试API
    path("api/database/", include("apps.database.urls")),  # 添加数据库比对API
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    path("api/token/refresh/", CustomTokenRefreshView.as_view(), name="token_refresh"),
    path('', RedirectView.as_view(url='/api/'), name='root'),
    path('favicon.ico', RedirectView.as_view(url='/static/favicon.ico')),
    path('docs/', include_docs_urls(title='ArkReplay API')),
    re_path(r'^static/(?P<path>.*)$', serve, {'document_root': settings.STATIC_ROOT}),
    re_path(r'^(?!api)(?!admin)(?!static)(?!media)(?!docs).*$', TemplateView.as_view(template_name='index.html')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
