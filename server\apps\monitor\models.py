from django.db import models
from utils.models import BaseModel


# Create your models here.
class LoginLog(BaseModel):
    LOGIN_TYPE_CHOICES = ((1, "后台登录"),)
    username = models.Char<PERSON>ield(max_length=32, verbose_name="登录用户名", null=True, blank=True, help_text="登录用户名")
    ip = models.CharField(max_length=32, verbose_name="登录ip", null=True, blank=True, help_text="登录ip")
    agent = models.CharField(max_length=1500, verbose_name="agent信息", null=True, blank=True, help_text="agent信息")
    browser = models.CharField(max_length=200, verbose_name="浏览器名", null=True, blank=True, help_text="浏览器名")
    os = models.CharField(max_length=150, verbose_name="操作系统", null=True, blank=True, help_text="操作系统")
    login_type = models.IntegerField(default=1, choices=LOGIN_TYPE_CHOICES, verbose_name="登录类型", help_text="登录类型")
    status = models.BooleanField(default=True, verbose_name="登录状态", help_text="登录状态")

    class Meta:
        verbose_name = "登录日志"
        verbose_name_plural = verbose_name
        ordering = ("-create_time",)
    
    def __str__(self):
        return self.username

class OperationLog(BaseModel):
    request_modular = models.CharField(max_length=64, verbose_name="请求模块", null=True, blank=True, help_text="请求模块")
    request_path = models.TextField(verbose_name="请求地址", null=True, blank=True, help_text="请求地址")
    request_body = models.JSONField(verbose_name="请求参数", null=True, blank=True, help_text="请求参数")
    request_method = models.CharField(max_length=8, verbose_name="请求方式", null=True, blank=True, help_text="请求方式")
    request_msg = models.TextField(verbose_name="操作说明", null=True, blank=True, help_text="操作说明")
    request_ip = models.CharField(max_length=32, verbose_name="请求ip地址", null=True, blank=True, help_text="请求ip地址")
    request_browser = models.CharField(max_length=64, verbose_name="请求浏览器", null=True, blank=True, help_text="请求浏览器")
    response_code = models.CharField(max_length=32, verbose_name="响应状态码", null=True, blank=True, help_text="响应状态码")
    request_os = models.CharField(max_length=64, verbose_name="操作系统", null=True, blank=True, help_text="操作系统")
    json_result = models.JSONField(verbose_name="返回信息", null=True, blank=True, help_text="返回信息")
    status = models.BooleanField(default=False, verbose_name="响应状态", help_text="响应状态")
    creator = models.ForeignKey("user.User", on_delete=models.SET_NULL, null=True, blank=True, verbose_name="操作人", help_text="操作人")

    class Meta:
        verbose_name = '操作日志'
        verbose_name_plural = verbose_name
        ordering = ('-create_time',)
    
    def __str__(self):
        return self.request_modular
