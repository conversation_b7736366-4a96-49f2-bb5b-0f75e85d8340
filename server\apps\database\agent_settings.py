"""
Agent标准架构的Django设置配置
配置Django以使用Agent标准模型和通信设置
"""

# Agent通信配置
AGENT_BASE_URL = 'http://localhost:8001'  # Agent服务地址
AGENT_TIMEOUT = 30                        # Agent请求超时时间（秒）
AGENT_RETRY_COUNT = 3                     # Agent请求重试次数
AGENT_RETRY_DELAY = 1                     # Agent请求重试延迟（秒）

# Agent健康检查配置
AGENT_HEALTH_CHECK_INTERVAL = 60          # 健康检查间隔（秒）
AGENT_HEALTH_CHECK_TIMEOUT = 10           # 健康检查超时（秒）

# 数据库配置（与Agent共享）
# 确保Django和Agent使用相同的数据库
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'arkreplay',
        'USER': 'postgres',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'charset': 'utf8',
        },
    }
}

# Agent标准模型配置
AGENT_STANDARD_MODELS = {
    'USE_AGENT_MODELS': True,              # 是否使用Agent标准模型
    'READONLY_MODE': True,                 # Django是否为只读模式
    'AUTO_MIGRATE': False,                 # 是否自动迁移（由Agent管理表结构）
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'agent_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/agent_communication.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'apps.database.services.agent_communication': {
            'handlers': ['agent_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps.database.views_agent_standard': {
            'handlers': ['agent_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 50,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS配置（如果前端和后端分离）
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # Vue.js开发服务器
    "http://127.0.0.1:3000",
]

CORS_ALLOW_CREDENTIALS = True

# 缓存配置（用于Agent数据缓存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'arkreplay_agent',
        'TIMEOUT': 300,  # 5分钟缓存
    }
}

# Celery配置（用于异步任务）
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Shanghai'

# Agent任务配置
AGENT_TASK_CONFIG = {
    'MAX_CONCURRENT_TASKS': 5,             # 最大并发任务数
    'TASK_TIMEOUT': 3600,                  # 任务超时时间（秒）
    'PROGRESS_UPDATE_INTERVAL': 5,         # 进度更新间隔（秒）
    'RESULT_RETENTION_DAYS': 30,           # 结果保留天数
    'AUTO_CLEANUP_ENABLED': True,          # 是否自动清理
}

# 监控配置
MONITORING_CONFIG = {
    'ENABLE_METRICS': True,                # 是否启用指标收集
    'METRICS_INTERVAL': 60,                # 指标收集间隔（秒）
    'ALERT_THRESHOLDS': {
        'task_failure_rate': 0.1,          # 任务失败率阈值
        'agent_response_time': 10,          # Agent响应时间阈值（秒）
        'database_connection_errors': 5,    # 数据库连接错误阈值
    },
}

# 安全配置
SECURITY_CONFIG = {
    'ENCRYPT_PASSWORDS': True,             # 是否加密数据库密码
    'PASSWORD_ENCRYPTION_KEY': 'your-encryption-key',  # 密码加密密钥
    'API_RATE_LIMIT': '100/hour',          # API速率限制
    'ENABLE_AUDIT_LOG': True,              # 是否启用审计日志
}

# 开发环境特殊配置
if DEBUG:
    # 开发环境下的特殊设置
    AGENT_BASE_URL = 'http://localhost:8001'
    AGENT_TIMEOUT = 60  # 开发环境下延长超时时间
    
    # 开发环境日志配置
    LOGGING['handlers']['console']['level'] = 'DEBUG'
    LOGGING['loggers']['apps.database.services.agent_communication']['level'] = 'DEBUG'
    
    # 开发环境下禁用缓存
    CACHES['default']['TIMEOUT'] = 1

# 生产环境配置
else:
    # 生产环境下的安全设置
    AGENT_BASE_URL = 'http://agent-service:8001'  # 生产环境Agent地址
    AGENT_TIMEOUT = 30
    
    # 生产环境日志配置
    LOGGING['handlers']['agent_file']['filename'] = '/var/log/arkreplay/agent_communication.log'
    
    # 生产环境缓存配置
    CACHES['default']['LOCATION'] = 'redis://redis-service:6379/1'
