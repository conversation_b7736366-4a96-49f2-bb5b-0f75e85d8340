/**
 * 数据库工具函数
 */

// TagType是Element Plus的标签类型，包括 '' | 'success' | 'warning' | 'info' | 'danger'
type TagType = '' | 'primary' | 'success' | 'warning' | 'info' | 'danger';

/**
 * 数据库类型相关常量
 */
export const DB_TYPES = {
  MYSQL: 'mysql',
  ORACLE: 'oracle',
  POSTGRESQL: 'postgresql',
  SQLSERVER: 'sqlserver',
  DB2: 'db2',
  GAUSSDB: 'gaussdb'
};

/**
 * 获取数据库类型对应的标签类型
 * @param type 数据库类型
 * @returns ElTag支持的type类型
 */
export function getDbTypeColor(type?: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' | undefined {
  if (!type) return undefined;

  const tagMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    [DB_TYPES.MYSQL]: 'primary',
    [DB_TYPES.ORACLE]: 'warning',
    [DB_TYPES.POSTGRESQL]: 'success',
    [DB_TYPES.SQLSERVER]: 'info',
    [DB_TYPES.DB2]: 'warning',
    [DB_TYPES.GAUSSDB]: 'success'
  };

  return tagMap[String(type).toLowerCase()];
}

/**
 * 获取数据库类型的显示名称
 * @param type 数据库类型
 * @returns 数据库类型显示名称
 */
export function getDbTypeName(type?: string): string {
  if (!type) return '未知';

  const nameMap: Record<string, string> = {
    [DB_TYPES.MYSQL]: 'MySQL',
    [DB_TYPES.ORACLE]: 'Oracle',
    [DB_TYPES.POSTGRESQL]: 'PostgreSQL',
    [DB_TYPES.SQLSERVER]: 'SQL Server',
    [DB_TYPES.DB2]: 'DB2',
    [DB_TYPES.GAUSSDB]: 'GaussDB'
  };

  return nameMap[String(type).toLowerCase()] || type;
}

/**
 * 获取数据库默认端口
 * @param type 数据库类型
 * @returns 默认端口号
 */
export function getDefaultDbPort(type?: string): number {
  if (!type) return 3306;

  const portMap: Record<string, number> = {
    [DB_TYPES.MYSQL]: 3306,
    [DB_TYPES.ORACLE]: 1521,
    [DB_TYPES.POSTGRESQL]: 5432,
    [DB_TYPES.SQLSERVER]: 1433,
    [DB_TYPES.DB2]: 50000,
    [DB_TYPES.GAUSSDB]: 5432
  };

  return portMap[String(type).toLowerCase()] || 3306;
} 