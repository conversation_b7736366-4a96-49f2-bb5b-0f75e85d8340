"""
解决模型冲突管理命令
将现有的冲突模型数据迁移到统一的桥接模型

使用方法：
python manage.py resolve_model_conflicts --analyze          # 分析冲突
python manage.py resolve_model_conflicts --migrate          # 执行迁移
python manage.py resolve_model_conflicts --verify           # 验证迁移结果
python manage.py resolve_model_conflicts --cleanup          # 清理旧数据
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction, connection
from django.apps import apps
from apps.database.models import CompareModel, TableConfig, CompareTask, DataConnection
from apps.database.models_bridge import (
    ComparisonConnection, 
    ComparisonModel, 
    ComparisonTableRule, 
    ComparisonTask as BridgeComparisonTask
)
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '解决SQLAlchemy和Django模型冲突，迁移到统一桥接模型'

    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze',
            action='store_true',
            help='分析当前模型冲突情况'
        )
        
        parser.add_argument(
            '--migrate',
            action='store_true',
            help='执行数据迁移到桥接模型'
        )
        
        parser.add_argument(
            '--verify',
            action='store_true',
            help='验证迁移结果'
        )
        
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='清理旧模型数据（谨慎使用）'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='预览模式，不实际执行'
        )

    def handle(self, *args, **options):
        """命令处理入口"""
        self.dry_run = options['dry_run']
        
        if options['analyze']:
            self.analyze_conflicts()
        elif options['migrate']:
            self.migrate_data()
        elif options['verify']:
            self.verify_migration()
        elif options['cleanup']:
            self.cleanup_old_data()
        else:
            self.stdout.write(
                self.style.WARNING('请指定操作：--analyze, --migrate, --verify, 或 --cleanup')
            )

    def analyze_conflicts(self):
        """分析模型冲突"""
        self.stdout.write(self.style.SUCCESS('=== 模型冲突分析 ==='))
        
        conflicts = []
        
        # 检查表名冲突
        with connection.cursor() as cursor:
            # 检查是否存在旧表
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name LIKE 'database_%'
            """)
            old_tables = [row[0] for row in cursor.fetchall()]
            
            # 检查是否存在新表
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name LIKE 'comparison_%'
            """)
            new_tables = [row[0] for row in cursor.fetchall()]
        
        if old_tables:
            conflicts.append(f"发现旧模型表: {', '.join(old_tables)}")
        
        if new_tables:
            conflicts.append(f"发现新桥接表: {', '.join(new_tables)}")
        
        # 检查数据量
        old_data_counts = {
            'DataConnection': DataConnection.objects.count(),
            'CompareModel': CompareModel.objects.count(),
            'TableConfig': TableConfig.objects.count(),
            'CompareTask': CompareTask.objects.count(),
        }
        
        new_data_counts = {
            'ComparisonConnection': ComparisonConnection.objects.count(),
            'ComparisonModel': ComparisonModel.objects.count(),
            'ComparisonTableRule': ComparisonTableRule.objects.count(),
            'BridgeComparisonTask': BridgeComparisonTask.objects.count(),
        }
        
        self.stdout.write('\n旧模型数据统计:')
        for model, count in old_data_counts.items():
            self.stdout.write(f'  {model}: {count} 条记录')
            if count > 0:
                conflicts.append(f"{model}有{count}条数据需要迁移")
        
        self.stdout.write('\n新桥接模型数据统计:')
        for model, count in new_data_counts.items():
            self.stdout.write(f'  {model}: {count} 条记录')
        
        # 检查字段冲突
        field_conflicts = self._check_field_conflicts()
        conflicts.extend(field_conflicts)
        
        if conflicts:
            self.stdout.write(self.style.ERROR('\n发现以下冲突:'))
            for i, conflict in enumerate(conflicts, 1):
                self.stdout.write(f'  {i}. {conflict}')
            
            self.stdout.write(self.style.WARNING('\n建议执行: python manage.py resolve_model_conflicts --migrate'))
        else:
            self.stdout.write(self.style.SUCCESS('\n未发现严重冲突'))

    def _check_field_conflicts(self):
        """检查字段级冲突"""
        conflicts = []
        
        # 检查连接字段类型冲突
        if CompareModel.objects.exists():
            sample = CompareModel.objects.first()
            if isinstance(sample.source_connection, str):
                conflicts.append("CompareModel使用字符串存储连接关系，与桥接模型的外键设计冲突")
        
        # 检查状态枚举冲突
        if CompareTask.objects.exists():
            unique_statuses = set(CompareTask.objects.values_list('status', flat=True))
            bridge_statuses = {'pending', 'running', 'completed', 'failed', 'cancelled'}
            
            conflicting_statuses = unique_statuses - bridge_statuses
            if conflicting_statuses:
                conflicts.append(f"任务状态枚举冲突: {conflicting_statuses}")
        
        return conflicts

    def migrate_data(self):
        """执行数据迁移"""
        self.stdout.write(self.style.SUCCESS('=== 开始数据迁移 ==='))
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('预览模式 - 不会实际执行'))
        
        try:
            with transaction.atomic():
                if self.dry_run:
                    transaction.set_rollback(True)
                
                # 1. 迁移数据库连接
                conn_count = self._migrate_connections()
                self.stdout.write(f'✓ 迁移数据库连接: {conn_count} 条')
                
                # 2. 迁移比对模型
                model_count = self._migrate_models()
                self.stdout.write(f'✓ 迁移比对模型: {model_count} 条')
                
                # 3. 迁移表规则
                rule_count = self._migrate_table_rules()
                self.stdout.write(f'✓ 迁移表规则: {rule_count} 条')
                
                # 4. 迁移比对任务
                task_count = self._migrate_tasks()
                self.stdout.write(f'✓ 迁移比对任务: {task_count} 条')
                
                if not self.dry_run:
                    self.stdout.write(self.style.SUCCESS('\n数据迁移完成'))
                else:
                    self.stdout.write(self.style.WARNING('\n预览完成 - 未实际执行'))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'迁移失败: {str(e)}'))
            raise CommandError(f'数据迁移失败: {str(e)}')

    def _migrate_connections(self):
        """迁移数据库连接"""
        count = 0
        
        for old_conn in DataConnection.objects.all():
            # 检查是否已存在
            if ComparisonConnection.objects.filter(name=old_conn.name).exists():
                continue
            
            if not self.dry_run:
                ComparisonConnection.objects.create(
                    name=old_conn.name,
                    type=old_conn.type,
                    host=old_conn.host,
                    port=old_conn.port,
                    username=old_conn.username,
                    password=old_conn.password or '',
                    database=old_conn.database,
                    params=old_conn.parameters,  # parameters -> params
                    status=old_conn.status,
                    create_time=old_conn.create_time,
                    update_time=old_conn.update_time
                )
            count += 1
        
        return count

    def _migrate_models(self):
        """迁移比对模型"""
        count = 0
        
        for old_model in CompareModel.objects.all():
            # 检查是否已存在
            if ComparisonModel.objects.filter(name=old_model.name).exists():
                continue
            
            # 查找对应的连接
            source_conn = None
            target_conn = None
            
            if old_model.source_connection:
                try:
                    # 尝试按ID查找
                    source_conn = ComparisonConnection.objects.get(id=int(old_model.source_connection))
                except (ValueError, ComparisonConnection.DoesNotExist):
                    # 尝试按名称查找
                    try:
                        source_conn = ComparisonConnection.objects.get(name=old_model.source_connection)
                    except ComparisonConnection.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f'跳过模型 {old_model.name}: 未找到源连接 {old_model.source_connection}')
                        )
                        continue
            
            if old_model.target_connection:
                try:
                    target_conn = ComparisonConnection.objects.get(id=int(old_model.target_connection))
                except (ValueError, ComparisonConnection.DoesNotExist):
                    try:
                        target_conn = ComparisonConnection.objects.get(name=old_model.target_connection)
                    except ComparisonConnection.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f'跳过模型 {old_model.name}: 未找到目标连接 {old_model.target_connection}')
                        )
                        continue
            
            if not source_conn or not target_conn:
                continue
            
            if not self.dry_run:
                ComparisonModel.objects.create(
                    name=old_model.name,
                    description=old_model.description,
                    source_connid=source_conn,
                    target_connid=target_conn,
                    cmp_type='content',  # 默认值
                    status=old_model.status,
                    create_time=old_model.create_time,
                    update_time=old_model.update_time
                )
            count += 1
        
        return count

    def _migrate_table_rules(self):
        """迁移表规则"""
        count = 0
        
        for old_config in TableConfig.objects.all():
            # 查找对应的比对模型
            try:
                bridge_model = ComparisonModel.objects.get(name=old_config.model.name)
            except ComparisonModel.DoesNotExist:
                continue
            
            # 检查是否已存在
            if ComparisonTableRule.objects.filter(
                model=bridge_model, 
                table_id=old_config.table_id
            ).exists():
                continue
            
            if not self.dry_run:
                ComparisonTableRule.objects.create(
                    model=bridge_model,
                    table_id=old_config.table_id,
                    table_name=old_config.table_id,  # 使用table_id作为table_name
                    sql_1=old_config.sql_1,
                    sql_2=old_config.sql_2,
                    remark=old_config.remark,
                    create_time=old_config.create_time,
                    update_time=old_config.update_time
                )
            count += 1
        
        return count

    def _migrate_tasks(self):
        """迁移比对任务"""
        count = 0
        
        # 状态映射
        status_mapping = {
            'waiting': 'pending',
            'running': 'running',
            'success': 'completed',
            'failed': 'failed',
            'stopped': 'cancelled'
        }
        
        for old_task in CompareTask.objects.all():
            # 生成唯一task_id
            task_id = f"migrated_{old_task.id}_{old_task.create_time.strftime('%Y%m%d_%H%M%S')}"
            
            # 检查是否已存在
            if BridgeComparisonTask.objects.filter(task_id=task_id).exists():
                continue
            
            # 查找对应的模型
            bridge_model = None
            if old_task.model:
                try:
                    bridge_model = ComparisonModel.objects.get(name=old_task.model.name)
                except ComparisonModel.DoesNotExist:
                    pass
            
            if not self.dry_run:
                BridgeComparisonTask.objects.create(
                    task_id=task_id,
                    user_id='system',  # 默认用户
                    model=bridge_model,
                    task_name=old_task.name,
                    status=status_mapping.get(old_task.status, 'pending'),
                    progress_pct=old_task.progress,
                    total_records=old_task.total_records,
                    processed_records=old_task.processed_records,
                    diff_records=old_task.different_records,
                    source_only=old_task.source_only_records,
                    target_only=old_task.target_only_records,
                    exec_time=old_task.execution_time,
                    start_time=old_task.start_time,
                    complete_time=old_task.end_time,
                    error_msg=old_task.message,
                    create_time=old_task.create_time
                )
            count += 1
        
        return count

    def verify_migration(self):
        """验证迁移结果"""
        self.stdout.write(self.style.SUCCESS('=== 验证迁移结果 ==='))
        
        # 统计数据
        old_counts = {
            'DataConnection': DataConnection.objects.count(),
            'CompareModel': CompareModel.objects.count(),
            'TableConfig': TableConfig.objects.count(),
            'CompareTask': CompareTask.objects.count(),
        }
        
        new_counts = {
            'ComparisonConnection': ComparisonConnection.objects.count(),
            'ComparisonModel': ComparisonModel.objects.count(),
            'ComparisonTableRule': ComparisonTableRule.objects.count(),
            'BridgeComparisonTask': BridgeComparisonTask.objects.count(),
        }
        
        self.stdout.write('迁移前后数据对比:')
        for (old_name, old_count), (new_name, new_count) in zip(old_counts.items(), new_counts.items()):
            status = '✓' if new_count >= old_count else '✗'
            self.stdout.write(f'  {status} {old_name}({old_count}) -> {new_name}({new_count})')
        
        # 验证数据完整性
        self._verify_data_integrity()

    def _verify_data_integrity(self):
        """验证数据完整性"""
        issues = []
        
        # 检查外键关系
        for model in ComparisonModel.objects.all():
            if not model.source_connid or not model.target_connid:
                issues.append(f"模型 {model.name} 缺少连接关系")
        
        # 检查表规则关联
        orphaned_rules = ComparisonTableRule.objects.filter(model__isnull=True).count()
        if orphaned_rules > 0:
            issues.append(f"发现 {orphaned_rules} 个孤立的表规则")
        
        if issues:
            self.stdout.write(self.style.ERROR('发现数据完整性问题:'))
            for issue in issues:
                self.stdout.write(f'  - {issue}')
        else:
            self.stdout.write(self.style.SUCCESS('数据完整性验证通过'))

    def cleanup_old_data(self):
        """清理旧数据"""
        if not self.dry_run:
            confirm = input('确认要删除旧模型数据吗？这个操作不可逆！(yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write('操作已取消')
                return
        
        self.stdout.write(self.style.WARNING('=== 清理旧数据 ==='))
        
        if self.dry_run:
            self.stdout.write('预览模式 - 将要删除的数据:')
        
        # 删除顺序很重要，避免外键约束错误
        models_to_clean = [
            ('CompareTask', CompareTask),
            ('TableConfig', TableConfig),
            ('CompareModel', CompareModel),
            ('DataConnection', DataConnection),
        ]
        
        for name, model_class in models_to_clean:
            count = model_class.objects.count()
            if count > 0:
                if not self.dry_run:
                    model_class.objects.all().delete()
                    self.stdout.write(f'✓ 删除 {name}: {count} 条记录')
                else:
                    self.stdout.write(f'  将删除 {name}: {count} 条记录')
        
        if not self.dry_run:
            self.stdout.write(self.style.SUCCESS('旧数据清理完成'))
        else:
            self.stdout.write('预览完成')
