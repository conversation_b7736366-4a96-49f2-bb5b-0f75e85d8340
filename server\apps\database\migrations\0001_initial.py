# Generated by Django 5.1 on 2025-05-15 12:23

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CompareModel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模型名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('source_connection', models.CharField(blank=True, max_length=100, null=True, verbose_name='源连接')),
                ('target_connection', models.CharField(blank=True, max_length=100, null=True, verbose_name='目标连接')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '比对模型',
                'verbose_name_plural': '比对模型',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='DataConnection',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='连接名称')),
                ('type', models.CharField(choices=[('db2', 'DB2'), ('gaussdb', 'GaussDB'), ('mysql', 'MySQL'), ('postgresql', 'PostgreSQL'), ('oracle', 'Oracle'), ('sqlserver', 'SQL Server')], default='db2', max_length=20, verbose_name='数据库类型')),
                ('host', models.CharField(max_length=255, verbose_name='主机')),
                ('port', models.IntegerField(verbose_name='端口')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('password', models.CharField(blank=True, max_length=255, null=True, verbose_name='密码')),
                ('database', models.CharField(max_length=100, verbose_name='数据库名')),
                ('parameters', models.JSONField(blank=True, null=True, verbose_name='连接参数')),
                ('status', models.CharField(choices=[('active', '正常'), ('inactive', '禁用')], default='active', max_length=20, verbose_name='状态')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '数据库连接',
                'verbose_name_plural': '数据库连接',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='CompareTask',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=100, null=True, verbose_name='任务名称')),
                ('status', models.CharField(choices=[('waiting', '等待中'), ('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('stopped', '已停止')], default='waiting', max_length=20, verbose_name='状态')),
                ('progress', models.IntegerField(default=0, verbose_name='进度')),
                ('total_records', models.IntegerField(default=0, verbose_name='总记录数')),
                ('processed_records', models.IntegerField(default=0, verbose_name='已处理记录数')),
                ('matched_records', models.IntegerField(default=0, verbose_name='匹配记录数')),
                ('different_records', models.IntegerField(default=0, verbose_name='差异记录数')),
                ('error_records', models.IntegerField(default=0, verbose_name='错误记录数')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('message', models.TextField(blank=True, null=True, verbose_name='消息')),
                ('result', models.JSONField(blank=True, null=True, verbose_name='任务结果')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('external_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='外部任务ID')),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='database.comparemodel', verbose_name='比对模型')),
            ],
            options={
                'verbose_name': '比对任务',
                'verbose_name_plural': '比对任务',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='CompareDiff',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('table_name', models.CharField(max_length=100, verbose_name='表名')),
                ('field_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='字段名')),
                ('record_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='记录ID')),
                ('diff_type', models.CharField(choices=[('value', '值不同'), ('source_missing', '源端缺失'), ('target_missing', '目标端缺失'), ('type', '类型不同')], max_length=20, verbose_name='差异类型')),
                ('severity', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], default='medium', max_length=20, verbose_name='严重程度')),
                ('source_value', models.TextField(blank=True, null=True, verbose_name='源值')),
                ('target_value', models.TextField(blank=True, null=True, verbose_name='目标值')),
                ('context', models.JSONField(blank=True, null=True, verbose_name='上下文')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diffs', to='database.comparetask', verbose_name='比对任务')),
            ],
            options={
                'verbose_name': '比对差异',
                'verbose_name_plural': '比对差异',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='TableConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('table_id', models.CharField(max_length=50, verbose_name='表ID')),
                ('remark', models.CharField(blank=True, max_length=255, null=True, verbose_name='备注')),
                ('sql_1', models.TextField(verbose_name='源SQL')),
                ('sql_2', models.TextField(verbose_name='目标SQL')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tables', to='database.comparemodel', verbose_name='比对模型')),
            ],
            options={
                'verbose_name': '表配置',
                'verbose_name_plural': '表配置',
                'ordering': ['id'],
            },
        ),
    ]
