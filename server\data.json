[{"model": "auth.permission", "pk": 1, "fields": {"name": "Can add permission", "content_type": 1, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change permission", "content_type": 1, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete permission", "content_type": 1, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view permission", "content_type": 1, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add group", "content_type": 2, "codename": "add_group"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change group", "content_type": 2, "codename": "change_group"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete group", "content_type": 2, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view group", "content_type": 2, "codename": "view_group"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add log entry", "content_type": 3, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change log entry", "content_type": 3, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete log entry", "content_type": 3, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view log entry", "content_type": 3, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add user", "content_type": 6, "codename": "add_user"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change user", "content_type": 6, "codename": "change_user"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete user", "content_type": 6, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view user", "content_type": 6, "codename": "view_user"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add 菜单/权限", "content_type": 7, "codename": "add_menu"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change 菜单/权限", "content_type": 7, "codename": "change_menu"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete 菜单/权限", "content_type": 7, "codename": "delete_menu"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view 菜单/权限", "content_type": 7, "codename": "view_menu"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add Menu meta", "content_type": 8, "codename": "add_menumeta"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change Menu meta", "content_type": 8, "codename": "change_menumeta"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete Menu meta", "content_type": 8, "codename": "delete_menumeta"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view Menu meta", "content_type": 8, "codename": "view_menumeta"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add 角色表", "content_type": 9, "codename": "add_role"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change 角色表", "content_type": 9, "codename": "change_role"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete 角色表", "content_type": 9, "codename": "delete_role"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view 角色表", "content_type": 9, "codename": "view_role"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add 部门", "content_type": 10, "codename": "add_deptinfo"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change 部门", "content_type": 10, "codename": "change_deptinfo"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete 部门", "content_type": 10, "codename": "delete_deptinfo"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view 部门", "content_type": 10, "codename": "view_deptinfo"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add 登录日志", "content_type": 11, "codename": "add_loginlog"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change 登录日志", "content_type": 11, "codename": "change_loginlog"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete 登录日志", "content_type": 11, "codename": "delete_loginlog"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view 登录日志", "content_type": 11, "codename": "view_loginlog"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add 操作日志", "content_type": 12, "codename": "add_operationlog"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change 操作日志", "content_type": 12, "codename": "change_operationlog"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete 操作日志", "content_type": 12, "codename": "delete_operationlog"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view 操作日志", "content_type": 12, "codename": "view_operationlog"}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "user", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "system", "model": "menu"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "system", "model": "menumeta"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "system", "model": "role"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "system", "model": "deptinfo"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "monitor", "model": "loginlog"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "monitor", "model": "operationlog"}}, {"model": "user.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$870000$lVzs6bBz8msSwjJTHaSCCM$U0BHxforQcXnnEjv0f8jWAeVQXjxHy7T5+KES3EWaTw=", "last_login": "2024-12-02T07:12:48.964Z", "avatar": null, "nickname": "admin", "username": "admin", "email": "<EMAIL>", "status": true, "is_staff": true, "is_superuser": true, "create_time": "2024-09-06T05:38:36.315Z", "dept": "4c76aef7-7c65-4505-a614-e7b2b2c5b2af", "groups": [], "user_permissions": [], "role": ["b3223970-2c59-444a-8e21-153a94afe909"]}}, {"model": "user.user", "pk": 17, "fields": {"password": "pbkdf2_sha256$870000$ceLOVDqDIhXsLljITeygAx$Q/xxZFyhNwrPl4pbpFcVRkT6I+tnr4H1dJsNkm+6zrw=", "last_login": null, "avatar": null, "nickname": "用户管理员", "username": "平台用户管理员", "email": "<EMAIL>", "status": true, "is_staff": false, "is_superuser": false, "create_time": "2024-10-25T03:53:17.329Z", "dept": "4c76aef7-7c65-4505-a614-e7b2b2c5b2af", "groups": [], "user_permissions": [], "role": ["9a4b7173-beb2-4544-a1e6-2fe4c1aa365e"]}}, {"model": "system.role", "pk": "63908cae-d489-4bbb-87f3-d50dd93db2ae", "fields": {"create_time": "2024-10-25T06:12:19.179Z", "update_time": "2024-10-25T06:12:19.179Z", "name": "管理员", "code": "admins", "status": true, "parent": null, "menu": []}}, {"model": "system.role", "pk": "9a4b7173-beb2-4544-a1e6-2fe4c1aa365e", "fields": {"create_time": "2024-10-25T06:13:00.814Z", "update_time": "2024-10-25T06:27:15.197Z", "name": "平台用户管理员", "code": "<PERSON><PERSON><PERSON>", "status": true, "parent": "63908cae-d489-4bbb-87f3-d50dd93db2ae", "menu": ["a17cb829-10be-4300-8ddc-e5b365d3ef38", "16e24578-c162-4928-9cc4-65596d8cb94a", "e3b423d3-8ad0-42b0-a612-516c93208fa8", "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "b7965934-3627-4c1d-a919-d52bb61212a9", "f5c282a6-9ac4-4b8a-965a-d1d98a7178ab", "cd6d7efd-010f-497b-8f84-fc40ed4931b2", "a75c5a78-f5ff-4366-9dfb-4c27d703dbf9", "fd445ccb-f5ec-4dbc-b94b-2cdef097a3bd", "1ddacfca-c8e0-4971-812d-57338800e278", "90a7dd20-15b6-4792-ab24-6293081293d5"]}}, {"model": "system.role", "pk": "b3223970-2c59-444a-8e21-153a94afe909", "fields": {"create_time": "2024-09-08T14:33:34.107Z", "update_time": "2024-10-31T02:29:29.919Z", "name": "超级管理员", "code": "superadmin", "status": true, "parent": null, "menu": ["a17cb829-10be-4300-8ddc-e5b365d3ef38", "16e24578-c162-4928-9cc4-65596d8cb94a", "e3b423d3-8ad0-42b0-a612-516c93208fa8", "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "aee2c869-be1c-4ab2-b321-0c3e77c04160", "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "b7965934-3627-4c1d-a919-d52bb61212a9", "f5c282a6-9ac4-4b8a-965a-d1d98a7178ab", "1c5e1b60-ff5e-4aa4-b8b3-3fcab50340c7", "82b02bc1-07fc-4e4a-b48c-1906069bc931", "07ad99e6-eae4-4b0a-aa71-7673d8641238", "cfa44067-8699-4e5b-b40d-d3cd726c05c5", "5e54be04-b0e8-4bef-bf21-a11df0677285", "436077d9-7654-4d56-9f18-895d44bf2b6b", "c855293d-4f35-490e-b4e6-ea79eb4678ce", "fbf4bfce-5f53-44fc-8604-1a3f3bcf3b56", "9db4963f-a84e-419f-bbdf-3d2ef794af6f", "cd6d7efd-010f-497b-8f84-fc40ed4931b2", "a75c5a78-f5ff-4366-9dfb-4c27d703dbf9", "fd445ccb-f5ec-4dbc-b94b-2cdef097a3bd", "1ddacfca-c8e0-4971-812d-57338800e278", "90a7dd20-15b6-4792-ab24-6293081293d5", "44d7669a-87a2-430c-9fe8-95757d6f8e01", "de834396-885e-4c1c-a53c-a378a6875390", "0be68109-bf34-46e9-913f-b44135b9efa1", "dc368aa0-c7fd-4ec4-b17f-176ba2e3be73", "2570af26-944c-47dd-af41-a211d3ef02f2", "93134e5a-b4be-4f59-a5c3-29547d4809ab"]}}, {"model": "system.menu", "pk": "07ad99e6-eae4-4b0a-aa71-7673d8641238", "fields": {"create_time": "2024-10-25T02:30:52.099Z", "update_time": "2024-10-25T02:30:52.099Z", "parent": "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "menu_type": 2, "name": "新增", "code": "/api/system/menu/:add", "path": "/api/system/menu/", "component": null, "status": true, "meta": "242704c7-b5eb-4b3e-9581-23da9ede5c7e", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "0be68109-bf34-46e9-913f-b44135b9efa1", "fields": {"create_time": "2024-10-25T02:48:18.936Z", "update_time": "2024-10-25T02:48:18.936Z", "parent": "aee2c869-be1c-4ab2-b321-0c3e77c04160", "menu_type": 2, "name": "删除", "code": "/api/system/dept/:delete", "path": "/api/system/dept/", "component": null, "status": true, "meta": "2298b1d7-1eba-445a-8a4c-0a25a9e96056", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "16e24578-c162-4928-9cc4-65596d8cb94a", "fields": {"create_time": "2024-10-14T09:21:34.349Z", "update_time": "2024-10-14T13:30:40.431Z", "parent": "a17cb829-10be-4300-8ddc-e5b365d3ef38", "menu_type": 1, "name": "权限测试", "code": null, "path": "/test/permission", "component": "/test/permission/index", "status": true, "meta": "bd4b1268-db6b-4caf-bf7e-a8c2a697ed0c", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "1c5e1b60-ff5e-4aa4-b8b3-3fcab50340c7", "fields": {"create_time": "2024-10-24T07:47:47.529Z", "update_time": "2024-10-24T07:55:17.086Z", "parent": "16e24578-c162-4928-9cc4-65596d8cb94a", "menu_type": 2, "name": "查看", "code": "/api/test/permission/:read", "path": "/api/test/permission/", "component": null, "status": true, "meta": "9435b8e0-0e01-445f-9060-49537df8161f", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "1ddacfca-c8e0-4971-812d-57338800e278", "fields": {"create_time": "2024-10-25T02:47:56.316Z", "update_time": "2024-10-25T02:47:56.316Z", "parent": "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "menu_type": 2, "name": "删除", "code": "/api/user/:delete", "path": "/api/user/", "component": null, "status": true, "meta": "d6638fb6-9a8e-4063-9230-ec260cbf705b", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "2570af26-944c-47dd-af41-a211d3ef02f2", "fields": {"create_time": "2024-10-30T07:43:30.634Z", "update_time": "2024-10-31T02:22:31.816Z", "parent": "dc368aa0-c7fd-4ec4-b17f-176ba2e3be73", "menu_type": 1, "name": "登录日志", "code": null, "path": "/monitor/loginlog", "component": "/monitor/loginlog/index", "status": true, "meta": "6adc30be-629d-4410-b5b0-6476d2564298", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "3c2c9a41-4aaf-4afd-8cda-4c3b91be7c30", "fields": {"create_time": "2024-10-24T07:48:34.380Z", "update_time": "2024-10-24T08:28:08.511Z", "parent": "16e24578-c162-4928-9cc4-65596d8cb94a", "menu_type": 2, "name": "查看2", "code": "/api/test/permission2/:read", "path": "/api/test/permission2/", "component": null, "status": true, "meta": "ee3ae725-1376-4d39-900a-61c81a77d496", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "436077d9-7654-4d56-9f18-895d44bf2b6b", "fields": {"create_time": "2024-10-25T02:42:04.452Z", "update_time": "2024-10-25T02:42:04.452Z", "parent": "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "menu_type": 2, "name": "查看", "code": "/api/system/role/:read", "path": "/api/system/role/", "component": null, "status": true, "meta": "0ca55276-c54d-4d61-a817-1ca4037ed2c7", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "44d7669a-87a2-430c-9fe8-95757d6f8e01", "fields": {"create_time": "2024-10-25T02:48:18.914Z", "update_time": "2024-10-25T02:48:18.914Z", "parent": "aee2c869-be1c-4ab2-b321-0c3e77c04160", "menu_type": 2, "name": "新增", "code": "/api/system/dept/:add", "path": "/api/system/dept/", "component": null, "status": true, "meta": "3cfdb18a-3ed4-4193-8753-004d790843fd", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "5e54be04-b0e8-4bef-bf21-a11df0677285", "fields": {"create_time": "2024-10-25T02:30:52.121Z", "update_time": "2024-10-25T02:30:52.121Z", "parent": "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "menu_type": 2, "name": "删除", "code": "/api/system/menu/:delete", "path": "/api/system/menu/", "component": null, "status": true, "meta": "fded4dba-8f38-4fc3-b13b-74d11b9d6823", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "fields": {"create_time": "2024-10-14T14:04:37.888Z", "update_time": "2024-10-16T09:39:25.096Z", "parent": "e3b423d3-8ad0-42b0-a612-516c93208fa8", "menu_type": 1, "name": "用户管理", "code": null, "path": "/system/user", "component": "/user/index", "status": true, "meta": "4e161fb1-6f66-4e9b-a1d2-1231869890e0", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "82b02bc1-07fc-4e4a-b48c-1906069bc931", "fields": {"create_time": "2024-10-25T02:30:52.088Z", "update_time": "2024-10-25T02:30:52.088Z", "parent": "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "menu_type": 2, "name": "查看", "code": "/api/system/menu/:read", "path": "/api/system/menu/", "component": null, "status": true, "meta": "6bff4051-e205-4de1-b343-5e09dad6ea59", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "90a7dd20-15b6-4792-ab24-6293081293d5", "fields": {"create_time": "2024-10-25T02:48:18.900Z", "update_time": "2024-10-25T02:48:18.900Z", "parent": "aee2c869-be1c-4ab2-b321-0c3e77c04160", "menu_type": 2, "name": "查看", "code": "/api/system/dept/:read", "path": "/api/system/dept/", "component": null, "status": true, "meta": "bf93cd77-d279-4577-9845-6d52c71de464", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "93134e5a-b4be-4f59-a5c3-29547d4809ab", "fields": {"create_time": "2024-10-30T09:32:31.361Z", "update_time": "2024-10-30T09:32:31.361Z", "parent": "dc368aa0-c7fd-4ec4-b17f-176ba2e3be73", "menu_type": 1, "name": "操作日志", "code": null, "path": "/monitor/operationlog", "component": "/monitor/operationlog/index", "status": true, "meta": "bfcd1b2a-597b-419c-8b15-4d38d58ef8c9", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "9db4963f-a84e-419f-bbdf-3d2ef794af6f", "fields": {"create_time": "2024-10-25T02:42:04.482Z", "update_time": "2024-10-25T02:42:04.482Z", "parent": "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "menu_type": 2, "name": "删除", "code": "/api/system/role/:delete", "path": "/api/system/role/", "component": null, "status": true, "meta": "43a996b8-15f0-496f-933b-93bed0a887f2", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "a17cb829-10be-4300-8ddc-e5b365d3ef38", "fields": {"create_time": "2024-09-15T14:22:23.832Z", "update_time": "2024-10-14T13:55:08.497Z", "parent": null, "menu_type": 1, "name": "功能测试", "code": null, "path": "/test", "component": null, "status": true, "meta": "d19b1c05-a962-4923-b939-4471b52cdd60", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "a75c5a78-f5ff-4366-9dfb-4c27d703dbf9", "fields": {"create_time": "2024-10-25T02:47:56.291Z", "update_time": "2024-10-25T02:47:56.291Z", "parent": "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "menu_type": 2, "name": "新增", "code": "/api/user/:add", "path": "/api/user/", "component": null, "status": true, "meta": "c26066ad-cb55-4c49-ae27-5a74b5dc4361", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "aee2c869-be1c-4ab2-b321-0c3e77c04160", "fields": {"create_time": "2024-10-14T13:59:11.676Z", "update_time": "2024-10-16T09:40:23.503Z", "parent": "e3b423d3-8ad0-42b0-a612-516c93208fa8", "menu_type": 1, "name": "部门管理", "code": null, "path": "/system/department", "component": "/department/index", "status": true, "meta": "af4c154e-21ad-4216-b236-ae0da15a1fa1", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "b7965934-3627-4c1d-a919-d52bb61212a9", "fields": {"create_time": "2024-10-15T05:14:30.499Z", "update_time": "2024-10-15T05:37:08.697Z", "parent": null, "menu_type": 1, "name": "首页", "code": null, "path": "/", "component": null, "status": true, "meta": "8d78fd5e-05b0-4242-a6bc-ece333724fd6", "method": null, "redirect": "/home"}}, {"model": "system.menu", "pk": "c855293d-4f35-490e-b4e6-ea79eb4678ce", "fields": {"create_time": "2024-10-25T02:42:04.463Z", "update_time": "2024-10-25T02:42:04.463Z", "parent": "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "menu_type": 2, "name": "新增", "code": "/api/system/role/:add", "path": "/api/system/role/", "component": null, "status": true, "meta": "823ae329-bef9-47a4-a4ed-e56f0d19b584", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "cd6d7efd-010f-497b-8f84-fc40ed4931b2", "fields": {"create_time": "2024-10-25T02:47:56.271Z", "update_time": "2024-10-25T02:47:56.271Z", "parent": "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "menu_type": 2, "name": "查看", "code": "/api/user/:read", "path": "/api/user/", "component": null, "status": true, "meta": "58b9dacf-edb9-477b-a625-c89a806f1af6", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "cfa44067-8699-4e5b-b40d-d3cd726c05c5", "fields": {"create_time": "2024-10-25T02:30:52.108Z", "update_time": "2024-10-25T02:30:52.108Z", "parent": "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "menu_type": 2, "name": "修改", "code": "/api/system/menu/:change", "path": "/api/system/menu/", "component": null, "status": true, "meta": "d0a38a0d-611d-469e-8741-f07f3c84e910", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "d5fbdf9f-3705-4fba-b91c-f931dcdbe123", "fields": {"create_time": "2024-10-14T13:52:42.273Z", "update_time": "2024-10-16T09:38:15.818Z", "parent": "e3b423d3-8ad0-42b0-a612-516c93208fa8", "menu_type": 1, "name": "菜单权限", "code": null, "path": "/system/permission", "component": "/system/permission/index", "status": true, "meta": "79c28ca9-5901-4d80-802f-e80b8fe88f54", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "dc368aa0-c7fd-4ec4-b17f-176ba2e3be73", "fields": {"create_time": "2024-10-30T07:42:22.305Z", "update_time": "2024-10-30T07:42:22.305Z", "parent": null, "menu_type": 1, "name": "系统监控", "code": null, "path": "/monitor", "component": null, "status": true, "meta": "7c9e7404-00a1-4041-8af8-a590a070bc26", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "fields": {"create_time": "2024-10-14T14:00:01.203Z", "update_time": "2024-10-16T09:39:14.253Z", "parent": "e3b423d3-8ad0-42b0-a612-516c93208fa8", "menu_type": 1, "name": "角色管理", "code": null, "path": "/system/role", "component": "/role/index", "status": true, "meta": "7710f9d3-07d1-4fc4-9072-115089b0ccdb", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "de834396-885e-4c1c-a53c-a378a6875390", "fields": {"create_time": "2024-10-25T02:48:18.929Z", "update_time": "2024-10-25T02:48:18.929Z", "parent": "aee2c869-be1c-4ab2-b321-0c3e77c04160", "menu_type": 2, "name": "修改", "code": "/api/system/dept/:change", "path": "/api/system/dept/", "component": null, "status": true, "meta": "8d042eb5-249b-4c95-a8a2-ffa76b41eaf0", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "e3b423d3-8ad0-42b0-a612-516c93208fa8", "fields": {"create_time": "2024-10-14T13:49:39.867Z", "update_time": "2024-10-30T02:33:26.366Z", "parent": null, "menu_type": 1, "name": "系统管理", "code": null, "path": "/system", "component": null, "status": true, "meta": "eafcd30d-b262-4ea8-ad1a-1a63ffd27609", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "f5c282a6-9ac4-4b8a-965a-d1d98a7178ab", "fields": {"create_time": "2024-10-15T05:15:12.609Z", "update_time": "2024-10-15T05:19:55.124Z", "parent": "b7965934-3627-4c1d-a919-d52bb61212a9", "menu_type": 1, "name": "首页", "code": null, "path": "/home", "component": "/home/<USER>", "status": true, "meta": "3c200710-72a5-4b25-bf7e-5da6398868e6", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "fbf4bfce-5f53-44fc-8604-1a3f3bcf3b56", "fields": {"create_time": "2024-10-25T02:42:04.477Z", "update_time": "2024-10-25T02:42:04.477Z", "parent": "dcbdeec0-9e59-4c87-ab4b-6a362a27bd84", "menu_type": 2, "name": "修改", "code": "/api/system/role/:change", "path": "/api/system/role/", "component": null, "status": true, "meta": "fcd52d1f-bdd8-474f-a780-79df801427a5", "method": null, "redirect": null}}, {"model": "system.menu", "pk": "fd445ccb-f5ec-4dbc-b94b-2cdef097a3bd", "fields": {"create_time": "2024-10-25T02:47:56.305Z", "update_time": "2024-10-25T02:47:56.305Z", "parent": "5eb483bd-76e3-46c4-b60c-323a8b876fe4", "menu_type": 2, "name": "修改", "code": "/api/user/:change", "path": "/api/user/", "component": null, "status": true, "meta": "2a489016-147a-4687-a6f6-ae7d73df672f", "method": null, "redirect": null}}, {"model": "system.menumeta", "pk": "0ca55276-c54d-4d61-a817-1ca4037ed2c7", "fields": {"create_time": "2024-10-25T02:42:04.444Z", "update_time": "2024-10-25T02:42:04.444Z", "title": null, "icon": null, "rank": 9995, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "2298b1d7-1eba-445a-8a4c-0a25a9e96056", "fields": {"create_time": "2024-10-25T02:48:18.934Z", "update_time": "2024-10-25T02:48:18.934Z", "title": null, "icon": null, "rank": 9998, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "242704c7-b5eb-4b3e-9581-23da9ede5c7e", "fields": {"create_time": "2024-10-25T02:30:52.094Z", "update_time": "2024-10-25T02:30:52.094Z", "title": null, "icon": null, "rank": 9996, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "2a489016-147a-4687-a6f6-ae7d73df672f", "fields": {"create_time": "2024-10-25T02:47:56.301Z", "update_time": "2024-10-25T02:47:56.302Z", "title": null, "icon": null, "rank": 9997, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "3c200710-72a5-4b25-bf7e-5da6398868e6", "fields": {"create_time": "2024-10-15T05:15:12.594Z", "update_time": "2024-10-15T05:19:55.109Z", "title": "首页", "icon": "ep:house", "rank": 1, "r_svg_name": null, "is_show_menu": true, "is_show_parent": false, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "3cfdb18a-3ed4-4193-8753-004d790843fd", "fields": {"create_time": "2024-10-25T02:48:18.910Z", "update_time": "2024-10-25T02:48:18.910Z", "title": null, "icon": null, "rank": 9996, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "43a996b8-15f0-496f-933b-93bed0a887f2", "fields": {"create_time": "2024-10-25T02:42:04.482Z", "update_time": "2024-10-25T02:42:04.482Z", "title": null, "icon": null, "rank": 9998, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "4e161fb1-6f66-4e9b-a1d2-1231869890e0", "fields": {"create_time": "2024-10-14T14:04:37.881Z", "update_time": "2024-10-16T09:39:25.096Z", "title": "用户管理", "icon": "ep:user", "rank": 3, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "58b9dacf-edb9-477b-a625-c89a806f1af6", "fields": {"create_time": "2024-10-25T02:47:56.271Z", "update_time": "2024-10-25T02:47:56.271Z", "title": null, "icon": null, "rank": 9995, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "6adc30be-629d-4410-b5b0-6476d2564298", "fields": {"create_time": "2024-10-30T07:43:30.627Z", "update_time": "2024-10-31T02:22:31.802Z", "title": "登录日志", "icon": "ri:login-circle-line", "rank": 1, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "6bff4051-e205-4de1-b343-5e09dad6ea59", "fields": {"create_time": "2024-10-25T02:30:52.082Z", "update_time": "2024-10-25T02:30:52.082Z", "title": null, "icon": null, "rank": 9995, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "7710f9d3-07d1-4fc4-9072-115089b0ccdb", "fields": {"create_time": "2024-10-14T14:00:01.186Z", "update_time": "2024-10-16T09:39:14.253Z", "title": "角色管理", "icon": "ri:group-line", "rank": 2, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "79c28ca9-5901-4d80-802f-e80b8fe88f54", "fields": {"create_time": "2024-10-14T13:52:42.255Z", "update_time": "2024-10-16T09:38:15.816Z", "title": "菜单权限", "icon": "ep:menu", "rank": 1, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "7c9e7404-00a1-4041-8af8-a590a070bc26", "fields": {"create_time": "2024-10-30T07:42:22.283Z", "update_time": "2024-10-30T07:42:22.283Z", "title": "系统监控", "icon": "ep:monitor", "rank": 2, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "823ae329-bef9-47a4-a4ed-e56f0d19b584", "fields": {"create_time": "2024-10-25T02:42:04.457Z", "update_time": "2024-10-25T02:42:04.457Z", "title": null, "icon": null, "rank": 9996, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "8d042eb5-249b-4c95-a8a2-ffa76b41eaf0", "fields": {"create_time": "2024-10-25T02:48:18.923Z", "update_time": "2024-10-25T02:48:18.923Z", "title": null, "icon": null, "rank": 9997, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "8d78fd5e-05b0-4242-a6bc-ece333724fd6", "fields": {"create_time": "2024-10-15T05:14:30.486Z", "update_time": "2024-10-15T05:37:08.688Z", "title": "首页", "icon": "ep:house", "rank": 0, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "9435b8e0-0e01-445f-9060-49537df8161f", "fields": {"create_time": "2024-10-24T07:47:47.521Z", "update_time": "2024-10-24T07:47:47.521Z", "title": null, "icon": null, "rank": 9999, "r_svg_name": null, "is_show_menu": true, "is_show_parent": false, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "af4c154e-21ad-4216-b236-ae0da15a1fa1", "fields": {"create_time": "2024-10-14T13:59:11.669Z", "update_time": "2024-10-16T09:40:23.493Z", "title": "部门管理", "icon": "ri:node-tree", "rank": 4, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "bd4b1268-db6b-4caf-bf7e-a8c2a697ed0c", "fields": {"create_time": "2024-10-14T09:21:34.342Z", "update_time": "2024-10-14T13:30:40.414Z", "title": "权限测试", "icon": "ep:paperclip", "rank": 1, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "bf93cd77-d279-4577-9845-6d52c71de464", "fields": {"create_time": "2024-10-25T02:48:18.896Z", "update_time": "2024-10-25T02:48:18.896Z", "title": null, "icon": null, "rank": 9995, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "bfcd1b2a-597b-419c-8b15-4d38d58ef8c9", "fields": {"create_time": "2024-10-30T09:32:31.354Z", "update_time": "2024-10-30T09:32:31.354Z", "title": "操作日志", "icon": "ep:document", "rank": 2, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "c26066ad-cb55-4c49-ae27-5a74b5dc4361", "fields": {"create_time": "2024-10-25T02:47:56.289Z", "update_time": "2024-10-25T02:47:56.289Z", "title": null, "icon": null, "rank": 9996, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "d0a38a0d-611d-469e-8741-f07f3c84e910", "fields": {"create_time": "2024-10-25T02:30:52.105Z", "update_time": "2024-10-25T02:30:52.105Z", "title": null, "icon": null, "rank": 9997, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "d19b1c05-a962-4923-b939-4471b52cdd60", "fields": {"create_time": "2024-09-15T14:22:23.815Z", "update_time": "2024-10-14T13:55:08.480Z", "title": "功能测试", "icon": "ri:test-tube-line", "rank": 99, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "d6638fb6-9a8e-4063-9230-ec260cbf705b", "fields": {"create_time": "2024-10-25T02:47:56.312Z", "update_time": "2024-10-25T02:47:56.312Z", "title": null, "icon": null, "rank": 9998, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "eafcd30d-b262-4ea8-ad1a-1a63ffd27609", "fields": {"create_time": "2024-10-14T13:49:39.849Z", "update_time": "2024-10-30T02:33:26.361Z", "title": "系统管理", "icon": "ep:setting", "rank": 1, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "ee3ae725-1376-4d39-900a-61c81a77d496", "fields": {"create_time": "2024-10-24T07:48:34.370Z", "update_time": "2024-10-24T07:48:34.370Z", "title": null, "icon": null, "rank": 9999, "r_svg_name": null, "is_show_menu": true, "is_show_parent": false, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "fcd52d1f-bdd8-474f-a780-79df801427a5", "fields": {"create_time": "2024-10-25T02:42:04.469Z", "update_time": "2024-10-25T02:42:04.469Z", "title": null, "icon": null, "rank": 9997, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.menumeta", "pk": "fded4dba-8f38-4fc3-b13b-74d11b9d6823", "fields": {"create_time": "2024-10-25T02:30:52.116Z", "update_time": "2024-10-25T02:30:52.116Z", "title": null, "icon": null, "rank": 9998, "r_svg_name": null, "is_show_menu": true, "is_show_parent": true, "is_keepalive": false, "frame_url": null, "frame_loading": false, "transition_enter": null, "transition_leave": null, "is_hidden_tag": false, "fixed_tag": false, "dynamic_level": 0}}, {"model": "system.deptinfo", "pk": "4c76aef7-7c65-4505-a614-e7b2b2c5b2af", "fields": {"create_time": "2024-09-08T15:16:41.289Z", "update_time": "2024-10-25T02:56:44.534Z", "name": "管理部门", "code": "management", "type": 3, "parent": "9975ede0-ab2a-488b-9464-e7ac7823e7d5", "rank": 0, "auto_bind": false, "status": true, "roles": []}}, {"model": "system.deptinfo", "pk": "9975ede0-ab2a-488b-9464-e7ac7823e7d5", "fields": {"create_time": "2024-09-08T15:15:30.860Z", "update_time": "2024-10-25T02:55:36.454Z", "name": "总部", "code": "base", "type": 1, "parent": null, "rank": 0, "auto_bind": false, "status": true, "roles": []}}, {"model": "monitor.operationlog", "pk": 60, "fields": {"create_time": "2024-12-02T07:15:00.969Z", "update_time": "2024-12-02T07:15:00.969Z", "request_modular": "", "request_path": "/api/monitor/clearoperationlog/", "request_body": {}, "request_method": "DELETE", "request_msg": null, "request_ip": "127.0.0.1", "request_browser": "Chrome 131.0.0", "response_code": "200", "request_os": "Windows 10", "json_result": {"success": true, "data": null, "msg": "删除所有日志成功", "page": 1, "limit": 1, "total": 0}, "status": true, "creator": 1}}]