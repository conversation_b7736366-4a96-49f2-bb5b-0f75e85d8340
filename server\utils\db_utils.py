import os
import logging
import requests
import json
import traceback
from django.conf import settings
from typing import Dict, Any, Optional, List, Union
from .retry import retry
from .error_handler import format_error, notify_error

logger = logging.getLogger(__name__)

def log_pg_connection_debug(connection_config, prefix="PG-DEBUG"):
    """PostgreSQL连接调试日志"""
    try:
        # 创建一个安全的配置副本（不含密码）
        safe_config = {**connection_config}
        if 'password' in safe_config:
            safe_config['password'] = '*****'

        # 检查各种必要的参数
        required_params = ['host', 'port', 'username', 'database', 'dbname']
        missing_params = [p for p in required_params if p not in safe_config or not safe_config[p]]

        # 记录基本信息
        logger.info(f"{prefix} - 连接参数: {json.dumps(safe_config, ensure_ascii=False)}")

        # 记录参数检查结果
        if missing_params:
            logger.warning(f"{prefix} - 缺少参数: {', '.join(missing_params)}")

        # 检查PostgreSQL特有参数
        pg_specific = {k: v for k, v in safe_config.items() if k in ['dbname', 'sslmode', 'application_name', 'client_encoding']}
        logger.info(f"{prefix} - PostgreSQL特有参数: {json.dumps(pg_specific, ensure_ascii=False)}")

        # 检查URI连接字符串
        if 'uri' in safe_config:
            uri = safe_config['uri']
            # 替换密码为星号以避免泄露
            masked_uri = uri
            if '@' in uri:
                parts = uri.split('@')
                creds = parts[0].split(':')
                if len(creds) > 2:  # 确保有用户名和密码部分
                    masked_uri = f"{creds[0]}:{'*' * 6}@{parts[1]}"
            logger.info(f"{prefix} - URI连接字符串: {masked_uri}")

        # 检查参数对象
        if 'parameters' in safe_config and safe_config['parameters']:
            logger.info(f"{prefix} - 额外参数: {json.dumps(safe_config['parameters'], ensure_ascii=False)}")

        # 检查请求结构
        if 'db_type' in safe_config:
            logger.info(f"{prefix} - 数据库类型: {safe_config['db_type']}")
        if 'config' in safe_config:
            safe_config_inner = {**safe_config['config']}
            if 'password' in safe_config_inner:
                safe_config_inner['password'] = '*****'
            logger.info(f"{prefix} - 内部配置: {json.dumps(safe_config_inner, ensure_ascii=False)}")
    except Exception as e:
        logger.error(f"{prefix} - 调试日志记录失败: {str(e)}")
        logger.exception(e)

class DBAgentClient:
    """
    DB Agent API客户端类

    负责与DB-Agent服务通信，执行数据库连接测试、查询和比对操作。
    """

    def __init__(self, agent_url=None):
        """初始化DB-Agent客户端"""
        # 获取API地址，优先使用传入的值，其次从环境变量获取，再次从settings获取，最后使用默认值
        if agent_url:
            self.agent_url = agent_url
        else:
            # 从环境变量获取
            self.agent_url = os.environ.get('DB_AGENT_URL')

            # 如果环境变量中没有，尝试从Django settings获取
            if not self.agent_url:
                try:
                    from django.conf import settings
                    self.agent_url = getattr(settings, 'DB_AGENT_URL', None)
                except (ImportError, AttributeError):
                    pass

            # 如果仍然没有，使用默认值
            if not self.agent_url:
                self.agent_url = "http://localhost:8001"

        # 获取API密钥，同样优先从环境变量获取，如果没有则使用默认值
        api_key = os.environ.get('DB_AGENT_API_KEY', 'token')

        # 尝试从Django settings获取
        if api_key == 'token':
            try:
                from django.conf import settings
                api_key = getattr(settings, 'DB_AGENT_API_KEY', 'token')
            except (ImportError, AttributeError):
                pass

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # 确保URL末尾没有斜杠，便于拼接路径
        self.agent_url = self.agent_url.rstrip('/')

        logger.info(f"已初始化DB-Agent客户端，服务地址: {self.agent_url}")

    def get_connection_url(self):
        """获取连接测试API URL"""
        return f"{self.agent_url}/api/database/connection/test/"

    def get_query_url(self):
        """获取查询API URL"""
        return f"{self.agent_url}/api/v1/query"

    def get_compare_url(self):
        """获取比对API URL"""
        return f"{self.agent_url}/api/tasks"

    def test_connection(self, connection_config):
        """
        测试数据库连接

        参数:
            connection_config: 数据库连接配置

        返回:
            连接测试结果
        """
        try:
            url = self.get_connection_url()

            # 构建符合DB-Agent期望的请求体格式
            # 直接将整个配置作为config字段的值，避免数据结构变换
            request_body = {
                "config": connection_config
            }

            # 发送请求到DB-Agent，包含认证头
            response = requests.post(url, json=request_body, headers=self.headers, timeout=15)
            try:
                content = response.json()
            except ValueError:
                content = {"success": False, "message": f"非法JSON响应: {response.text[:200]}"}

            # 检查HTTP状态码
            response.raise_for_status()

            # HTTP状态码成功
            return content

        except requests.exceptions.HTTPError as e:
            response_text = ""
            try:
                response_text = e.response.text
                error_data = e.response.json()
                logger.error(f"连接测试错误详情: {error_data}")
                return {"success": False, "message": f"连接测试失败: {error_data.get('detail', str(e))}"}
            except ValueError:
                logger.error(f"连接测试错误响应不是有效的JSON: {response_text[:200]}")
                return {"success": False, "message": f"连接测试失败: {str(e)}，响应: {response_text[:200]}"}

        except requests.exceptions.Timeout:
            logger.error("连接测试请求超时")
            return {"success": False, "message": "连接测试请求超时"}

        except requests.exceptions.ConnectionError:
            logger.error("无法连接到DB-Agent服务")
            return {"success": False, "message": "无法连接到DB-Agent服务"}

        except Exception as e:
            logger.exception(f"执行连接测试时发生错误: {str(e)}")
            return {"success": False, "message": f"执行连接测试时发生错误: {str(e)}"}

    def execute_query(self, connection_config, sql, parameters=None):
        """
        执行SQL查询

        参数:
            connection_config: 数据库连接配置
            sql: SQL查询语句
            parameters: 查询参数

        返回:
            查询结果
        """
        try:
            url = self.get_query_url()
            logger.info(f"发送查询请求到DB-Agent: {url}")

            # 准备请求数据
            request_data = {
                "connection": connection_config,
                "sql": sql,
                "parameters": parameters or {}
            }

            # 发送请求到DB-Agent
            response = requests.post(url, json=request_data, headers=self.headers, timeout=30)
            response.raise_for_status()

            # 解析响应
            return response.json()

        except requests.exceptions.Timeout:
            logger.error("查询请求超时")
            return {"success": False, "message": "查询请求超时"}

        except requests.exceptions.ConnectionError:
            logger.error("无法连接到DB-Agent服务")
            return {"success": False, "message": "无法连接到DB-Agent服务"}

        except Exception as e:
            logger.exception(f"执行查询时发生错误: {str(e)}")
            return {"success": False, "message": f"执行查询时发生错误: {str(e)}"}

    def create_compare_task(self, task_name: str, tables: list, source_conn, target_conn, limit=1000, options=None):
        """创建数据库比对任务"""
        try:
            # 如果没有表配置，直接返回失败
            if not tables or len(tables) == 0:
                return {'success': False, 'message': '未提供表配置信息'}

            # 获取第一个表进行比对(目前DB-Agent API只支持单表比对)
            first_table = tables[0]

            # 获取主键字段
            primary_keys = []
            if hasattr(first_table, "primary_key") and first_table.primary_key:
                # 从表配置的主键字段获取
                primary_keys = first_table.primary_key if isinstance(first_table.primary_key, list) else [first_table.primary_key]
            elif hasattr(first_table, "keyColumns") and first_table.keyColumns:
                # 从keyColumns字段获取
                primary_keys = first_table.keyColumns
            elif hasattr(first_table, "key_columns") and first_table.key_columns:
                # 从key_columns字段获取
                primary_keys = first_table.key_columns
            else:
                # 尝试从对象字典中获取
                table_dict = first_table
                if not isinstance(first_table, dict):
                    table_dict = first_table.__dict__ if hasattr(first_table, "__dict__") else {}

                for key in ["primary_keys", "primary_key", "keyColumns", "key_columns"]:
                    if key in table_dict and table_dict[key]:
                        values = table_dict[key]
                        primary_keys = values if isinstance(values, list) else [values]
                        break

                # 如果仍未找到，尝试从SQL查询中推断主键
                if not primary_keys:
                    # 尝试从源查询或目标查询中推断主键
                    source_query = getattr(first_table, 'sourceQuery', None) or getattr(first_table, 'source_query', None)
                    target_query = getattr(first_table, 'targetQuery', None) or getattr(first_table, 'target_query', None)

                    inferred_keys = None
                    if source_query:
                        inferred_keys = self._extract_primary_keys_from_query(source_query)
                    elif target_query:
                        inferred_keys = self._extract_primary_keys_from_query(target_query)

                    if inferred_keys:
                        primary_keys = inferred_keys
                        logger.info(f"从SQL查询中推断主键: {primary_keys}")
                    else:
                        # 使用智能默认值，不再硬编码为"id"
                        primary_keys = ["KEY"]  # 使用更通用的KEY作为默认主键
                        logger.warning(f"未找到主键配置，使用智能默认主键: {primary_keys}")
                        logger.info("db-agent将尝试智能推断实际的主键字段")

            # 获取比对字段
            compare_columns = []
            if hasattr(first_table, "compareColumns") and first_table.compareColumns:
                compare_columns = first_table.compareColumns
            elif hasattr(first_table, "compare_columns") and first_table.compare_columns:
                compare_columns = first_table.compare_columns
            else:
                # 尝试从对象字典中获取
                table_dict = first_table
                if not isinstance(first_table, dict):
                    table_dict = first_table.__dict__ if hasattr(first_table, "__dict__") else {}

                for key in ["compare_columns", "compareColumns"]:
                    if key in table_dict and table_dict[key]:
                        compare_columns = table_dict[key]
                        break

            # 判断是表模式还是查询模式
            source_sql = ""
            target_sql = ""

            # 获取表名或SQL
            if hasattr(first_table, "sourceTable") and hasattr(first_table, "targetTable"):
                source_sql = first_table.sourceTable
                target_sql = first_table.targetTable
            elif hasattr(first_table, "source_table") and hasattr(first_table, "target_table"):
                source_sql = first_table.source_table
                target_sql = first_table.target_table
            elif hasattr(first_table, "sql_1") and hasattr(first_table, "sql_2"):
                source_sql = first_table.sql_1
                target_sql = first_table.sql_2
            else:
                # 尝试从对象字典中获取
                table_dict = first_table
                if not isinstance(first_table, dict):
                    table_dict = first_table.__dict__ if hasattr(first_table, "__dict__") else {}

                if "source_table" in table_dict and "target_table" in table_dict:
                    source_sql = table_dict["source_table"]
                    target_sql = table_dict["target_table"]
                elif "sourceTable" in table_dict and "targetTable" in table_dict:
                    source_sql = table_dict["sourceTable"]
                    target_sql = table_dict["targetTable"]
                elif "sql_1" in table_dict and "sql_2" in table_dict:
                    source_sql = table_dict["sql_1"]
                    target_sql = table_dict["sql_2"]

            # 检测是否为SQL查询语句
            is_query_mode = (
                isinstance(source_sql, str) and (
                    "select" in source_sql.lower() or
                    "from" in source_sql.lower() or
                    "join" in source_sql.lower() or
                    "where" in source_sql.lower() or
                    ";" in source_sql
                )
            )

            # 从options中获取算法类型
            algorithm_type = "primary_key"  # 默认算法
            if options and isinstance(options, dict):
                if "algorithm_type" in options:
                    algorithm_type = options["algorithm_type"]
                elif "algorithmType" in options:
                    algorithm_type = options["algorithmType"]

            # 构建请求配置
            config = {
                "primary_keys": primary_keys,
                "compare_columns": compare_columns,
                "batch_size": limit,
                "algorithm_type": algorithm_type
            }

            # 添加options中的其他配置项，统一前端和Agent的字段映射
            if options and isinstance(options, dict):
                # 前端字段到Agent字段的映射
                field_mapping = {
                    # 前端字段名 -> Agent字段名
                    'compareObjects': 'compare_columns',
                    'ignoreColumns': 'ignore_columns',
                    'whereClause': 'where_clause',
                    'ignoreCase': 'ignore_case',
                    'ignoreWhitespace': 'ignore_whitespace',
                    'numericPrecision': 'numeric_precision',
                    'datePrecision': 'date_precision',
                    'timeoutMinutes': 'timeout_minutes',
                    'threadCount': 'thread_count',
                    'caseSensitive': 'case_sensitive',
                    'trimStrings': 'trim_strings',
                    'ignorePrecision': 'ignore_precision',
                    'maxDiffRecords': 'max_diff_records',
                    'callbackUrl': 'callback_url',
                    'priority': 'priority'
                }

                # 处理字段映射
                for frontend_key, agent_key in field_mapping.items():
                    if frontend_key in options and options[frontend_key] is not None:
                        config[agent_key] = options[frontend_key]

                # 特殊处理：线程数转换为批次大小
                if 'threadCount' in options and options['threadCount']:
                    config['batch_size'] = max(100, min(5000, options['threadCount'] * 100))

                # 特殊处理：超时时间转换为秒
                if 'timeoutMinutes' in options and options['timeoutMinutes']:
                    config['timeout'] = options['timeoutMinutes'] * 60

                # 处理其他直接支持的配置项（保持向后兼容）
                direct_supported = [
                    "ignore_columns", "where_clause", "priority",
                    "callback_url", "max_diff_records", "case_sensitive",
                    "trim_strings", "ignore_whitespace", "ignore_precision"
                ]

                for key, value in options.items():
                    # 转换驼峰命名为下划线命名
                    snake_key = ''.join(['_' + c.lower() if c.isupper() else c for c in key]).lstrip('_')
                    if snake_key in direct_supported and snake_key not in config:
                        config[snake_key] = value

            # 根据模式设置不同的字段
            if is_query_mode:
                config["mode"] = "query"
                config["source_query"] = source_sql
                config["target_query"] = target_sql
                logger.info(f"使用查询模式进行比对，源SQL: {source_sql[:50]}...")
            else:
                config["mode"] = "table"
                config["source_table"] = source_sql
                config["target_table"] = target_sql
                logger.info(f"使用表模式进行比对，源表: {source_sql}, 目标表: {target_sql}")

            # 构建完整请求
            agent_config = {
                "task_name": task_name,  # 添加任务名称
                "source": {
                    "type": source_conn.type.lower(),  # 确保小写
                    "host": source_conn.host,
                    "port": source_conn.port,
                    "username": source_conn.username,
                    "password": source_conn.password,
                    "database": source_conn.database
                },
                "target": {
                    "type": target_conn.type.lower(),  # 确保小写
                    "host": target_conn.host,
                    "port": target_conn.port,
                    "username": target_conn.username,
                    "password": target_conn.password,
                    "database": target_conn.database
                },
                "config": config
            }

            # 日志记录请求信息
            logger.info(f"发送比对任务请求，任务名称: {task_name}, 模式: {config['mode']}, 算法: {algorithm_type}")

            # 发送HTTP请求创建比对任务
            url = f"{self.get_compare_url()}/compare"
            response = requests.post(url, json=agent_config, headers=self.headers, timeout=60)

            if response.status_code >= 400:
                error_msg = f"创建比对任务失败，状态码: {response.status_code}"
                try:
                    error_data = response.json()
                    if isinstance(error_data, dict) and 'message' in error_data:
                        error_msg = f"{error_msg}, 错误: {error_data['message']}"
                    logger.error(f"{error_msg}, 响应体: {response.text[:500]}")
                except:
                    logger.error(f"{error_msg}, 响应体: {response.text[:500]}")

                return {
                    'success': False,
                    'message': error_msg
                }

            # 解析响应
            result = response.json()
            logger.info(f"创建比对任务成功: {result}")
            return {
                'success': True,
                'data': result
            }

        except requests.exceptions.Timeout:
            logger.error("创建比对任务超时")
            return {'success': False, 'message': '请求超时，DB-Agent服务可能不可用'}
        except requests.exceptions.ConnectionError:
            logger.error("连接DB-Agent服务失败")
            return {'success': False, 'message': '无法连接DB-Agent服务，请检查服务是否启动'}
        except Exception as e:
            logger.exception(f"创建比对任务异常: {str(e)}")
            return {'success': False, 'message': f'发生异常: {str(e)}'}

    @retry(
        max_attempts=3,
        delay=1.0,
        backoff_factor=2.0,
        exceptions=[requests.exceptions.RequestException],
        fatal_exceptions=[requests.exceptions.ConnectionError],
        retry_message="重试获取任务状态 (尝试 {attempt}/{max_attempts}): 任务ID={task_id}",
        failure_message="获取任务状态失败，已达到最大重试次数: 任务ID={task_id}"
    )
    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            url = f"{self.get_compare_url()}/{task_id}/status"
            logger.debug(f"获取任务状态: {url}")

            response = requests.get(url, headers=self.headers, timeout=10)

            # 检查JSON响应
            try:
                result = response.json()
            except ValueError:
                error_msg = f"无效JSON响应: {response.text[:200]}"
                logger.error(error_msg)
                # 记录错误详情
                error_report = {
                    "type": "InvalidJSONResponse",
                    "message": error_msg,
                    "category": "external",
                    "severity": "error",
                    "is_fatal": False,
                    "context": {
                        "task_id": task_id,
                        "url": url,
                        "status_code": response.status_code,
                        "response_text": response.text[:500]
                    }
                }
                notify_error(error_report, task_id)
                return {'success': False, 'message': f'无效JSON响应: {response.text[:100]}...'}

            # 检查状态码
            if response.status_code < 400 and isinstance(result, dict) and result.get('success') is False:
                error_message = result.get('message', '未知错误')
                logger.error(f"获取任务状态失败: {error_message}")
                # 记录错误详情
                error_report = {
                    "type": "APIError",
                    "message": error_message,
                    "category": "external",
                    "severity": "error",
                    "is_fatal": False,
                    "context": {
                        "task_id": task_id,
                        "url": url,
                        "status_code": response.status_code,
                        "response": result
                    }
                }
                notify_error(error_report, task_id)
                return {'success': False, 'message': error_message, 'detail': result}

            # 检查HTTP状态码
            if response.status_code >= 400:
                error_msg = f"获取任务状态失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                error_detail = result if isinstance(result, dict) else {'text': response.text}
                # 记录错误详情
                error_report = {
                    "type": "HTTPError",
                    "message": f"状态码: {response.status_code}，响应: {response.text[:100]}...",
                    "category": "external",
                    "severity": "error",
                    "is_fatal": False,
                    "context": {
                        "task_id": task_id,
                        "url": url,
                        "status_code": response.status_code,
                        "response": error_detail
                    }
                }
                notify_error(error_report, task_id)
                return {'success': False, 'message': f'状态码: {response.status_code}，响应: {response.text[:100]}...', 'detail': error_detail}

            return {'success': True, 'data': result}

        except requests.exceptions.Timeout as e:
            error_msg = "获取任务状态超时"
            logger.error(error_msg)
            # 记录错误详情
            error_report = {
                "type": "TimeoutError",
                "message": error_msg,
                "category": "timeout",
                "severity": "warning",
                "is_fatal": False,
                "context": {
                    "task_id": task_id,
                    "url": f"{self.get_compare_url()}/{task_id}/status",
                    "exception": str(e)
                }
            }
            notify_error(error_report, task_id)
            return {'success': False, 'message': '请求超时，DB-Agent服务可能不可用'}

        except requests.exceptions.ConnectionError as e:
            error_msg = "连接DB-Agent服务失败"
            logger.error(error_msg)
            # 记录错误详情
            error_report = {
                "type": "ConnectionError",
                "message": error_msg,
                "category": "network",
                "severity": "error",
                "is_fatal": True,  # 连接错误被视为致命错误
                "context": {
                    "task_id": task_id,
                    "url": f"{self.get_compare_url()}/{task_id}/status",
                    "exception": str(e)
                }
            }
            notify_error(error_report, task_id)
            return {'success': False, 'message': 'Connection refused', 'detail': {'error': {'type': 'ConnectionError', 'message': '无法连接DB-Agent服务，请检查服务是否启动'}}}

        except Exception as e:
            error_msg = f"获取任务状态异常: {str(e)}"
            logger.exception(error_msg)
            # 使用错误处理工具记录错误
            error_report = format_error(e, task_id, {
                "url": f"{self.get_compare_url()}/{task_id}/status",
                "method": "get_task_status"
            })
            logger.error(f"错误详情: {error_report}")
            notify_error(error_report, task_id)
            return {'success': False, 'message': f'发生异常: {str(e)}'}

    def get_task_result(self, task_id):
        """获取任务结果"""
        try:
            url = f"{self.get_compare_url()}/{task_id}/result"
            response = requests.get(url, headers=self.headers, timeout=15)

            # 检查JSON响应
            try:
                result = response.json()
            except ValueError:
                logger.error(f"无效JSON响应: {response.text[:200]}")
                return {'success': False, 'message': f'无效JSON响应: {response.text[:100]}...'}

            # 检查状态码
            if response.status_code < 400 and isinstance(result, dict) and result.get('success') is False:
                error_message = result.get('message', '未知错误')
                logger.error(f"获取任务结果失败: {error_message}")
                return {'success': False, 'message': error_message, 'detail': result}

            # 检查HTTP状态码
            if response.status_code >= 400:
                logger.error(f"获取任务结果失败: {response.status_code} - {response.text}")
                error_detail = result if isinstance(result, dict) else {'text': response.text}
                return {'success': False, 'message': f'状态码: {response.status_code}，响应: {response.text[:100]}...', 'detail': error_detail}

            # 检查结果中是否有错误信息
            if isinstance(result, dict) and result.get('error'):
                error_info = result.get('error', {})
                error_message = error_info.get('message', '未知错误') if isinstance(error_info, dict) else str(error_info)
                logger.error(f"获取任务结果失败: {error_message}")
                return {'success': False, 'message': error_message, 'detail': result}

            return {'success': True, 'data': result}

        except requests.exceptions.Timeout:
            logger.error("获取任务结果超时")
            return {'success': False, 'message': '请求超时，DB-Agent服务可能不可用'}
        except requests.exceptions.ConnectionError:
            logger.error("连接DB-Agent服务失败")
            return {'success': False, 'message': 'Connection refused', 'detail': {'error': {'type': 'ConnectionError', 'message': '无法连接DB-Agent服务，请检查服务是否启动'}}}
        except Exception as e:
            logger.exception(f"获取任务结果异常: {str(e)}")
            return {'success': False, 'message': f'发生异常: {str(e)}'}

    def get_task_table_progress(self, task_id):
        """
        获取任务的表级进度

        参数:
            task_id: 任务ID

        返回:
            表级进度数据
        """
        try:
            # 修复URL路径，确保使用正确的API端点
            url = f"{self.get_compare_url()}/{task_id}/tables"
            logger.info(f"获取表级进度: {url}")

            # 添加重试逻辑
            max_retries = 3
            retry_count = 0
            while retry_count < max_retries:
                try:
                    # 发送请求到DB-Agent
                    response = requests.get(url, headers=self.headers, timeout=10)
                    response.raise_for_status()

                    # 解析响应
                    return response.json()
                except (requests.exceptions.RequestException, requests.exceptions.HTTPError) as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise
                    logger.warning(f"获取表级进度失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    import time
                    time.sleep(1)  # 重试前等待1秒

        except requests.exceptions.Timeout:
            logger.error("获取表级进度请求超时")
            return {"success": False, "message": "获取表级进度请求超时"}

        except requests.exceptions.ConnectionError:
            logger.error("无法连接到DB-Agent服务")
            return {"success": False, "message": "无法连接到DB-Agent服务"}

        except Exception as e:
            logger.exception(f"获取表级进度时发生错误: {str(e)}")
            return {"success": False, "message": f"获取表级进度时发生错误: {str(e)}"}

    def control_task(self, task_id, action):
        """
        控制任务执行

        参数:
            task_id: 任务ID
            action: 控制动作 (pause, resume, stop)

        返回:
            控制结果
        """
        try:
            url = f"{self.get_compare_url()}/{task_id}/control"
            logger.info(f"控制任务: {url}, 动作: {action}")

            # 准备请求数据
            request_data = {
                "action": action
            }

            # 发送请求到DB-Agent
            response = requests.post(url, json=request_data, headers=self.headers, timeout=10)
            response.raise_for_status()

            # 解析响应
            return response.json()

        except requests.exceptions.Timeout:
            logger.error("控制任务请求超时")
            return {"success": False, "message": "控制任务请求超时"}

        except requests.exceptions.ConnectionError:
            logger.error("无法连接到DB-Agent服务")
            return {"success": False, "message": "无法连接到DB-Agent服务"}

        except Exception as e:
            logger.exception(f"控制任务时发生错误: {str(e)}")
            return {"success": False, "message": f"控制任务时发生错误: {str(e)}"}

    def cancel_task(self, task_id):
        """
        取消任务执行

        参数:
            task_id: 任务ID

        返回:
            取消结果
        """
        try:
            # 使用DELETE方法取消任务
            url = f"{self.get_compare_url()}/{task_id}"
            logger.info(f"取消任务: {url}")

            # 发送DELETE请求到DB-Agent
            response = requests.delete(url, headers=self.headers, timeout=10)

            # 检查状态码
            if response.status_code == 204:  # 成功但无内容返回
                logger.info(f"成功取消任务: {task_id}")
                return {"success": True, "message": "任务已取消"}

            # 尝试解析响应
            try:
                result = response.json()
                return result
            except ValueError:
                # 如果无法解析JSON，检查状态码
                if response.status_code < 400:
                    return {"success": True, "message": "任务已取消"}
                else:
                    logger.error(f"取消任务失败: {response.status_code} - {response.text}")
                    return {"success": False, "message": f"取消任务失败: {response.status_code}"}

        except requests.exceptions.Timeout:
            logger.error("取消任务请求超时")
            return {"success": False, "message": "取消任务请求超时"}

        except requests.exceptions.ConnectionError:
            logger.error("无法连接到DB-Agent服务")
            return {"success": False, "message": "无法连接到DB-Agent服务"}

        except Exception as e:
            logger.exception(f"取消任务时发生错误: {str(e)}")
            return {"success": False, "message": f"取消任务时发生错误: {str(e)}"}

    def _extract_primary_keys_from_query(self, query):
        """
        从SQL查询中提取主键字段

        尝试从SQL查询中识别主键字段，支持以下模式：
        1. SELECT field AS KEY - 将field识别为主键
        2. SELECT field AS key - 将field识别为主键（不区分大小写）
        3. 如果没有AS KEY/key模式，则返回None

        Args:
            query: SQL查询语句

        Returns:
            主键字段列表或None
        """
        if not query:
            return None

        import re

        # 查找包含"as key"的字段表达式（不区分大小写）
        key_regex_patterns = [
            # 标准模式: SELECT field AS KEY
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*[,)]',
            # 末尾模式: SELECT field AS KEY FROM
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s+from',
            # 单字段模式: SELECT field AS KEY
            r'select\s+(.+?)\s+as\s+(?:key|KEY)\s*$'
        ]

        # 尝试所有模式
        for pattern in key_regex_patterns:
            key_regex = re.compile(pattern, re.IGNORECASE | re.DOTALL)
            matches = key_regex.search(query)

            if matches and matches.group(1):
                # 提取主键表达式
                key_expression = matches.group(1).strip()

                # 检查是否有连接操作符(||)
                if '||' in key_expression:
                    # 提取组合主键中的各个字段
                    key_parts = [part.strip() for part in key_expression.split('||')]
                    # 移除引号和空格，保留字段名
                    key_parts = [part.strip("'\"") for part in key_parts if not part.strip("'\"") == '-']
                    return key_parts

                # 返回单一主键
                return [key_expression]

        # 如果没有找到AS KEY模式，返回None
        return None


def get_db_connection_config(connection_name):
    """
    根据连接名称获取数据库连接配置

    参数:
        connection_name: 连接名称

    返回:
        数据库连接配置
    """
    from apps.database.models import DataConnection

    try:
        # 查询数据库连接
        connection = DataConnection.objects.get(name=connection_name)

        # 构建连接配置
        config = {
            "type": connection.type,
            "host": connection.host,
            "port": connection.port,
            "username": connection.username,
            "password": connection.password,
            "database": connection.database
        }

        # 添加自定义参数
        if connection.parameters:
            config.update(connection.parameters)

        return config

    except DataConnection.DoesNotExist:
        logger.error(f"数据库连接不存在: {connection_name}")
        return None

    except Exception as e:
        logger.exception(f"获取数据库连接配置时发生错误: {str(e)}")
        return None


# 创建全局DB-Agent客户端实例
db_agent_client = DBAgentClient()

def get_db_agent_client():
    """获取DB-Agent客户端实例"""
    return db_agent_client

# 简化日志记录的辅助函数
def log_task_result(task_id, operation_name, data=None):
    """
    记录任务操作结果，包括成功和失败情况

    参数:
        task_id: 任务ID
        operation_name: 操作名称
        data: 操作相关的数据
    """
    if operation_name == "error":
        message = data.get("message", "未知错误") if data else "未知错误"
        error_detail = data.get("error", {}) if data else {}
        logger.error(f"任务{operation_name}: task_id={task_id}, message={message}")
        if error_detail:
            logger.error(f"错误详情: {error_detail}")
        return False
    else:
        message = data.get("message", "") if data else ""
        status = data.get("status", "unknown") if data else "unknown"
        logger.info(f"任务{operation_name}: task_id={task_id}, status={status}, message={message}")
        return True