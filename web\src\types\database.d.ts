/**
 * 数据库模块相关类型定义
 */

// ------------ 数据连接相关类型 ------------

// 连接状态类型
export type ConnectionStatus = 'active' | 'inactive' | 'error' | 'online' | 'offline' | 'testing';

// 数据库连接信息
export interface DataConnection {
  id?: string | number;
  name: string;
  type: string;
  type_display?: string;
  host: string;
  port: number;
  username: string;
  password?: string;
  database?: string;
  schema?: string;
  options?: Record<string, any>;
  parameters?: Record<string, any>;
  description?: string;
  createTime?: string;
  updateTime?: string;
  create_time?: string;
  update_time?: string;
  status?: ConnectionStatus;
  status_display?: string;
  message?: string;
  savePassword?: boolean; // 前端临时属性，不传给后端
}

// ------------ 数据库元数据相关类型 ------------

// SQL执行参数
export interface SqlExecuteParams {
  sql: string;
  database?: string;
  params?: Record<string, any>;
  timeout?: number;
}

// 表数据查询参数
export interface TableDataParams {
  page?: number;
  size?: number;
  orderBy?: string;
  orderDir?: 'asc' | 'desc';
  filters?: Array<{
    column: string;
    operator: string;
    value: any;
  }>;
}

// ------------ 数据库对象类型 ------------

// 数据库类型
export interface DatabaseType {
  id: string;
  name: string;
  code: string;
  version?: string;
  description?: string;
  icon?: string;
}

// 数据库对象
export interface DatabaseObject {
  id: string;
  name: string;
  type: string;
  schema?: string;
  children?: DatabaseObject[];
  hasChildren?: boolean;
  metadata?: any;
}

// 查询结果
export interface QueryResult {
  columns: Array<{
    name: string;
    type: string;
  }>;
  rows: any[][];
  rowCount: number;
  executionTime: number;
  hasMore?: boolean;
}

// ------------ 数据比对相关类型 ------------

// 比对模型查询参数
export interface CompareModelQuery {
  page?: number;
  size?: number;
  keyword?: string;
  name?: string;
  status?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
}

// 比对模型
export interface CompareModel {
  id?: string;
  name: string;
  description?: string;
  sourceDb?: string;
  targetDb?: string;
  source_connection?: string;
  target_connection?: string;
  source_type?: string;
  target_type?: string;
  sourceConnectionId?: string;
  targetConnectionId?: string;
  tables?: CompareTable[] | TableConfig[];
  createTime?: string;
  updateTime?: string;
  create_time?: string;
  update_time?: string;
  createUser?: string;
  updateUser?: string;
  status?: boolean | string;
}

// SQL 表比对配置（旧版）
export interface TableConfig {
  table_id: string;
  remark: string;
  sql_1: string;
  sql_2: string;
  create_time?: string;
  update_time?: string;
}

// 比对表
export interface CompareTable {
  id?: string;
  name?: string;
  sourceTable: string;
  targetTable: string;
  sourcePrimaryKey?: string;
  targetPrimaryKey?: string;
  primaryKey?: string[];
  keyColumns: string[];
  compareColumns: string[];
  excludeColumns?: string[];
  whereCondition?: string;
  condition?: string;
  alias?: string;
  enabled?: boolean;
  columns?: ModelColumn[];
}

// 表中的列结构
export interface ModelColumn {
  name: string;
  alias?: string;
  enabled: boolean;
  type: string;
  compareRule?: string;
  ignoreCase?: boolean;
  trimSpace?: boolean;
}

// 字段映射
export interface FieldMapping {
  sourceField: string;
  targetField: string;
}

// 比对任务查询参数
export interface CompareTaskQuery {
  page?: number;
  size?: number;
  model?: string;
  status?: string;
  startTimeRange?: [string, string];
  createTimeStart?: string;
  createTimeEnd?: string;
}

// 任务表进度详情
export interface TableProgress {
  tableName: string;
  totalRecords: number;
  processed: number;
  matched: number;
  different: number;
  errors: number;
  progress: number;
}

// 任务详情
export interface TaskDetails {
  tableName?: string;
  tableProgress: Array<{
    tableName: string;
    progress: number;
  }>;
  totalRecords: number;
  processed: number;
  matched: number;
  different: number;
  errors: number;
  progress: number;
  remainingTime?: number;
  currentSpeed?: number;
  logs?: Array<{
    level: string;
    message: string;
    timestamp: string;
  }>;
  resources?: {
    cpu: number;
    memory: number;
    diskIO: number;
    networkIO: number;
  };
}

// 比对任务
export interface CompareTask {
  id: string;
  name?: string;
  model: string;
  modelName?: string;
  status: string;
  progress: number;
  createTime?: string;
  startTime?: string;
  endTime?: string;
  duration?: number;
  message?: string;
  details?: TaskDetails;
  result?: {
    error?: {
      type: string;
      message: string;
      traceback?: string;
      context?: Record<string, any>;
      reported_at?: string;
    };
    task_id?: string;
    status?: string;
    message?: string;
    links?: Record<string, string>;
  };
  // 以下字段从旧定义保留
  sourceConnectionId?: string;
  targetConnectionId?: string;
  mode?: 'normal' | 'fast' | 'full';
  maxRecords?: number;
  timeout?: number;
  samplingRate?: number;
  errorThreshold?: number;
  parallelism?: number;
  notifyEmail?: string;
  totalRecords?: number;
  processedRecords?: number;
  matchedRecords?: number;
  differentRecords?: number;
  errorRecords?: number;
  create_time?: string;
  update_time?: string;
  start_time?: string;
  end_time?: string;
  elapsedTime?: number;
}

// 预检查参数
export interface PreCheckParams {
  model: string;
}

// 开始任务参数
export interface StartTaskParams {
  model: string;
  name: string;
  limit: number;
  options: Record<string, any>;
}

// 任务状态响应
export interface TaskStatusResponse {
  data: CompareTask;
  details: TaskDetails;
}

// 比对结果
export interface CompareResult {
  taskId: string;
  modelId?: string;
  modelName?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
  duration?: number;
  total: number;
  totalRecords?: number;
  matched: number;
  matchedRecords?: number;
  different: number;
  differentRecords?: number;
  errors: number;
  errorRecords?: number;
  matchRate?: number;
  diffTypeCounts?: Record<string, number>;
  severityCounts?: Record<string, number>;
  createTime?: string;
  differences?: CompareDifference[];
}

// 比对差异
export interface CompareDifference {
  id: string;
  tableName: string;
  keyColumns: Record<string, any>;
  sourceData?: Record<string, any>;
  targetData?: Record<string, any>;
  diffType: 'value' | 'missing_source' | 'missing_target' | 'type';
  diffColumns?: string[];
  createTime?: string;
  severity?: string;
  context?: any;
}

// 旧版差异记录定义（保留兼容性）
export interface CompareDiff {
  id: string;
  taskId: string;
  tableName: string;
  primaryKey: Record<string, any>;
  sourceData: Record<string, any>;
  targetData: Record<string, any>;
  diffType: 'insert' | 'update' | 'delete';
  diffColumns: string[];
  createTime?: string;
}

// 比对差异查询
export interface CompareDiffQuery {
  page?: number;
  size?: number;
  taskId?: string;
  tableName?: string;
  diffType?: string;
  keyword?: string;
  severity?: string;
}

// 比对任务请求
export interface CompareTaskRequest {
  model: string;
  name?: string;
  limit?: number;
  options?: {
    compareMode?: string;
    sampleRate?: number;
    compareObjects?: string[];
    resultStorage?: string;
    executionMode?: string;
    errorHandling?: string;
    threadCount?: number;
    ignoreCase?: boolean;
    ignoreWhitespace?: boolean;
    numericPrecision?: number;
    datePrecision?: string;
    timeoutMinutes?: number;
    [key: string]: any;
  };
} 