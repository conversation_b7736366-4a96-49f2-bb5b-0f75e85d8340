/**
 * 数据库API模块统一导出 - 完整版本
 * 包含所有核心功能模块和向后兼容性支持
 */

// ===== 核心客户端 =====
export {
  DatabaseApiClient,
  databaseApiClient,
  withDatabaseErrorHandling
} from './client';

// ===== 错误处理 =====
export {
  DatabaseApiError,
  handleDatabaseError,
  getErrorMessage,
  showErrorMessage,
  DatabaseErrorCode
} from './error';

// ===== 数据库连接管理 =====
export {
  getConnectionList,
  getConnection,
  createConnection,
  updateConnection,
  deleteConnection,
  testConnection,
  testConnectionById,
  getConnectionDebug,
  getConnectionStatistics,
  batchTestConnections,
  getConnectionHealth
} from './connection';

// ===== 比较模型管理 =====
export {
  getCompareModels,
  getCompareModel,
  createCompareModel,
  updateCompareModel,
  deleteCompareModel,
  cloneCompareModel
} from './compare';

// ===== 比较任务管理 =====
export {
  createCompareTask,
  startCompareTask,
  controlCompareTask,
  stopCompareTask,
  cancelCompareTask,
  launchCompareTask,
  getCompareTask,
  getCompareTaskHistory,
  deleteCompareTask,
  getCompareTaskLogs,
  getCompareTaskSummary,
  preCheckCompareTask,
  executeDirectComparison,
  getTaskStatus,
  getTaskResult,
  // 向后兼容的别名
  getCompareTaskStatus,
  getCompareTaskResult
} from './compare';

// ===== 差异数据管理 =====
export {
  getCompareDiffList,
  getDiffStatistics,
  analyzeDifferences,
  getDiffDetail,
  resolveDiff,
  batchResolveDifferences,
  exportDifferences,
  getDiffHistory,
  ignoreAllLowSeverityDiffs,
  exportUnresolvedDiffs
} from './differences';

// ===== 数据库操作 =====
export {
  getDatabaseTypes,
  getDatabaseObjects,
  getTableStructure,
  getTableDataPreview,
  executeQuery,
  cancelQuery,
  getQueryHistory,
  getTableDDL,
  getViewDefinition,
  getProcedureDefinition,
  getFunctionDefinition,
  exportQueryResult,
  getDatabaseSchemas,
  getSqlCompletions
} from './operations';

// ===== 元数据管理 =====
export {
  getDatasourceList,
  getDatasourceDetail,
  getDatabaseList,
  getTableList,
  getTableSchema,
  getTableData,
  getTableCount,
  executeSql,
  getObjectTypes,
  getFilterTypes,
  validateSql,
  previewSql,
  getTableRelations,
  getDataTypeMappings,
  batchGetTableSchemas
} from './metadata';

// ===== WebSocket 通信 =====
export {
  DatabaseWebSocketManager,
  databaseWebSocketManager,
  createTaskStatusWebSocket,
  createTaskNotificationWebSocket,
  closeTaskStatusWebSocket,
  closeTaskNotificationWebSocket,
  WebSocketStatus
} from './websocket';
export type { WebSocketConnection, WebSocketConfig } from './websocket';

// ===== 端点配置 =====
export {
  CONNECTION_ENDPOINTS,
  COMPARE_ENDPOINTS,
  DIFF_ENDPOINTS,
  DATABASE_ENDPOINTS,
  METADATA_ENDPOINTS,
  WEBSOCKET_ENDPOINTS,
  EndpointManager,
  ENDPOINTS
} from './endpoints';

// ===== 类型定义 =====
export type {
  BaseApiResponse,
  PaginatedResponse,
  DatabaseConnection,
  DatabaseType,
  CompareConfig,
  ComparisonAlgorithm,
  TaskStatus,
  ComparisonTask,
  ComparisonSummary,
  ComparisonResult,
  ComparisonDetails,
  ComparisonMetadata,
  DifferenceRecord,
  SourceOnlyRecord,
  TargetOnlyRecord,
  TableInfo,
  ColumnInfo,
  ConnectionTestResult,
  ApiError,
  ErrorCode,
  DatabaseRequestConfig
} from './types';

// ===== 向后兼容的别名 =====
// 为了保持向后兼容性，提供一些常用函数的别名

// 元数据管理的别名
export { getDatasourceList as getDatasources } from './metadata';
export { getDatabaseList as getDatabases } from './metadata';
export { getTableList as getTables } from './metadata';
export { executeSql as executeSQL } from './metadata';
export { validateSql as validateSQL } from './metadata';
export { previewSql as previewSQL } from './metadata';
export { getDataTypeMappings as getTypeMappings } from './metadata';

// 差异数据的别名
export { getDiffDetail as getCompareDiffDetail } from './differences';
export { analyzeDifferences as analyzeCompareDiff } from './differences';
export { resolveDiff as resolveCompareDiff } from './differences';
export { batchResolveDifferences as batchResolveCompareDiff } from './differences';
export { exportDifferences as exportCompareDiff } from './differences';
export { getDiffStatistics as getCompareDiffStatistics } from './differences';

// WebSocket的别名
export { createTaskStatusWebSocket as createWebSocketConnection } from './websocket';
export { closeTaskStatusWebSocket as closeWebSocketConnection } from './websocket';
export { createTaskStatusWebSocket as subscribeToTaskUpdates } from './websocket';
export { closeTaskStatusWebSocket as unsubscribeFromTaskUpdates } from './websocket';

// 数据操作的别名（基于现有函数创建）
export { validateSql as validateQuery } from './metadata';
export { getTableData as getSourceOnlyData } from './metadata';
export { getTableData as getTargetOnlyData } from './metadata';
export { getCompareDiffList as getDifferenceData } from './differences';
export { getDiffStatistics as getFieldStatistics } from './differences';
export { exportQueryResult as exportTableData } from './operations';
export { getDatabaseSchemas as getSchemas } from './operations';
export { getCompareDiffList as getSourceOnlyRecords } from './differences';
export { getCompareDiffList as getTargetOnlyRecords } from './differences';

// ===== 默认导出 =====
export { default } from './client';