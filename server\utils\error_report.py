"""
错误报告格式模块

提供统一的错误报告格式类，用于生成标准化的错误报告。
"""
import json
import uuid
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from django.utils import timezone
try:
    from .error_handler import ErrorCategory, ErrorSeverity, categorize_error, determine_severity
except ImportError:
    # 如果无法导入，创建简单的替代类和函数
    class ErrorCategory:
        UNKNOWN = "unknown"
        DATABASE = "database"
        NETWORK = "network"
        VALIDATION = "validation"
        INTERNAL = "internal"

    class ErrorSeverity:
        CRITICAL = "critical"
        ERROR = "error"
        WARNING = "warning"
        INFO = "info"

    def categorize_error(error):
        return ErrorCategory.UNKNOWN

    def determine_severity(error, category=None):
        return ErrorSeverity.ERROR

logger = logging.getLogger(__name__)

class ErrorReport:
    """
    错误报告类

    用于生成标准化的错误报告，包括错误类型、消息、堆栈跟踪、时间戳等信息。
    """

    def __init__(
        self,
        error: Optional[Exception] = None,
        error_type: str = None,
        message: str = None,
        category: Union[ErrorCategory, str] = None,
        severity: Union[ErrorSeverity, str] = None,
        is_fatal: bool = None,
        task_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        traceback_info: Optional[str] = None,
        timestamp: Optional[str] = None,
        report_id: Optional[str] = None
    ):
        """
        初始化错误报告

        Args:
            error: 异常对象
            error_type: 错误类型
            message: 错误消息
            category: 错误类别
            severity: 错误严重程度
            is_fatal: 是否为致命错误
            task_id: 相关任务ID
            context: 错误上下文信息
            traceback_info: 堆栈跟踪信息
            timestamp: 时间戳
            report_id: 报告ID
        """
        self.report_id = report_id or str(uuid.uuid4())
        self.timestamp = timestamp or timezone.now().isoformat()

        if error:
            # 如果提供了异常对象，从中提取信息
            self.error_type = error_type or type(error).__name__
            self.message = message or str(error)
            self.traceback_info = traceback_info or traceback.format_exc()

            # 如果没有提供类别和严重程度，自动判断
            if category is None:
                self.category = categorize_error(error)
            else:
                self.category = category

            if severity is None:
                self.severity = determine_severity(error, self.category if isinstance(self.category, ErrorCategory) else None)
            else:
                self.severity = severity

            # 如果没有提供是否为致命错误，根据严重程度判断
            if is_fatal is None:
                self.is_fatal = self.severity == ErrorSeverity.CRITICAL
            else:
                self.is_fatal = is_fatal
        else:
            # 如果没有提供异常对象，使用提供的信息
            self.error_type = error_type or "UnknownError"
            self.message = message or "未知错误"
            self.traceback_info = traceback_info or ""
            self.category = category or ErrorCategory.UNKNOWN
            self.severity = severity or ErrorSeverity.ERROR
            self.is_fatal = is_fatal if is_fatal is not None else False

        # 转换类别和严重程度为字符串
        if isinstance(self.category, ErrorCategory):
            self.category = self.category.value

        if isinstance(self.severity, ErrorSeverity):
            self.severity = self.severity.value

        self.task_id = task_id
        self.context = context or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典

        Returns:
            错误报告字典
        """
        return {
            "report_id": self.report_id,
            "timestamp": self.timestamp,
            "type": self.error_type,
            "message": self.message,
            "category": self.category,
            "severity": self.severity,
            "is_fatal": self.is_fatal,
            "task_id": self.task_id,
            "context": self.context,
            "traceback": self.traceback_info
        }

    def to_json(self) -> str:
        """
        转换为JSON字符串

        Returns:
            JSON字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)

    def log(self, logger_instance=None):
        """
        记录错误日志

        Args:
            logger_instance: 日志记录器实例，如果为None则使用默认日志记录器
        """
        log = logger_instance or logger

        # 根据严重程度选择日志级别
        if self.severity == "critical":
            log.critical(f"[{self.error_type}] {self.message}")
            log.critical(f"错误详情: {self.to_json()}")
        elif self.severity == "error":
            log.error(f"[{self.error_type}] {self.message}")
            log.error(f"错误详情: {self.to_json()}")
        elif self.severity == "warning":
            log.warning(f"[{self.error_type}] {self.message}")
            log.warning(f"错误详情: {self.to_json()}")
        else:
            log.info(f"[{self.error_type}] {self.message}")
            log.info(f"错误详情: {self.to_json()}")

    @classmethod
    def from_exception(cls, error: Exception, task_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> 'ErrorReport':
        """
        从异常创建错误报告

        Args:
            error: 异常对象
            task_id: 相关任务ID
            context: 错误上下文信息

        Returns:
            错误报告对象
        """
        return cls(error=error, task_id=task_id, context=context)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ErrorReport':
        """
        从字典创建错误报告

        Args:
            data: 错误报告字典

        Returns:
            错误报告对象
        """
        return cls(
            error_type=data.get("type"),
            message=data.get("message"),
            category=data.get("category"),
            severity=data.get("severity"),
            is_fatal=data.get("is_fatal"),
            task_id=data.get("task_id"),
            context=data.get("context"),
            traceback_info=data.get("traceback"),
            timestamp=data.get("timestamp"),
            report_id=data.get("report_id")
        )

    @classmethod
    def from_json(cls, json_str: str) -> 'ErrorReport':
        """
        从JSON字符串创建错误报告

        Args:
            json_str: JSON字符串

        Returns:
            错误报告对象
        """
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except json.JSONDecodeError:
            logger.error(f"解析错误报告JSON失败: {json_str}")
            return cls(
                error_type="JSONDecodeError",
                message=f"解析错误报告JSON失败: {json_str[:100]}...",
                category=ErrorCategory.VALIDATION.value,
                severity=ErrorSeverity.ERROR.value,
                is_fatal=False
            )
