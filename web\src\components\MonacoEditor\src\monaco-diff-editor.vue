<template>
  <div class="monaco-editor-container" :style="{ height: height }">
    <!-- 加载状态 -->
    <div v-if="loading" class="monaco-editor-loading">
      <div class="spinner"></div>
      <div v-if="loadError" class="loading-error">
        <span class="error-message">{{ loadError }}</span>
        <el-button size="small" type="primary" @click="retryLoad">重试</el-button>
      </div>
    </div>
    
    <!-- 差异导航工具栏 -->
    <div v-if="showDiffNavigator && !loading" class="diff-navigator">
      <div class="diff-stats" v-if="showDiffStats && diffStats.total > 0">
        <span class="stat added" title="添加的行数">+{{ diffStats.added }}</span>
        <span class="stat removed" title="删除的行数">-{{ diffStats.removed }}</span>
        <span class="stat changed" title="修改的行数">~{{ diffStats.changed }}</span>
      </div>
      <div class="nav-buttons">
        <el-button link icon="ArrowUp" :disabled="!hasDifferences" @click="navigateToPreviousDiff" />
        <span v-if="hasDifferences" class="diff-counter">{{ currentDiffIndex + 1 }}/{{ totalDiffs }}</span>
        <el-button link icon="ArrowDown" :disabled="!hasDifferences" @click="navigateToNextDiff" />
      </div>
      <div class="view-options">
        <el-button link
          :icon="inlineMode ? 'SplitScreens' : 'CopyDocument'" 
          @click="toggleViewMode" 
          :title="inlineMode ? '切换到并排视图' : '切换到内联视图'" />
      </div>
    </div>
    
    <!-- 编辑器容器 -->
    <div :id="containerId" class="monaco-editor-content"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, reactive } from 'vue';
import { useMonacoLoader } from './useMonaco';
import { getCurrentTheme } from '@/utils/monaco-theme';
import type { MonacoDiffEditorProps, MonacoDiffEditorEmits } from './types';
import { v4 as uuidv4 } from 'uuid';
import type * as Monaco from 'monaco-editor';
import { debounce } from 'lodash-es';

const props = withDefaults(defineProps<MonacoDiffEditorProps>(), {
  language: 'javascript',
  theme: 'vs',
  height: '320px',
  original: '',
  modified: '',
  options: () => ({}),
  readOnly: false,
  showDiffNavigator: false,
  showDiffStats: false,
  inlineDiff: false,
  codeFolding: true,
  performanceMode: 'standard'
});

const emit = defineEmits<MonacoDiffEditorEmits>();

const containerId = computed(() => `monaco-diff-editor-${uuidv4()}`);
const loading = ref(true);
const loadError = ref<string | null>(null);
const { monaco, loading: monacoLoading } = useMonacoLoader();

// 差异导航相关状态
const diffNavigator = ref<any>(null);
const diffStats = reactive({
  added: 0,
  removed: 0,
  changed: 0,
  total: 0
});
const hasDifferences = computed(() => diffStats.total > 0);
const currentDiffIndex = ref(0);
const totalDiffs = ref(0);
const inlineMode = ref(props.inlineDiff);

let editor: Monaco.editor.IStandaloneDiffEditor | null = null;
let originalModel: Monaco.editor.ITextModel | null = null;
let modifiedModel: Monaco.editor.ITextModel | null = null;
let resizeObserver: ResizeObserver | null = null;
let themeObserver: MutationObserver | null = null;

// 提前注册清理函数
onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleWindowResize);

  // 清理主题观察器
  if (themeObserver) {
    themeObserver.disconnect();
  }
  
  // 清理调整大小观察器
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  
  // 清理编辑器实例
  if (editor) {
    editor.dispose();
    editor = null;
  }
  
  // 清理模型
  if (originalModel) {
    originalModel.dispose();
    originalModel = null;
  }
  
  if (modifiedModel) {
    modifiedModel.dispose();
    modifiedModel = null;
  }
});

// 获取性能模式对应的编辑器选项
const getPerformanceOptions = (mode: string): Partial<Monaco.editor.IDiffEditorConstructionOptions> => {
  switch (mode) {
    case 'quickDiff':
      return {
        ignoreTrimWhitespace: true,
        renderSideBySide: !inlineMode.value,
        renderOverviewRuler: false,
        maxComputationTime: 1000, // 限制差异计算时间
        maxFileSize: 50000 // 更大的文件大小限制
      };
    case 'advanced':
      return {
        ignoreTrimWhitespace: false,
        renderSideBySide: !inlineMode.value,
        renderOverviewRuler: true,
        maxComputationTime: 5000 // 允许更多的计算时间，以获得更精确的差异
      };
    default: // standard
      return {
        ignoreTrimWhitespace: false,
        renderSideBySide: !inlineMode.value,
        renderOverviewRuler: true
      };
  }
};

// 观察主题变化的函数
const observeThemeChanges = () => {
  // 创建MutationObserver来监听DOM变化
  themeObserver = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'attributes' && 
          (mutation.attributeName === 'class' || 
           mutation.attributeName === 'data-theme' || 
           mutation.attributeName === 'style')) {
        updateEditorTheme();
      }
    }
  });

  // 监听文档根元素的class和data-theme属性变化
  themeObserver.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class', 'data-theme', 'style'] 
  });
  
  // 也监听body元素
  themeObserver.observe(document.body, { 
    attributes: true, 
    attributeFilter: ['data-theme'] 
  });

  // 在组件销毁时断开观察
  onBeforeUnmount(() => {
    themeObserver.disconnect();
  });
};

// 创建差异编辑器
const createEditor = async () => {
  if (!monaco.value) return;
  
  try {
    loadError.value = null;
    
    // 获取容器元素
    const container = document.getElementById(containerId.value);
    if (!container) {
      loadError.value = '找不到差异编辑器容器';
      return;
    }
    
    // 创建原始和修改后的模型
    const originalUri = monaco.value.Uri.parse(`original-${containerId.value}`);
    const modifiedUri = monaco.value.Uri.parse(`modified-${containerId.value}`);
    
    // 如果模型已存在，则重用
    originalModel = monaco.value.editor.getModel(originalUri) || 
                   monaco.value.editor.createModel(props.original, props.language, originalUri);
    modifiedModel = monaco.value.editor.getModel(modifiedUri) || 
                   monaco.value.editor.createModel(props.modified, props.language, modifiedUri);
    
    // 编辑器默认选项
    const defaultOptions: Monaco.editor.IDiffEditorConstructionOptions = {
      originalEditable: false,
      readOnly: props.readOnly,
      renderSideBySide: !inlineMode.value,
      automaticLayout: true,
      folding: props.codeFolding,
      foldingStrategy: 'auto',
      renderLineHighlight: 'all',
      contextmenu: true,
      scrollBeyondLastLine: false,
      minimap: { enabled: true },
      wordWrap: 'off'
    };
    
    // 合并性能选项
    const performanceOptions = getPerformanceOptions(props.performanceMode);
    
    // 创建差异编辑器
    editor = monaco.value.editor.createDiffEditor(
      container,
      { ...defaultOptions, ...performanceOptions, ...props.options }
    );
    
    // 应用主题
    try {
      const themeToApply = props.theme || getCurrentTheme();
      monaco.value.editor.setTheme(themeToApply);
    } catch (error) {
      console.error('设置差异编辑器主题失败:', error);
    }
    
    // 设置模型
    editor.setModel({
      original: originalModel,
      modified: modifiedModel
    });
    
    // 创建差异导航
    if (props.showDiffNavigator && monaco.value) {
      // 检查是否支持DiffNavigator
      if (monaco.value.editor && 'createDiffNavigator' in monaco.value.editor) {
        try {
          // @ts-ignore - Monaco编辑器类型定义可能不完整
          diffNavigator.value = monaco.value.editor.createDiffNavigator(editor, {
            followsCaret: true,
            ignoreCharChanges: false,
            alwaysRevealFirst: true
          });
          
          // 监听差异导航变化
          if (diffNavigator.value && typeof diffNavigator.value.onDidUpdate === 'function') {
            diffNavigator.value.onDidUpdate((e: any) => {
              if (e.total !== undefined) {
                totalDiffs.value = e.total;
              }
              if (e.index !== undefined) {
                currentDiffIndex.value = e.index;
              }
            });
          }
        } catch (error) {
          console.error('创建差异导航器失败:', error);
        }
      }
    }
    
    // 监听修改事件
    modifiedModel.onDidChangeContent(() => {
      const value = modifiedModel?.getValue() || '';
      emit('update:modified', value);
      emit('change', value);
      
      // 更新差异统计信息
      updateDiffStats();
    });
    
    // 更新差异统计信息
    updateDiffStats();
    
    // 触发编辑器挂载事件
    emit('editor-mounted', {
      editor,
      monaco: monaco.value
    });
    
    // 更新布局
    editor.layout();
    
    // 加载完成
    loading.value = false;
    
    // 设置完成后自动聚焦到第一个差异
    if (props.showDiffNavigator && diffNavigator.value && hasDifferences.value) {
      setTimeout(() => {
        navigateToFirstDiff();
      }, 300);
    }
    
    // 添加自适应调整大小
    setupResizeObserver();
    
    // 启动主题变化监听
    observeThemeChanges();
    
  } catch (error) {
    console.error('差异编辑器初始化失败:', error);
    loading.value = false;
    loadError.value = error instanceof Error ? error.message : '差异编辑器初始化失败';
  }
};

// 重试加载
const retryLoad = () => {
  if (editor) {
    editor.dispose();
    editor = null;
  }
  
  loading.value = true;
  createEditor();
};

// 更新编辑器内容
const updateContent = () => {
  if (!monaco.value || !editor) return;
  
  try {
    if (originalModel && originalModel.getValue() !== props.original) {
      originalModel.setValue(props.original);
    }
    
    if (modifiedModel && modifiedModel.getValue() !== props.modified) {
      modifiedModel.setValue(props.modified);
    }
    
    // 重新计算差异
    editor.layout();
    
    // 更新差异统计信息
    updateDiffStats();
  } catch (error) {
    console.error('更新差异编辑器内容失败:', error);
  }
};

// 更新差异统计信息
const updateDiffStats = debounce(() => {
  if (!editor) return;
  
  try {
    // 获取差异信息
    const diffs = editor.getLineChanges();
    
    if (diffs) {
      let added = 0;
      let removed = 0;
      let changed = 0;
      
      diffs.forEach(diff => {
        const originalLineCount = diff.originalEndLineNumber - diff.originalStartLineNumber + 1;
        const modifiedLineCount = diff.modifiedEndLineNumber - diff.modifiedStartLineNumber + 1;
        
        if (originalLineCount === 0) {
          // 仅添加行
          added += modifiedLineCount;
        } else if (modifiedLineCount === 0) {
          // 仅删除行
          removed += originalLineCount;
        } else {
          // 修改行
          changed += Math.max(originalLineCount, modifiedLineCount);
        }
      });
      
      // 更新统计信息
      diffStats.added = added;
      diffStats.removed = removed;
      diffStats.changed = changed;
      diffStats.total = diffs.length;
      totalDiffs.value = diffs.length;
      
      // 发送差异统计事件
      emit('diff-stats', { ...diffStats });
    }
  } catch (error) {
    console.error('计算差异统计信息失败:', error);
  }
}, 300);

// 导航到下一个差异
const navigateToNextDiff = () => {
  if (diffNavigator.value && hasDifferences.value) {
    diffNavigator.value.next();
    const position = editor?.getPosition();
    if (position) {
      emit('navigate-diff', position);
    }
  }
};

// 导航到上一个差异
const navigateToPreviousDiff = () => {
  if (diffNavigator.value && hasDifferences.value) {
    diffNavigator.value.previous();
    const position = editor?.getPosition();
    if (position) {
      emit('navigate-diff', position);
    }
  }
};

// 导航到第一个差异
const navigateToFirstDiff = () => {
  if (diffNavigator.value && hasDifferences.value) {
    diffNavigator.value.first();
    currentDiffIndex.value = 0;
  }
};

// 切换视图模式（内联/并排）
const toggleViewMode = () => {
  if (!editor) return;
  
  inlineMode.value = !inlineMode.value;
  editor.updateOptions({
    renderSideBySide: !inlineMode.value
  });
  
  // 更新布局
  setTimeout(() => {
    editor?.layout();
  }, 50);
};

// 设置自适应大小调整
const setupResizeObserver = () => {
  if (!resizeObserver && typeof ResizeObserver !== 'undefined') {
    const container = document.getElementById(containerId.value);
    if (container) {
      resizeObserver = new ResizeObserver(debounce(() => {
        if (editor) {
          editor.layout();
        }
      }, 100));
      resizeObserver.observe(container);
    }
  }
};

// 监听Monaco加载状态
watch(() => monacoLoading.value, (newLoading) => {
  if (!newLoading && monaco.value) {
    createEditor();
  }
});

// 监听属性变化
watch(() => props.original, () => updateContent());
watch(() => props.modified, () => updateContent());
watch(() => props.theme, (newTheme) => {
  if (monaco.value && editor) {
    try {
      // 应用新主题
      const themeToApply = newTheme || getCurrentTheme();
      monaco.value.editor.setTheme(themeToApply);
    } catch (error) {
      console.error('设置差异编辑器主题失败:', error);
    }
  }
});
watch(() => props.language, (newLanguage) => {
  if (!monaco.value || !originalModel || !modifiedModel) return;
  
  monaco.value.editor.setModelLanguage(originalModel, newLanguage);
  monaco.value.editor.setModelLanguage(modifiedModel, newLanguage);
});
watch(() => props.inlineDiff, (newValue) => {
  inlineMode.value = newValue;
  if (editor) {
    editor.updateOptions({
      renderSideBySide: !newValue
    });
  }
});
watch(() => props.performanceMode, (newMode) => {
  if (editor) {
    const performanceOptions = getPerformanceOptions(newMode);
    editor.updateOptions(performanceOptions);
  }
});

// 组件挂载
onMounted(() => {
  if (!monacoLoading.value && monaco.value) {
    createEditor();
  }
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleWindowResize);
});

// 处理窗口大小变化
const handleWindowResize = debounce(() => {
  if (editor) {
    editor.layout();
  }
}, 100);

// 公开方法
defineExpose({
  getEditor: () => editor,
  updateLayout: () => editor?.layout(),
  navigateToNextDiff,
  navigateToPreviousDiff,
  navigateToFirstDiff,
  toggleViewMode,
  getDiffStats: () => ({ ...diffStats })
});

// 在适当位置添加主题更新函数
const updateEditorTheme = () => {
  if (monaco.value && editor) {
    try {
      const themeToApply = getCurrentTheme();
      monaco.value.editor.setTheme(themeToApply);
    } catch (error) {
      console.error('更新差异编辑器主题失败:', error);
    }
  }
};
</script>

<style lang="scss" scoped>
.monaco-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  border: 1px solid var(--el-border-color-light);
  border-radius: 0;
  overflow: hidden;
  
  /* 确保内部编辑器没有额外边框 */
  :deep(.monaco-editor), :deep(.monaco-diff-editor) {
    border: none !important;
  }
}

.monaco-editor-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.monaco-editor-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--el-bg-color);
  z-index: 10;
}

.diff-navigator {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  padding: 4px 8px;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 5;
  border-bottom-left-radius: 4px;
  
  .diff-stats {
    display: flex;
    align-items: center;
    margin-right: 10px;
    
    .stat {
      margin: 0 4px;
      padding: 1px 4px;
      border-radius: 3px;
      font-size: 12px;
      
      &.added {
        background-color: rgba(0, 255, 0, 0.1);
        color: #2db7f5;
      }
      
      &.removed {
        background-color: rgba(255, 0, 0, 0.1);
        color: #ff4d4f;
      }
      
      &.changed {
        background-color: rgba(255, 165, 0, 0.1);
        color: #faad14;
      }
    }
  }
  
  .nav-buttons {
    display: flex;
    align-items: center;
    
    .diff-counter {
      font-size: 12px;
      margin: 0 5px;
    }
  }
  
  .view-options {
    margin-left: 10px;
    border-left: 1px solid var(--el-border-color-light);
    padding-left: 10px;
  }
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-left-color: var(--el-color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-error {
  margin-top: 15px;
  text-align: center;
  
  .error-message {
    color: var(--el-color-danger);
    margin-bottom: 10px;
    display: block;
  }
}
</style>
 