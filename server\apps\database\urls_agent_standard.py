"""
基于Agent标准的URL配置
提供只读的数据查询API和简单的Agent交互接口
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views_agent_standard import (
    AgentConnectionViewSet,
    AgentComparisonModelViewSet,
    AgentComparisonTaskViewSet,
    AgentDashboardViewSet,
    AgentHealthViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'connections', AgentConnectionViewSet, basename='agent-connections')
router.register(r'models', AgentComparisonModelViewSet, basename='agent-models')
router.register(r'tasks', AgentComparisonTaskViewSet, basename='agent-tasks')
router.register(r'dashboard', AgentDashboardViewSet, basename='agent-dashboard')
router.register(r'health', AgentHealthViewSet, basename='agent-health')

# URL模式
urlpatterns = [
    # Agent标准API
    path('api/v1/agent/', include(router.urls)),
    
    # 兼容性路由（可选）
    path('api/v1/', include(router.urls)),
]

# 为前端提供的主要API端点说明
"""
主要API端点：

1. 数据库连接管理（只读）
   GET /api/v1/agent/connections/                    # 获取所有连接
   GET /api/v1/agent/connections/{id}/               # 获取单个连接
   POST /api/v1/agent/connections/{id}/test_connection/  # 测试连接

2. 比对模型管理（只读）
   GET /api/v1/agent/models/                         # 获取所有模型
   GET /api/v1/agent/models/{id}/                    # 获取单个模型
   GET /api/v1/agent/models/{id}/table_rules/        # 获取模型的表规则
   GET /api/v1/agent/models/{id}/recent_tasks/       # 获取模型的最近任务

3. 比对任务管理
   GET /api/v1/agent/tasks/                          # 获取所有任务
   GET /api/v1/agent/tasks/{id}/                     # 获取单个任务
   POST /api/v1/agent/tasks/create_task/             # 创建新任务（通过Agent）
   POST /api/v1/agent/tasks/{id}/control_task/       # 控制任务（通过Agent）
   GET /api/v1/agent/tasks/{id}/progress/            # 获取任务进度
   GET /api/v1/agent/tasks/{id}/results/             # 获取任务结果

4. 仪表板统计
   GET /api/v1/agent/dashboard/stats/                # 获取统计信息
   GET /api/v1/agent/dashboard/recent_tasks/         # 获取最近任务
   GET /api/v1/agent/dashboard/running_tasks/        # 获取运行中任务

5. 健康检查
   GET /api/v1/agent/health/check/                   # 检查Agent健康状态

查询参数支持：

连接查询：
- ?active_only=true                                  # 只返回活跃连接

模型查询：
- ?active_only=true                                  # 只返回活跃模型

任务查询：
- ?status=running                                    # 按状态过滤
- ?model_id=1                                        # 按模型过滤
- ?user_id=user123                                   # 按用户过滤
- ?start_date=2024-01-01                            # 按开始日期过滤
- ?end_date=2024-12-31                              # 按结束日期过滤

任务结果查询：
- ?status=D                                          # 按差异状态过滤（D=差异，M=匹配，S=仅源端，T=仅目标端）
- ?table_name=table1                                 # 按表名过滤
- ?page=1&page_size=50                              # 分页参数

请求体格式：

创建任务：
POST /api/v1/agent/tasks/create_task/
{
    "model_id": 1,
    "table_rule_ids": [1, 2, 3],  // 可选，为空则使用模型的所有规则
    "task_name": "测试任务",       // 可选
    "description": "任务描述",     // 可选
    "user_id": "user123"
}

控制任务：
POST /api/v1/agent/tasks/{id}/control_task/
{
    "action": "start|pause|resume|cancel",
    "task_id": "task_123"
}

响应格式：

成功响应：
{
    "success": true,
    "message": "操作成功",
    "data": { ... }
}

错误响应：
{
    "success": false,
    "message": "错误描述",
    "error": "详细错误信息"
}

分页响应：
{
    "total": 100,
    "page": 1,
    "page_size": 50,
    "results": [ ... ]
}
"""
