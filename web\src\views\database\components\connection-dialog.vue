<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑数据库连接' : '创建数据库连接'"
    width="600px"
    destroy-on-close
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="连接名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入连接名称" />
      </el-form-item>
      
      <el-form-item label="数据库类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择数据库类型" style="width: 100%">
          <el-option label="MySQL" value="mysql" />
          <el-option label="Oracle" value="oracle" />
          <el-option label="PostgreSQL" value="postgresql" />
          <el-option label="SQL Server" value="sqlserver" />
          <el-option label="DB2" value="db2" />
          <el-option label="GaussDB" value="gaussdb" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="主机地址" prop="host">
        <el-input v-model="form.host" placeholder="请输入主机地址" />
      </el-form-item>
      
      <el-form-item label="端口" prop="port">
        <el-input-number v-model="form.port" :min="1" :max="65535" style="width: 100%" />
      </el-form-item>
      
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          :placeholder="passwordPlaceholder"
          show-password
        />
        <span v-if="hasExistingPassword && !passwordChanged" class="password-hint">
          <el-icon><Lock /></el-icon> 密码已保存
        </span>
      </el-form-item>
      
      <el-form-item label="数据库名" prop="database">
        <el-input v-model="form.database" placeholder="请输入数据库名" />
      </el-form-item>
      
      <el-collapse>
        <el-collapse-item title="高级选项" name="advanced">
          <el-form-item label="连接选项">
            <el-input
              v-model="advancedOptionsStr"
              type="textarea"
              :rows="4"
              placeholder="请输入高级连接选项，格式为JSON对象，例如：{&quot;charset&quot;:&quot;utf8mb4&quot;,&quot;timezone&quot;:&quot;+08:00&quot;}"
            />
          </el-form-item>
          
          <el-form-item>
            <el-checkbox v-model="form.savePassword">保存密码</el-checkbox>
            <el-tooltip
              content="若不保存密码，需要在每次使用连接时手动输入"
              placement="top"
            >
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>
    
    <div class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
      <el-button type="info" @click="handleTest">测试连接</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { InfoFilled, Lock } from '@element-plus/icons-vue';
import type { DataConnection } from '@/types/database';
import { testConnection } from '@/api/database/index';
import { useConnectionStore } from '@/store/modules/connection';

const props = defineProps<{
  visible: boolean;
  connectionData?: DataConnection;
}>();

const emit = defineEmits(['update:visible', 'submit', 'test-connection']);

// 表单引用
const formRef = ref();

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit('update:visible', val);
    // 如果关闭对话框，也确保重置表单状态
    if (!val) {
      resetForm();
    }
  }
});

// 编辑模式判断
const isEdit = computed(() => !!props.connectionData?.id);

// 表单数据
const form = reactive({
  id: '',
  name: '',
  type: 'mysql',
  host: 'localhost',
  port: 3306,
  username: '',
  password: '',
  database: '',
  savePassword: true,
  parameters: {}
});

// 高级选项字符串
const advancedOptionsStr = ref('{}');

// 数据库类型对应的默认端口
const defaultPorts = {
  mysql: 3306,
  oracle: 1521,
  postgresql: 5432,
  sqlserver: 1433,
  db2: 50000,
  gaussdb: 25308
};

// 监听数据库类型变化，自动修改默认端口
// 但仅在新建连接或用户手动切换类型时生效，避免覆盖已有数据
let isInitialLoad = true;
let previousType = '';

watch(() => form.type, (newType) => {
  // 初始加载数据时记录初始类型但不修改端口
  if (isInitialLoad) {
    previousType = newType;
    isInitialLoad = false;
    return;
  }
  
  // 只有用户手动切换类型时才重置为默认端口
  if (newType !== previousType) {
    previousType = newType;
    form.port = defaultPorts[newType as keyof typeof defaultPorts] || 3306;
  }
});

// 监听高级选项变化
watch(advancedOptionsStr, (newVal) => {
  try {
    form.parameters = JSON.parse(newVal);
  } catch (error) {
    // 解析失败时不更新参数
    console.error('高级选项格式错误', error);
  }
}, { deep: true });

// 密码处理相关变量
const passwordPlaceholder = ref('');
const hasExistingPassword = ref(false);
const passwordChanged = ref(false);

// 监听传入的连接数据
watch(() => props.connectionData, (newVal) => {
  if (newVal) {
    resetForm();
    
    // 重置数据库类型监听的初始标志
    isInitialLoad = true;
    
    // 设置密码占位符和状态
    if (newVal.id) {
      // 编辑模式
      hasExistingPassword.value = true;
      passwordPlaceholder.value = '••••••••'; // 使用占位符表示已有密码
      passwordChanged.value = false;
    } else {
      // 新建模式
      hasExistingPassword.value = false;
      passwordPlaceholder.value = '请输入密码';
      passwordChanged.value = true;
    }
    
    // 填充表单数据，但不填充密码
    Object.assign(form, {
      id: newVal.id || '',
      name: newVal.name || '',
      type: newVal.type || 'mysql',
      host: newVal.host || 'localhost',
      port: newVal.port || defaultPorts[newVal.type as keyof typeof defaultPorts] || 3306,
      username: newVal.username || '',
      password: '', // 编辑模式时不回显密码，只有新增时才可能有密码
      database: newVal.database || '',
      parameters: newVal.parameters || {},
      savePassword: true,
    });
    
    // 更新高级选项字符串
    advancedOptionsStr.value = JSON.stringify(form.parameters, null, 2);
    
    // 防止初始加载后立即被数据库类型监听器重置端口
    previousType = form.type;
  }
}, { immediate: true });

// 监听密码字段变化
watch(() => form.password, (newVal) => {
  if (hasExistingPassword.value && newVal) {
    passwordChanged.value = true;
  }
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度应为2到50个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ]
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  // 恢复默认值
  Object.assign(form, {
    id: '',
    name: '',
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: '',
    password: '',
    database: '',
    savePassword: true,
    parameters: {}
  });
  
  advancedOptionsStr.value = '{}';
};

// 在setup中添加
const connectionStore = useConnectionStore();

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    // 如果是编辑模式且有已存在密码，则临时移除密码验证规则
    if (isEdit.value && hasExistingPassword.value && !passwordChanged.value) {
      const tempRules = JSON.parse(JSON.stringify(rules));
      tempRules.password = [];
      await formRef.value.validate(tempRules);
    } else {
      await formRef.value.validate();
    }
    
    // 检查ID是否有效（可能已在其他地方被删除）
    if (isEdit.value && form.id) {
      const deletedIds = connectionStore.getValidDeletedIds();
      const deletedIdStrings = deletedIds.map(item => item.id);
      
      if (deletedIdStrings.includes(String(form.id))) {
        ElMessageBox.alert(
          '该连接记录可能已被删除，无法保存更改。请创建新的连接。',
          '连接不存在',
          { type: 'warning' }
        );
        return;
      }
    }
    
    // 尝试解析高级选项
    try {
      form.parameters = JSON.parse(advancedOptionsStr.value);
    } catch (error) {
      ElMessage.error('高级选项格式错误，请检查JSON格式');
      return;
    }
    
    // 构建提交数据
    const submitData: DataConnection = {
      id: form.id,
      name: form.name,
      type: form.type,
      host: form.host,
      port: form.port,
      username: form.username,
      database: form.database,
      parameters: form.parameters || {}
    };
    
    // 只有以下情况才包含密码字段：
    // 1. 新建连接时 (isEdit=false)
    // 2. 编辑连接且密码已被修改 (passwordChanged=true)
    if (!isEdit.value || (isEdit.value && passwordChanged.value && form.password)) {
      submitData.password = form.password;
    } else {
      // 编辑模式下，如果密码没有修改，则不传递密码字段，后端会保留原密码
      console.log('未修改密码，不提交密码字段');
    }
    
    // 提交表单
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败', error);
    ElMessage.error('请完善表单信息');
  }
};

// 测试连接
const handleTest = async () => {
  if (!formRef.value) return;
  
  try {
    // 与提交表单使用相同的验证逻辑
    // 如果是编辑模式且有已存在密码，则临时移除密码验证规则
    if (isEdit.value && hasExistingPassword.value && !passwordChanged.value) {
      const tempRules = JSON.parse(JSON.stringify(rules));
      tempRules.password = [];
      await formRef.value.validate(tempRules);
    } else {
      await formRef.value.validate();
    }
    
    // 检查ID是否有效（可能已在其他地方被删除）
    if (isEdit.value && form.id) {
      const deletedIds = connectionStore.getValidDeletedIds();
      const deletedIdStrings = deletedIds.map(item => item.id);
      
      if (deletedIdStrings.includes(String(form.id))) {
        ElMessageBox.alert(
          '该连接记录可能已被删除，无法测试。请创建新的连接。',
          '连接不存在',
          { type: 'warning' }
        );
        return;
      }
    }
    
    // 尝试解析高级选项
    try {
      form.parameters = JSON.parse(advancedOptionsStr.value);
    } catch (error) {
      ElMessage.error('高级选项格式错误，请检查JSON格式');
      return;
    }
    
    // 构建测试连接数据
    const testData: {
      id?: string;
      type: string;
      host: string;
      port: number;
      username: string;
      database: string;
      parameters: any;
      password?: string;
    } = {
      id: form.id, // 包含ID以便后端查询完整信息
      type: form.type,
      host: form.host,
      port: parseInt(form.port.toString(), 10),
      username: form.username,
      database: form.database,
      parameters: form.parameters
    };
    
    // 处理密码：编辑模式且密码未修改时，不传递密码字段，让后端使用ID查询
    if (!isEdit.value || (isEdit.value && passwordChanged.value && form.password)) {
      testData.password = form.password;
    }
    
    ElMessage.info('正在测试连接...');
        
    // 发送测试请求
    try {
      const res = await testConnection(testData);
      
      if (res && res.code === 0) {
        ElMessage.success('测试连接成功');
      } else {
        ElMessage.error(`测试连接失败: ${res?.message || '未知错误'}`);
      }
    } catch (error: any) {
      ElMessage.error(`测试连接失败: ${error?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('表单验证失败', error);
    ElMessage.error('请完善表单信息');
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

.info-icon {
  margin-left: 5px;
  color: var(--el-color-info);
  cursor: help;
}

.password-hint {
  margin-left: 10px;
  font-size: 0.8em;
  color: var(--el-color-info);
}
</style> 