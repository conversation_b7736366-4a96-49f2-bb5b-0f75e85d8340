/**
 * Monaco Editor实例缓存模块
 * 用于在页面切换时保留编辑器实例，减少重新初始化的时间
 */

import type * as Monaco from 'monaco-editor';
import { getMonaco } from './monaco-loader';

// 缓存的编辑器实例
const editorInstances = new Map<string, any>();
// 最大缓存数量
const MAX_CACHE_SIZE = 10;
// 使用时间记录，用于LRU清理
const usageTimestamp = new Map<string, number>();

/**
 * 创建或获取缓存的编辑器实例
 * @param containerId 容器ID
 * @param container DOM容器元素
 * @param options 编辑器选项
 * @returns Monaco编辑器实例
 */
export async function getOrCreateEditor(
  containerId: string,
  container: HTMLElement,
  options: Monaco.editor.IStandaloneEditorConstructionOptions
): Promise<any> {
  try {   
    // 标记使用时间
    usageTimestamp.set(containerId, Date.now());
    
    // 如果已有缓存实例，先销毁旧的DOM关联
    if (editorInstances.has(containerId)) {
      console.log(`[Monaco] 使用缓存实例: ${containerId}`);
      const editor = editorInstances.get(containerId);
      
      // 更新编辑器选项
      editor.updateOptions(options);
      
      // 如果容器变化，需要更换容器
      const currentContainer = editor.getContainerDomNode();
      if (currentContainer !== container) {
        console.log(`[Monaco] 容器已变化，更新DOM关联`);
        editor._domElement = container;
        editor.layout();
      }
      
      return editor;
    }
    
    // 检查缓存是否已满，如果满了则清理最久未使用的实例
    if (editorInstances.size >= MAX_CACHE_SIZE) {
      cleanLeastRecentlyUsed();
    }
    
    // 创建新实例
    const monaco = await getMonaco();
    
    if (!monaco) {
      throw new Error("Monaco加载失败，getMonaco返回undefined或null");
    }
    
    if (!monaco.editor) {
      throw new Error("Monaco加载不完整，editor API不存在");
    }
        
    if (!container) {
      throw new Error("无效的编辑器容器，DOM元素为null或undefined");
    }
    
    const editor = monaco.editor.create(container, options);
    
    if (!editor) {
      throw new Error("编辑器创建失败，monaco.editor.create返回null");
    }
    
    console.log(`[Monaco] 编辑器创建成功: ${containerId}`);
    
    // 缓存实例
    editorInstances.set(containerId, editor);
    usageTimestamp.set(containerId, Date.now());
    
    return editor;
  } catch (error) {
    console.error(`[Monaco] 创建编辑器失败:`, error);
    throw error;
  }
}

/**
 * 清理编辑器实例缓存
 * @param containerId 容器ID
 */
export function clearEditor(containerId: string): void {
  if (editorInstances.has(containerId)) {
    console.log(`[Monaco] 清理编辑器实例: ${containerId}`);
    const editor = editorInstances.get(containerId);
    editor.dispose();
    editorInstances.delete(containerId);
    usageTimestamp.delete(containerId);
  }
}

/**
 * 清理最久未使用的编辑器实例
 */
function cleanLeastRecentlyUsed(): void {
  if (usageTimestamp.size === 0) return;
  
  // 找到最久未使用的ID
  let oldestId = '';
  let oldestTime = Date.now();
  
  for (const [id, time] of usageTimestamp.entries()) {
    if (time < oldestTime) {
      oldestTime = time;
      oldestId = id;
    }
  }
  
  // 清理最久未使用的实例
  if (oldestId) {
    console.log(`[Monaco] LRU清理最久未使用的实例: ${oldestId}`);
    clearEditor(oldestId);
  }
}

/**
 * 清理所有编辑器实例
 */
export function clearAllEditors(): void {
  for (const [id] of editorInstances) {
    clearEditor(id);
  }
}

/**
 * 在页面卸载前清理缓存
 */
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    clearAllEditors();
  });
}

export default {
  getOrCreateEditor,
  clearEditor,
  clearAllEditors
}; 