<template>
  <div class="comparison-result-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>比对结果</h1>
      <p class="page-description">查看和分析数据库比对结果</p>
    </div>

    <!-- 任务选择器 -->
    <div class="task-selector-section">
      <el-card>
        <template #header>
          <span>选择比对任务</span>
        </template>
        <el-select
          v-model="selectedTaskId"
          placeholder="请选择要查看的比对任务"
          style="width: 100%"
          @change="handleTaskChange"
        >
          <el-option
            v-for="task in availableTasks"
            :key="task.taskId"
            :label="`${task.name || task.taskId} - ${task.status}`"
            :value="task.taskId"
          />
        </el-select>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-section">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 结果展示 -->
    <div v-else-if="comparisonResult" class="result-container">
      <!-- 摘要信息卡片 -->
      <el-card class="summary-card">
        <template #header>
          <span>比对摘要</span>
        </template>
        <div class="summary-content">
          <div class="stat-item">
            <span class="label">总记录数:</span>
            <span class="value">{{ comparisonResult.summary.totalRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">匹配记录:</span>
            <span class="value">{{ comparisonResult.summary.matchedRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">差异记录:</span>
            <span class="value">{{ comparisonResult.summary.diffRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">源独有:</span>
            <span class="value">{{ comparisonResult.summary.sourceOnlyRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">目标独有:</span>
            <span class="value">{{ comparisonResult.summary.targetOnlyRecords }}</span>
          </div>
          <div class="stat-item">
            <span class="label">执行时间:</span>
            <span class="value">{{ comparisonResult.summary.executionTime }}秒</span>
          </div>
        </div>
      </el-card>

      <!-- 详细结果标签页 -->
      <el-card class="result-tabs-card">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="差异记录" name="differences">
            <DifferenceTable
              :task-id="selectedTaskId"
              :total="comparisonResult.summary.diffRecords || 0"
            />
          </el-tab-pane>

          <el-tab-pane label="源独有记录" name="sourceOnly">
            <SourceOnlyTable
              :task-id="selectedTaskId"
              :total="comparisonResult.summary.sourceOnlyRecords || 0"
            />
          </el-tab-pane>

          <el-tab-pane label="目标独有记录" name="targetOnly">
            <TargetOnlyTable
              :task-id="selectedTaskId"
              :total="comparisonResult.summary.targetOnlyRecords || 0"
            />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请选择要查看的比对任务" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getComparisonResult, getCompareTaskHistory, type ComparisonResultSummary } from '@/api/database/compare';
import { handleDatabaseError, showErrorMessage } from '@/api/database/error';
import { transformComparisonResult } from '@/utils/DataTransformUtil';
import DifferenceTable from './components/DifferenceTable.vue';
import SourceOnlyTable from './components/SourceOnlyTable.vue';
import TargetOnlyTable from './components/TargetOnlyTable.vue';

// 响应式数据
const selectedTaskId = ref('');
const activeTab = ref('differences');
const loading = ref(false);
const comparisonResult = ref<ComparisonResultSummary | null>(null);
const availableTasks = ref<any[]>([]);

// 加载可用任务列表
const loadAvailableTasks = async () => {
  try {
    const response = await getCompareTaskHistory({ status: 'success' });
    if (response.success && response.data) {
      availableTasks.value = response.data.map(task => ({
        taskId: String(task.external_id || task.id),
        name: task.name || `任务-${task.id}`,
        status: task.status,
        createdAt: task.create_time,
      }));
    }
  } catch (error) {
    console.error('加载任务列表失败:', error);
    // 如果API调用失败，使用模拟数据
    availableTasks.value = [
      {
        taskId: 'task-001',
        name: '用户表比对',
        status: 'success',
        createdAt: new Date().toISOString(),
      }
    ];
  }
};

// 加载比对结果
const loadComparisonResult = async () => {
  if (!selectedTaskId.value) {
    comparisonResult.value = null;
    return;
  }

  loading.value = true;
  try {
    console.log('🔍 开始加载比对结果，任务ID:', selectedTaskId.value);
    const response = await getComparisonResult(selectedTaskId.value);

    console.log('📡 API响应原始数据:', response);
    console.log('✅ API响应成功状态:', response.success);
    console.log('📊 API响应数据:', response.data);

    // 详细分析API响应数据结构
    if (response.data) {
      console.log('🔍 详细数据分析:');
      console.log('- taskId/task_id:', response.data.task_id || response.data.taskId);
      console.log('- status:', response.data.status);
      console.log('- summary 原始结构:', response.data.summary);

      if (response.data.summary) {
        console.log('- summary 字段详情:');
        Object.keys(response.data.summary).forEach(key => {
          console.log(`  - ${key}: ${response.data.summary[key]} (${typeof response.data.summary[key]})`);
        });
      }

      if (response.data.details) {
        console.log('- details 结构:', response.data.details);
        if (response.data.details.items) {
          console.log('- details.items 长度:', response.data.details.items.length);
        }
      }
    }

    if (response.success && response.data) {
      try {
        // 使用标准化的转换工具转换后端数据字段名到前端期望的格式
        const transformedData = transformComparisonResult(response.data, true); // 启用调试模式
        console.log('🔄 转换后的数据:', transformedData);

        // 验证转换后的数据结构
        if (!transformedData) {
          throw new Error('数据转换结果为空');
        }

        if (!transformedData.summary) {
          console.warn('⚠️ 转换后的数据缺少 summary 字段');
          // 尝试手动构建 summary
          transformedData.summary = {
            totalRecords: 0,
            matchedRecords: 0,
            diffRecords: 0,
            sourceOnlyRecords: 0,
            targetOnlyRecords: 0,
            errorRecords: 0,
            executionTime: 0
          };
        }

        console.log('✅ 最终设置的数据:', transformedData);
        comparisonResult.value = transformedData;
        ElMessage.success('比对结果加载成功');

      } catch (transformError) {
        console.error('❌ 数据转换失败:', transformError);
        console.error('原始数据:', response.data);
        throw new Error(`数据转换失败: ${transformError.message}`);
      }
    } else {
      console.error('❌ API响应失败:', response.message);
      throw new Error(response.message || '获取比对结果失败');
    }
  } catch (error) {
    console.error('💥 加载比对结果失败:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      taskId: selectedTaskId.value
    });

    const dbError = handleDatabaseError(error);
    showErrorMessage(dbError);
    comparisonResult.value = null;
  } finally {
    loading.value = false;
  }
};

// 处理任务选择变化
const handleTaskChange = () => {
  // 移除重复调用，因为watch已经处理了
  console.log('📝 任务选择变化，新任务ID:', selectedTaskId.value);
};

// 监听任务ID变化
watch(selectedTaskId, (newTaskId, oldTaskId) => {
  console.log('👀 监听到任务ID变化:', { oldTaskId, newTaskId });
  if (newTaskId && newTaskId !== oldTaskId) {
    loadComparisonResult();
  }
});

// 组件挂载时加载数据
onMounted(() => {
  loadAvailableTasks();
});
</script>

<style scoped>
.comparison-result-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.task-selector-section {
  margin-bottom: 24px;
}

.loading-section {
  margin: 24px 0;
}

.result-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-card {
  margin-bottom: 24px;
}

.summary-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.stat-item .label {
  font-weight: 500;
  color: #6b7280;
}

.stat-item .value {
  font-weight: 600;
  color: #1f2937;
}

.result-tabs-card {
  min-height: 400px;
}

.empty-state {
  margin: 48px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-result-page {
    padding: 16px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .result-container {
    gap: 16px;
  }

  .summary-content {
    grid-template-columns: 1fr;
  }
}
</style>
