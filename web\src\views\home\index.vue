<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>欢迎使用 ARK回溯反演平台</span>
            </div>
          </template>
          <div class="card-content">
            <p>这是一个强大的数据比对和分析平台，帮助您更好地管理和分析数据。</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="mt-4">
      <el-col :span="8">
        <el-card class="stat-card">
          <template #header>
            <div class="card-header">
              <span>数据比对任务</span>
            </div>
          </template>
          <div class="card-content">
            <h2>{{ stats.compareTasks || 0 }}</h2>
            <p>已完成任务数</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="stat-card">
          <template #header>
            <div class="card-header">
              <span>数据模型</span>
            </div>
          </template>
          <div class="card-content">
            <h2>{{ stats.models || 0 }}</h2>
            <p>已创建模型数</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="stat-card">
          <template #header>
            <div class="card-header">
              <span>数据库连接</span>
            </div>
          </template>
          <div class="card-content">
            <h2>{{ stats.connections || 0 }}</h2>
            <p>已配置连接数</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getHomeStats } from '@/api/dashboard';

defineOptions({
  name: "HomePage"
});

const stats = ref({
  compareTasks: 0,
  models: 0,
  connections: 0
});

const loadStats = async () => {
  try {

  } catch (error) {
    console.error('加载首页统计数据失败:', error);
  }
};

onMounted(() => {
  loadStats();
});
</script>

<style scoped>
.home-container {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  text-align: center;
  padding: 20px 0;
}

.stat-card {
  height: 100%;
}

.stat-card .card-content h2 {
  font-size: 28px;
  margin: 10px 0;
  color: var(--el-color-primary);
}

.stat-card .card-content p {
  color: var(--el-text-color-secondary);
  margin: 0;
}

.mt-4 {
  margin-top: 20px;
}
</style>
