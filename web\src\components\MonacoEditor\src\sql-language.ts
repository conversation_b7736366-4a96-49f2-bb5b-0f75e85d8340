import type * as Monaco from 'monaco-editor';

/**
 * SQL语言配置
 * 该配置使用Monaco Editor API定义SQL的语法高亮、自动完成等功能
 */
export function registerSqlLanguage(monaco: typeof Monaco) {
  // 如果已经注册了SQL语言，则不再重复注册
  if (monaco.languages.getLanguages().some(lang => lang.id === 'sql')) {
    return;
  }

  // 注册SQL语言
  monaco.languages.register({ id: 'sql' });

  // SQL关键字
  const keywords = [
    'SELECT', 'FROM', 'WHERE', 'AS', 'RIGHT', 'LEFT', 'ON', 'INNER', 'JOIN', 'OUTER', 'FULL',
    'GROUP', 'ORDER', 'BY', 'HAVING', 'WITH', 'UPDATE', 'DELETE', 'INSERT', 'INTO', 'VALUES',
    'CREATE', 'ALTER', 'TABLE', 'DROP', 'INDEX', 'VIEW', 'PROCEDURE', 'FUNCTION', 'TRIGGER',
    'GRANT', 'REVOKE', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'TRANSACTION', 'BEGIN', 'END',
    'DECLARE', 'EXEC', 'SET', 'TRUNCATE', 'CASE', 'WHEN', 'THEN', 'ELSE', 'NULL', 'NOT', 'AND', 'OR',
    'EXISTS', 'IN', 'UNION', 'ALL', 'BETWEEN', 'LIKE', 'IS', 'ESCAPE', 'ASC', 'DESC', 'LIMIT',
    'OFFSET', 'TOP', 'PERCENT', 'CONSTRAINT', 'PRIMARY', 'FOREIGN', 'KEY', 'REFERENCES',
    'UNIQUE', 'CHECK', 'DEFAULT', 'CASCADE', 'RESTRICT', 'TEMPORARY', 'TEMP', 'IF',
    'DATABASE', 'SCHEMA', 'DISTINCT', 'USING', 'OVER', 'PARTITION', 'EXTRACT', 'CAST',
    'ADD', 'MODIFY', 'COLUMN', 'DROP'
  ];

  // SQL内置函数
  const builtinFunctions = [
    'AVG', 'COUNT', 'MAX', 'MIN', 'SUM', 'UCASE', 'LCASE', 'MID', 'LEN', 'ROUND',
    'NOW', 'FORMAT', 'DATEADD', 'DATEDIFF', 'TO_CHAR', 'TO_DATE', 'SUBSTR', 'INSTR',
    'NVL', 'DECODE', 'REPLACE', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'LENGTH',
    'GREATEST', 'LEAST', 'RANK', 'DENSE_RANK', 'ROW_NUMBER', 'PERCENT_RANK', 'LEAD', 'LAG',
    'GETDATE', 'CONVERT', 'CURRENT_TIMESTAMP', 'SYSDATE', 'SYSTIMESTAMP', 'COALESCE',
    'NULLIF', 'IFNULL', 'ISNULL', 'CONCAT', 'SUBSTRING', 'LEFT', 'RIGHT', 'CHARINDEX',
    'ABS', 'CEILING', 'FLOOR', 'POWER', 'SQRT', 'SQUARE', 'ACOS', 'ASIN', 'ATAN', 'COS',
    'SIN', 'TAN', 'LOG', 'LOG10', 'EXP', 'CHAR', 'ASCII', 'SIGN'
  ];

  // SQL数据类型
  const dataTypes = [
    'INT', 'INTEGER', 'SMALLINT', 'TINYINT', 'BIGINT', 'DECIMAL', 'NUMERIC', 'FLOAT',
    'REAL', 'DOUBLE', 'PRECISION', 'MONEY', 'SMALLMONEY', 'BIT', 'BOOLEAN', 'CHAR',
    'VARCHAR', 'NCHAR', 'NVARCHAR', 'TEXT', 'NTEXT', 'XML', 'DATE', 'TIME', 'DATETIME',
    'DATETIME2', 'DATETIMEOFFSET', 'TIMESTAMP', 'BINARY', 'VARBINARY', 'IMAGE', 'BLOB',
    'CLOB', 'CURSOR', 'TABLE', 'ROWID', 'UNIQUEIDENTIFIER', 'HIERARCHYID', 'GEOMETRY',
    'GEOGRAPHY', 'JSON', 'ARRAY', 'VARIANT'
  ];

  // 配置SQL语言
  monaco.languages.setMonarchTokensProvider('sql', {
    defaultToken: '',
    tokenPostfix: '.sql',
    ignoreCase: true,

    brackets: [
      { open: '[', close: ']', token: 'delimiter.square' },
      { open: '(', close: ')', token: 'delimiter.parenthesis' }
    ],

    keywords: keywords,
    operators: [
      '+', '-', '*', '/', '%', '&', '|', '^', '=', '>', '<', '>=', '<=', '<>', '!=', '!>', '!<',
      'AND', 'OR', 'NOT', 'LIKE', 'BETWEEN', 'IN', 'IS', 'EXISTS'
    ],
    builtinFunctions: builtinFunctions,
    dataTypes: dataTypes,

    escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,

    tokenizer: {
      root: [
        { include: '@whitespace' },
        { include: '@numbers' },
        { include: '@strings' },
        { include: '@comments' },

        [/[;,.]/, 'delimiter'],
        [/[()]/, '@brackets'],
        [/[\[\]]/, '@brackets'],

        [/@[a-zA-Z]\w*/, 'variable.name'],

        [
          /[a-zA-Z_]\w*/,
          {
            cases: {
              '@keywords': 'keyword',
              '@builtinFunctions': 'predefined',
              '@dataTypes': 'type',
              '@default': 'identifier'
            }
          }
        ],

        [/[<>=!%&+\-*/|~^]/, 'operator'],
      ],

      whitespace: [
        [/\s+/, 'white']
      ],

      comments: [
        [/--.*$/, 'comment'],
        [/\/\*/, 'comment', '@comment'],
      ],

      comment: [
        [/[^*/]+/, 'comment'],
        [/\/\*/, 'comment', '@push'],
        [/\*\//, 'comment', '@pop'],
        [/./, 'comment']
      ],

      numbers: [
        [/0[xX][0-9a-fA-F]*/, 'number'],
        [/[$][+-]*\d*(\.\d*)?/, 'number'],
        [/((\d+(\.\d*)?)|(\.\d+))([eE][\-+]?\d+)?/, 'number']
      ],

      strings: [
        [/'/, { token: 'string', next: '@string' }],
        [/"/, { token: 'string.double', next: '@stringDouble' }],
      ],

      string: [
        [/[^']+/, 'string'],
        [/''/, 'string'],
        [/'/, { token: 'string', next: '@pop' }],
      ],

      stringDouble: [
        [/[^"]+/, 'string.double'],
        [/""/, 'string.double'],
        [/"/, { token: 'string.double', next: '@pop' }],
      ],
    },
  });

  // 配置SQL代码补全
  monaco.languages.registerCompletionItemProvider('sql', {
    provideCompletionItems: (model, position, context, token) => {
      const wordInfo = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: wordInfo.startColumn,
        endColumn: wordInfo.endColumn
      };
      
      const suggestions = [
        ...keywords.map(keyword => ({
          label: keyword,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: keyword,
          range
        })),
        ...builtinFunctions.map(func => ({
          label: func,
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: func,
          range
        })),
        ...dataTypes.map(type => ({
          label: type,
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: type,
          range
        })),
      ];

      return { suggestions };
    }
  });
}

export const configureSqlLanguage = registerSqlLanguage;