export default {
    path: "/database",
    meta: {
      icon: "ri:database-2-line",
      title: "数据比对",
      rank: 4
    },
    children: [
      {
        path: "/database/model",
        name: "comparemodel",
        component: () => import("@/views/database/model/index.vue"),
        meta: {
          icon: "ri:settings-2-line",
          title: "比对模型",
          keepAlive: true
        }
      },

      {
        path: "/database/model/create",
        name: "comparemodelcreate",
        component: () => import("@/views/database/model/edit.vue"),
        meta: {
          title: "新增比对模型",
          hideInMenu: true,
          activePath: "/database/model"
        }
      },
      {
        path: "/database/model/edit/:id",
        name: "comparemodelupdate",
        component: () => import("@/views/database/model/edit.vue"),
        meta: {
          title: "编辑比对模型",
          hideInMenu: true
        }
      },
      {
        path: "/database/execute",
        name: "compareexecute",
        component: () => import("@/views/database/execute/index.vue"),
        meta: {
          icon: "ri:play-circle-line",
          title: "执行比对",
          keepAlive: true
        }
      },
      {
        path: "/database/result",
        name: "compareresult",
        component: () => import("@/views/database/result/index.vue"),
        meta: {
          icon: "ri:file-text-line",
          title: "比对结果",
          keepAlive: true
        }
      },
      {
        path: "/database/history",
        name: "comparehistory",
        component: () => import("@/views/database/history/index.vue"),
        meta: {
          icon: "ri:history-line",
          title: "历史记录",
          keepAlive: true
        }
      }
    ]
  };