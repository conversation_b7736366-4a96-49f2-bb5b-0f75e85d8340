/**
 * 路由调试工具 - 用于跟踪路由加载和匹配过程
 */

// 声明全局变量类型
declare global {
  interface Window {
    __ROUTE_DEBUG__: {
      originalRoutes: any | null;
      processedRoutes: any | null;
      componentPaths: string[] | null;
      routeNames?: string[];
      errors: Array<{
        timestamp: string;
        message: string;
        data: any;
      }>;
    };
    routeDebug: {
      showOriginalRoutes: () => void;
      showProcessedRoutes: () => void;
      showComponentPaths: () => void;
      showErrors: () => void;
      clearErrors: () => void;
    };
  }
}

// 创建一个全局对象存储路由调试信息
window.__ROUTE_DEBUG__ = {
  originalRoutes: null,
  processedRoutes: null,
  componentPaths: null,
  errors: []
};

/**
 * 记录路由处理日志
 * @param message 日志消息
 * @param data 相关数据
 */
export function logRouteDebug(message: string, data?: any) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`[路由调试 ${timestamp}] ${message}`, data);

  // 将错误记录到全局对象
  if (message.includes('警告') || message.includes('错误')) {
    window.__ROUTE_DEBUG__.errors.push({
      timestamp,
      message,
      data
    });
  }
}

/**
 * 记录原始路由数据
 * @param routes 原始路由数据
 */
export function recordOriginalRoutes(routes: any[]) {
  window.__ROUTE_DEBUG__.originalRoutes = routes;
  logRouteDebug('记录原始路由数据', routes);
}

/**
 * 记录处理后的路由数据
 * @param routes 处理后的路由数据
 */
export function recordProcessedRoutes(routes: any[]) {
  window.__ROUTE_DEBUG__.processedRoutes = routes;
  logRouteDebug('记录处理后的路由数据', routes);
}

/**
 * 记录可用的组件路径
 * @param paths 组件路径列表
 */
export function recordComponentPaths(paths: string[]) {
  window.__ROUTE_DEBUG__.componentPaths = paths;
  logRouteDebug('记录可用组件路径', paths);
}

/**
 * 检查路由配置中的常见问题
 * @param routes 路由配置
 */
export function checkRouteIssues(routes: any[]) {
  // 对每个路由执行检查
  const findIssues = (route: any, parentPath = '') => {
    const path = route.path;
    const fullPath = parentPath ? `${parentPath}${path.startsWith('/') ? path : `/${path}`}` : path;
    
    // 检查组件是否存在
    if (!route.component && !route.redirect && !route.children?.length) {
      logRouteDebug('错误: 路由没有组件也没有重定向或子路由', { route, fullPath });
    }
    
    // 检查名称冲突
    if (route.name) {
      if (window.__ROUTE_DEBUG__.routeNames?.includes(route.name)) {
        logRouteDebug('错误: 路由名称重复', { name: route.name, path: fullPath });
      } else {
        window.__ROUTE_DEBUG__.routeNames = [
          ...(window.__ROUTE_DEBUG__.routeNames || []),
          route.name
        ];
      }
    }
    
    // 递归检查子路由
    if (route.children?.length) {
      route.children.forEach((child: any) => findIssues(child, fullPath));
    }
  };
  
  // 重置已检查的路由名称
  window.__ROUTE_DEBUG__.routeNames = [];
  
  // 执行检查
  routes.forEach(route => findIssues(route));
}

/**
 * 在开发环境下自动挂载调试工具
 */
export function setupRouteDebugger() {
  if (process.env.NODE_ENV === 'development') {
    // 添加调试按钮到页面
    const debugButton = document.createElement('button');
    debugButton.innerText = '路由调试';
    debugButton.style.position = 'fixed';
    debugButton.style.bottom = '20px';
    debugButton.style.right = '20px';
    debugButton.style.zIndex = '9999';
    debugButton.style.padding = '8px 12px';
    debugButton.style.backgroundColor = '#42b983';
    debugButton.style.color = 'white';
    debugButton.style.border = 'none';
    debugButton.style.borderRadius = '4px';
    debugButton.style.cursor = 'pointer';
    
    debugButton.addEventListener('click', () => {
      console.log('%c路由调试信息', 'font-weight: bold; font-size: 16px; color: #42b983;');
      console.log('原始路由:', window.__ROUTE_DEBUG__.originalRoutes);
      console.log('处理后路由:', window.__ROUTE_DEBUG__.processedRoutes);
      console.log('可用组件路径:', window.__ROUTE_DEBUG__.componentPaths);
      console.log('错误和警告:', window.__ROUTE_DEBUG__.errors);
    });
    
    // 在DOM加载完成后添加按钮
    window.addEventListener('DOMContentLoaded', () => {
      document.body.appendChild(debugButton);
    });
    
    logRouteDebug('路由调试工具已初始化');
  }
}

// 为开发者控制台添加调试命令
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.routeDebug = {
    showOriginalRoutes: () => console.log(window.__ROUTE_DEBUG__.originalRoutes),
    showProcessedRoutes: () => console.log(window.__ROUTE_DEBUG__.processedRoutes),
    showComponentPaths: () => console.log(window.__ROUTE_DEBUG__.componentPaths),
    showErrors: () => console.log(window.__ROUTE_DEBUG__.errors),
    clearErrors: () => {
      window.__ROUTE_DEBUG__.errors = [];
      console.log('错误记录已清除');
    }
  };
} 