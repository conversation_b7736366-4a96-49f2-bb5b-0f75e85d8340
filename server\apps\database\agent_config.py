"""
Agent配置 - 极简实现
通过配置开关控制Agent标准架构的启用
"""
from django.conf import settings

# Agent标准架构配置
AGENT_CONFIG = {
    # 是否启用Agent标准架构
    'USE_AGENT_STANDARD': getattr(settings, 'USE_AGENT_STANDARD', False),
    
    # Agent服务地址
    'AGENT_BASE_URL': getattr(settings, 'AGENT_BASE_URL', 'http://localhost:8001'),
    
    # Agent请求超时时间
    'AGENT_TIMEOUT': getattr(settings, 'AGENT_TIMEOUT', 30),
    
    # 是否在Agent失败时回退到原有方式
    'AGENT_FALLBACK_ENABLED': getattr(settings, 'AGENT_FALLBACK_ENABLED', True),
    
    # Agent状态同步间隔（秒）
    'AGENT_SYNC_INTERVAL': getattr(settings, 'AGENT_SYNC_INTERVAL', 10),
}

def is_agent_enabled():
    """检查是否启用Agent标准架构"""
    return AGENT_CONFIG['USE_AGENT_STANDARD']

def get_agent_url():
    """获取Agent服务地址"""
    return AGENT_CONFIG['AGENT_BASE_URL']

def get_agent_timeout():
    """获取Agent请求超时时间"""
    return AGENT_CONFIG['AGENT_TIMEOUT']

def is_fallback_enabled():
    """检查是否启用回退机制"""
    return AGENT_CONFIG['AGENT_FALLBACK_ENABLED']
