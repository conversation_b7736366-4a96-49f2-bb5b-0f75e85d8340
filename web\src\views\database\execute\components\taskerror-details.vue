<!--
任务错误详情组件
用于展示数据比对任务执行过程中的错误信息，包括错误类型、错误消息、错误上下文和堆栈跟踪等
-->
<template>
  <div class="task-error-details">
    <div v-if="error" class="error-container">
      <div class="error-header">
        <el-alert
          :title="error.message || '任务执行失败'"
          type="error"
          :description="getErrorDescription()"
          show-icon
          :closable="false"
          class="mb-3"
        />
        
        <div class="error-meta">
          <div v-if="error.type" class="meta-item">
            <span class="label">错误类型:</span>
            <span class="value">{{ error.type }}</span>
          </div>
          <div v-if="error.reported_at" class="meta-item">
            <span class="label">发生时间:</span>
            <span class="value">{{ formatTime(error.reported_at) }}</span>
          </div>
        </div>
      </div>
      
      <div v-if="hasDetails" class="error-details mt-3">
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <div v-if="error.context" class="context mb-3">
              <h4>错误上下文</h4>
              <pre class="context-content">{{ formatContext(error.context) }}</pre>
            </div>
            
            <div v-if="error.traceback" class="traceback">
              <h4>错误堆栈</h4>
              <pre class="traceback-content">{{ error.traceback }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <div class="error-actions mt-3">
        <el-button 
          type="primary" 
          @click="$emit('retry')"
          :loading="loading"
        >
          重试任务
        </el-button>
        <el-button 
          type="info" 
          @click="copyError"
          :loading="copying"
        >
          复制错误信息
        </el-button>
        <el-button 
          @click="$emit('close')"
        >
          关闭
        </el-button>
      </div>
    </div>
    
    <div v-else class="no-error">
      <el-empty description="没有错误信息" />
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'TaskErrorDetails',
  
  props: {
    error: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['retry', 'close'],
  
  setup(props) {
    const copying = ref(false)
    
    const hasDetails = computed(() => {
      return !!(props.error?.context || props.error?.traceback)
    })
    
    // 格式化错误上下文
    const formatContext = (context) => {
      if (!context) return ''
      try {
        if (typeof context === 'string') {
          return context
        }
        return JSON.stringify(context, null, 2)
      } catch (e) {
        return String(context)
      }
    }
    
    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return timeStr
      }
    }
    
    // 获取错误描述
    const getErrorDescription = () => {
      if (!props.error) return ''
      
      // 针对常见错误类型提供更友好的描述
      if (props.error.type === 'ConnectionError' || props.error.message?.includes('无法连接')) {
        return '数据库连接失败，请检查连接配置是否正确、数据库服务是否可用。'
      }
      
      if (props.error.type === 'ValueError' && props.error.message?.includes('表不存在')) {
        return '指定的数据表不存在，请检查表名是否正确。'
      }
      
      if (props.error.type === 'PermissionError') {
        return '权限不足，数据库用户可能没有足够的权限执行操作。'
      }
      
      // 返回默认描述
      return '执行比对任务时发生错误，请查看详细信息或联系管理员'
    }
    
    // 复制错误信息
    const copyError = async () => {
      if (!props.error) return
      
      try {
        copying.value = true
        
        const errorText = `
错误类型: ${props.error.type || '未知'}
错误信息: ${props.error.message || '未知错误'}
发生时间: ${formatTime(props.error.reported_at)}
${props.error.context ? `上下文: ${formatContext(props.error.context)}` : ''}
${props.error.traceback ? `堆栈: ${props.error.traceback}` : ''}
        `.trim()
        
        await navigator.clipboard.writeText(errorText)
        ElMessage.success('错误信息已复制到剪贴板')
      } catch (e) {
        ElMessage.error('复制失败: ' + e.message)
      } finally {
        copying.value = false
      }
    }
    
    return {
      hasDetails,
      formatContext,
      formatTime,
      getErrorDescription,
      copyError,
      copying
    }
  }
}
</script>

<style lang="scss" scoped>
.task-error-details {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .error-container {
    .error-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      
      .meta-item {
        font-size: 14px;
        
        .label {
          color: #909399;
          margin-right: 4px;
        }
        
        .value {
          color: #606266;
          font-weight: 500;
        }
      }
    }
    
    .error-details {
      .context, .traceback {
        h4 {
          font-size: 14px;
          margin-bottom: 8px;
          color: #606266;
        }
        
        pre {
          background-color: #f5f7fa;
          border-radius: 4px;
          padding: 12px;
          white-space: pre-wrap;
          overflow-x: auto;
          font-family: monospace;
          font-size: 13px;
          line-height: 1.5;
        }
        
        .context-content {
          max-height: 200px;
          overflow-y: auto;
        }
        
        .traceback-content {
          max-height: 400px;
          overflow-y: auto;
          color: #F56C6C;
        }
      }
    }
  }
  
  .mb-3 {
    margin-bottom: 16px;
  }
  
  .mt-3 {
    margin-top: 16px;
  }
}
</style> 