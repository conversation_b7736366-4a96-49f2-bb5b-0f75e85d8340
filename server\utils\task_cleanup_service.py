"""
任务清理服务模块

提供任务清理功能，包括清理不活跃的任务、过期的任务状态同步等。
"""
import logging
import threading
import time
from typing import Dict, Set
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

class TaskCleanupService:
    """
    任务清理服务类

    负责清理不活跃的任务、过期的任务状态同步等。
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(TaskCleanupService, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化任务清理服务"""
        if self._initialized:
            return

        # 清理配置
        self.cleanup_interval = 300  # 5分钟清理一次
        self.inactive_threshold = 1800  # 30分钟无活动视为不活跃
        self.completed_task_retention = 7200  # 已完成任务保留2小时（减少启动时的清理）

        # 运行状态
        self.running = False
        self.cleanup_thread = None

        # 统计信息
        self.cleanup_count = 0
        self.last_cleanup_time = None

        # 初始化完成标志
        self._initialized = True

    def start(self):
        """启动清理服务"""
        with self._lock:
            if self.running:
                logger.warning("任务清理服务已在运行")
                return

            self.running = True
            self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            self.cleanup_thread.start()
            logger.info("任务清理服务已启动")

    def stop(self):
        """停止清理服务"""
        with self._lock:
            if not self.running:
                return

            self.running = False
            if self.cleanup_thread and self.cleanup_thread.is_alive():
                self.cleanup_thread.join(timeout=5.0)
            logger.info("任务清理服务已停止")

    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                # 执行清理
                self._perform_cleanup()

                # 等待下次清理
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"任务清理循环异常: {str(e)}")
                time.sleep(60)  # 发生异常时等待1分钟

    def _perform_cleanup(self):
        """执行清理操作"""
        try:
            logger.debug("开始执行任务清理")

            # 清理不活跃的任务同步
            inactive_tasks = self._cleanup_inactive_task_sync()

            # 清理已完成的任务
            completed_tasks = self._cleanup_completed_tasks()

            # 清理孤立的消息队列
            orphaned_queues = self._cleanup_orphaned_queues()

            # 更新统计信息
            self.cleanup_count += 1
            self.last_cleanup_time = timezone.now()

            logger.info(f"任务清理完成: 不活跃任务={len(inactive_tasks)}, "
                       f"已完成任务={len(completed_tasks)}, 孤立队列={len(orphaned_queues)}")

        except Exception as e:
            logger.error(f"执行任务清理时发生错误: {str(e)}")

    def _cleanup_inactive_task_sync(self) -> Set[str]:
        """清理不活跃的任务同步"""
        try:
            from .websocket_manager import websocket_manager
            from .message_queue import message_queue

            inactive_tasks = set()
            now = timezone.now()
            threshold = now - timedelta(seconds=self.inactive_threshold)

            # 获取所有正在同步的任务
            with message_queue.lock:
                sync_tasks = list(message_queue.last_sync_time.keys())

            for task_id in sync_tasks:
                try:
                    # 跳过系统保留的队列ID
                    if task_id == "global":
                        # global是系统全局通知队列，不应该被清理
                        logger.debug(f"跳过系统保留队列: {task_id}")
                        continue

                    # 检查是否有活跃连接
                    connections = websocket_manager.get_task_connections(task_id)
                    has_active_connections = False

                    for conn in connections:
                        last_activity = conn.get('last_activity')
                        if last_activity:
                            activity_time = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                            if activity_time.replace(tzinfo=None) > threshold.replace(tzinfo=None):
                                has_active_connections = True
                                break

                    # 如果没有活跃连接，清理任务
                    if not has_active_connections:
                        logger.info(f"清理不活跃任务同步: {task_id}")
                        message_queue.cleanup_task(task_id)
                        inactive_tasks.add(task_id)

                except Exception as e:
                    logger.error(f"清理任务 {task_id} 时发生错误: {str(e)}")

            return inactive_tasks

        except Exception as e:
            logger.error(f"清理不活跃任务同步时发生错误: {str(e)}")
            return set()

    def _cleanup_completed_tasks(self) -> Set[str]:
        """清理已完成的任务"""
        try:
            from apps.database.models import CompareTask
            from .message_queue import message_queue

            completed_tasks = set()
            now = timezone.now()
            threshold = now - timedelta(seconds=self.completed_task_retention)

            # 查找已完成且超过保留时间的任务
            with transaction.atomic():
                tasks = CompareTask.objects.filter(
                    status__in=['completed', 'success', 'failed', 'error', 'stopped', 'canceled'],
                    end_time__lt=threshold
                ).values_list('id', 'external_id')

                for task_id, external_id in tasks:
                    try:
                        # 清理消息队列
                        message_queue.cleanup_task(str(task_id))
                        if external_id:
                            message_queue.cleanup_task(external_id)

                        completed_tasks.add(str(task_id))
                        logger.debug(f"清理已完成任务: {task_id}")

                    except Exception as e:
                        logger.error(f"清理已完成任务 {task_id} 时发生错误: {str(e)}")

            return completed_tasks

        except Exception as e:
            logger.error(f"清理已完成任务时发生错误: {str(e)}")
            return set()

    def _cleanup_orphaned_queues(self) -> Set[str]:
        """清理孤立的消息队列"""
        try:
            from apps.database.models import CompareTask
            from .message_queue import message_queue

            orphaned_queues = set()

            # 获取所有队列中的任务ID
            with message_queue.lock:
                queue_task_ids = list(message_queue.queues.keys())
                sync_task_ids = list(message_queue.last_sync_time.keys())
                all_task_ids = set(queue_task_ids + sync_task_ids)

            # 检查每个任务是否仍然存在
            for task_id in all_task_ids:
                try:
                    # 跳过系统保留的队列ID
                    if task_id == "global":
                        # global是系统全局通知队列，不应该被清理
                        logger.debug(f"跳过系统保留队列: {task_id}")
                        continue

                    # 检查任务是否存在
                    exists = False
                    try:
                        # 先尝试通过ID查找
                        CompareTask.objects.get(id=task_id)
                        exists = True
                    except (CompareTask.DoesNotExist, ValueError):
                        # 再尝试通过external_id查找
                        exists = CompareTask.objects.filter(external_id=task_id).exists()

                    if not exists:
                        logger.info(f"清理孤立队列: {task_id}")
                        message_queue.cleanup_task(task_id)
                        orphaned_queues.add(task_id)

                except Exception as e:
                    logger.error(f"检查任务 {task_id} 时发生错误: {str(e)}")

            return orphaned_queues

        except Exception as e:
            logger.error(f"清理孤立队列时发生错误: {str(e)}")
            return set()

    def get_stats(self) -> Dict:
        """获取清理服务统计信息"""
        return {
            'running': self.running,
            'cleanup_count': self.cleanup_count,
            'last_cleanup_time': self.last_cleanup_time.isoformat() if self.last_cleanup_time else None,
            'cleanup_interval': self.cleanup_interval,
            'inactive_threshold': self.inactive_threshold,
            'completed_task_retention': self.completed_task_retention
        }

# 创建全局实例
try:
    task_cleanup_service = TaskCleanupService()
except Exception as e:
    logger.error(f"初始化任务清理服务失败: {str(e)}")

    # 创建一个简单的替代实例
    class SimpleTaskCleanupService:
        def start(self):
            logger.debug("启动任务清理服务（简化版）")

        def stop(self):
            logger.debug("停止任务清理服务（简化版）")

        def get_stats(self):
            return {"running": False, "cleanup_count": 0}

    task_cleanup_service = SimpleTaskCleanupService()
