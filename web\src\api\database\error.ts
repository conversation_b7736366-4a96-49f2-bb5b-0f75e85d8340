/**
 * 数据库API错误处理模块 - 简化版本
 * 专注于核心错误处理功能
 */

import { ElMessage } from 'element-plus';
import { ErrorCode, ApiError } from './types';

// 为了向后兼容，导出ErrorCode作为DatabaseErrorCode
export { ErrorCode as DatabaseErrorCode };

// ===== 错误类定义 =====

/**
 * 数据库API错误类
 */
export class DatabaseApiError extends Error {
  public readonly code: ErrorCode;
  public readonly endpoint?: string;
  public readonly details?: any;
  public readonly timestamp: string;

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.INTERNAL_ERROR,
    endpoint?: string,
    details?: any
  ) {
    super(message);
    this.name = 'DatabaseApiError';
    this.code = code;
    this.endpoint = endpoint;
    this.details = details;
    this.timestamp = new Date().toISOString();

    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, DatabaseApiError);
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON(): ApiError {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      endpoint: this.endpoint
    };
  }
}

// ===== 错误处理函数 =====

/**
 * 处理数据库API错误
 */
export function handleDatabaseError(error: any): DatabaseApiError {
  if (error instanceof DatabaseApiError) {
    return error;
  }

  // 处理HTTP错误
  if (error.response) {
    const { status, data } = error.response;
    const message = data?.message || error.message || '服务器错误';

    let code: ErrorCode;
    switch (status) {
      case 400:
        code = ErrorCode.QUERY_SYNTAX_ERROR;
        break;
      case 401:
        code = ErrorCode.CONNECTION_FAILED;
        break;
      case 404:
        code = ErrorCode.TABLE_NOT_FOUND;
        break;
      case 408:
        code = ErrorCode.CONNECTION_FAILED;
        break;
      case 500:
        code = ErrorCode.INTERNAL_ERROR;
        break;
      default:
        code = ErrorCode.NETWORK_ERROR;
    }

    return new DatabaseApiError(message, code, error.config?.url);
  }

  // 处理网络错误
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return new DatabaseApiError('网络连接失败', ErrorCode.NETWORK_ERROR);
  }

  // 默认错误
  return new DatabaseApiError(
    error.message || '未知错误',
    ErrorCode.INTERNAL_ERROR
  );
}

/**
 * 获取用户友好的错误消息
 */
export function getErrorMessage(error: DatabaseApiError): string {
  const errorMessages: Record<ErrorCode, string> = {
    [ErrorCode.CONNECTION_FAILED]: '数据库连接失败，请检查连接配置',
    [ErrorCode.QUERY_SYNTAX_ERROR]: 'SQL语法错误，请检查查询语句',
    [ErrorCode.TABLE_NOT_FOUND]: '表不存在，请检查表名',
    [ErrorCode.TASK_NOT_FOUND]: '任务不存在，请检查任务ID',
    [ErrorCode.COMPARISON_FAILED]: '数据比较失败，请检查比较配置',
    [ErrorCode.INTERNAL_ERROR]: '系统内部错误，请稍后重试',
    [ErrorCode.NETWORK_ERROR]: '网络连接错误，请检查网络设置',
    [ErrorCode.DATA_VALIDATION_ERROR]: '数据验证错误，请检查输入数据',
    [ErrorCode.VALIDATION_ERROR]: '验证错误，请检查输入参数',
    [ErrorCode.UNAUTHORIZED]: '未授权访问，请检查登录状态',
    [ErrorCode.FORBIDDEN]: '访问被禁止，权限不足',
    [ErrorCode.NOT_FOUND]: '资源不存在',
    [ErrorCode.TIMEOUT]: '请求超时，请稍后重试',
    [ErrorCode.RATE_LIMIT]: '请求频率过高，请稍后重试',
    [ErrorCode.BAD_GATEWAY]: '网关错误，请稍后重试',
    [ErrorCode.SERVICE_UNAVAILABLE]: '服务不可用，请稍后重试',
    [ErrorCode.GATEWAY_TIMEOUT]: '网关超时，请稍后重试',
    [ErrorCode.UNKNOWN_ERROR]: '未知错误，请联系管理员'
  };

  return errorMessages[error.code] || error.message || '未知错误';
}

/**
 * 显示错误消息
 */
export function showErrorMessage(error: DatabaseApiError): void {
  const message = getErrorMessage(error);
  ElMessage.error(message);
}

/**
 * 创建数据库API错误的便捷函数
 * @param code 错误代码
 * @param message 错误消息
 * @param endpoint 端点（可选）
 * @param details 详细信息（可选）
 */
export function createDatabaseError(
  code: ErrorCode,
  message: string,
  endpoint?: string,
  details?: any
): DatabaseApiError {
  return new DatabaseApiError(message, code, endpoint, details);
}
