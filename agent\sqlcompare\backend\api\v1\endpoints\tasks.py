#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理API端点 - 使用主项目模型和服务
"""

import os
import sys
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用主项目的模型和服务
from models.pydantic_models import TaskCreateFromModel, TaskCreateDirect, TaskResponse
from backend.models.base import APIResponse, PaginationParams
from backend.core.dependencies import (
    get_task_service, get_comparison_service, get_current_user_id
)

router = APIRouter()


@router.post("/from-model", response_model=APIResponse)
async def create_task_from_model(
    task_data: TaskCreateFromModel,
    user_id: str = Depends(get_current_user_id),
    task_service: TaskService = Depends(get_task_service)
):
    """
    基于模型创建比对任务

    - **model_id**: 比对模型ID
    - **task_name**: 任务名称（可选）
    - **description**: 任务描述（可选）
    - **selected_table_ids**: 选择执行的表ID列表（可选）
    """
    try:
        task_id = await task_service.create_task_from_model(user_id, task_data)
        return APIResponse.success_response(
            data={"task_id": task_id},
            message="基于模型创建任务成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/direct", response_model=APIResponse)
async def create_task_direct(
    task_data: TaskCreateDirect,
    user_id: str = Depends(get_current_user_id),
    task_service: TaskService = Depends(get_task_service)
):
    """
    直接创建比对任务

    - **task_name**: 任务名称
    - **description**: 任务描述
    - **comparison_type**: 比对类型
    - **source_connection**: 源数据库连接配置
    - **target_connection**: 目标数据库连接配置
    - **sql_rules**: SQL比对规则列表
    """
    try:
        task_id = await task_service.create_task_direct(user_id, task_data)
        return APIResponse.success_response(
            data={"task_id": task_id},
            message="直接创建任务成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=APIResponse)
async def get_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    pagination: PaginationParams = Depends(),
    user_id: str = Depends(get_current_user_id),
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务列表

    - **status**: 可选的状态过滤
    - **page**: 页码
    - **size**: 每页大小
    """
    try:
        tasks, total = await task_service.get_tasks(
            user_id=user_id,
            status=status,
            offset=pagination.offset,
            limit=pagination.size
        )

        return APIResponse.success_response(
            data={
                "items": tasks,
                "total": total,
                "page": pagination.page,
                "size": pagination.size
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}", response_model=APIResponse)
async def get_task(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务详情

    - **task_id**: 任务ID
    """
    try:
        task = await task_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        return APIResponse.success_response(data=task)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/start", response_model=APIResponse)
async def start_task(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    启动比对任务

    - **task_id**: 任务ID
    """
    try:
        success = await comparison_service.start_comparison_task(task_id)
        if success:
            return APIResponse.success_response(message="任务启动成功")
        else:
            raise HTTPException(status_code=400, detail="任务启动失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/cancel", response_model=APIResponse)
async def cancel_task(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    取消任务

    - **task_id**: 任务ID
    """
    try:
        success = await task_service.cancel_task(task_id)
        if success:
            return APIResponse.success_response(message="任务取消成功")
        else:
            raise HTTPException(status_code=400, detail="任务取消失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/progress", response_model=APIResponse)
async def get_task_progress(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务进度

    - **task_id**: 任务ID
    """
    try:
        progress = await task_service.get_task_progress(task_id)
        if not progress:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        return APIResponse.success_response(data=progress)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}", response_model=APIResponse)
async def delete_task(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """
    删除任务

    - **task_id**: 任务ID
    """
    try:
        success = await task_service.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        return APIResponse.success_response(message="任务删除成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=APIResponse)
async def get_task_statistics(
    user_id: str = Depends(get_current_user_id),
    task_service: TaskService = Depends(get_task_service)
):
    """
    获取任务统计信息
    """
    try:
        statistics = task_service.get_task_statistics(user_id)
        return APIResponse.success_response(data=statistics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/results", response_model=APIResponse)
async def get_task_results(
    task_id: str,
    limit: int = Query(100, description="返回结果数量限制"),
    offset: int = Query(0, description="结果偏移量"),
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务比对结果

    - **task_id**: 任务ID
    - **limit**: 返回结果数量限制
    - **offset**: 结果偏移量
    """
    try:
        results = comparison_service.get_comparison_results(task_id, limit, offset)
        return APIResponse.success_response(data={
            "results": results,
            "limit": limit,
            "offset": offset
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
