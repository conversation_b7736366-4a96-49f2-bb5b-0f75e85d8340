# SQLCompare Backend API

数据库比对任务管理API服务，提供完整的RESTful API接口，支持任务管理、配置管理、结果查询和实时监控。

## 功能特性

- 🚀 **任务管理**: 创建、启动、停止、查询比对任务
- ⚙️ **配置管理**: 数据库连接配置、比对规则配置的CRUD操作
- 📊 **进度监控**: 实时任务进度、状态更新、WebSocket推送
- 📈 **结果查询**: 比对结果的查询、导出、统计分析
- 🔐 **安全认证**: JWT认证、API密钥、权限管理
- 📡 **实时监控**: 系统健康检查、性能指标、告警管理

## 技术栈

- **框架**: FastAPI 0.104.1
- **数据库**: SQLite (集成现有TaskManager)
- **认证**: JWT + API Key
- **实时通信**: WebSocket
- **监控**: psutil系统监控
- **文档**: 自动生成OpenAPI文档

## 快速开始

### 1. 安装依赖

```bash
cd sqlcompare/backend
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# 环境配置
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=true

# 数据库配置
DATABASE_URL=sqlite:///./sqlcompare_api.db
SQLITE_REPORTER_DB_PATH=./comparison_tasks.db

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-key-change-in-production

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/api.log
```

### 3. 启动服务

```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/health

## API端点概览

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/refresh` - 刷新令牌

### 任务管理
- `POST /api/v1/tasks` - 创建比对任务
- `GET /api/v1/tasks` - 获取任务列表
- `GET /api/v1/tasks/{task_id}` - 获取任务详情
- `PUT /api/v1/tasks/{task_id}` - 更新任务
- `POST /api/v1/tasks/{task_id}/actions` - 执行任务操作
- `GET /api/v1/tasks/{task_id}/progress` - 获取任务进度
- `DELETE /api/v1/tasks/{task_id}` - 删除任务

### 配置管理
- `POST /api/v1/configs/connections` - 创建数据库连接
- `GET /api/v1/configs/connections` - 获取连接列表
- `POST /api/v1/configs/connections/test` - 测试数据库连接
- `POST /api/v1/configs/rules` - 创建比对规则
- `GET /api/v1/configs/rules` - 获取规则列表

### 结果查询
- `GET /api/v1/results/{task_id}` - 获取比对结果
- `GET /api/v1/results/{task_id}/summary` - 获取比对摘要
- `GET /api/v1/results/{task_id}/statistics` - 获取统计信息
- `POST /api/v1/results/{task_id}/export` - 导出结果

### 监控
- `GET /api/v1/monitoring/health` - 健康检查
- `GET /api/v1/monitoring/metrics` - 系统指标
- `GET /api/v1/monitoring/tasks/active` - 活跃任务
- `WS /api/v1/monitoring/ws` - WebSocket监控

## 使用示例

### 1. 用户认证

```python
import requests

# 登录
login_data = {
    "username": "admin",
    "password": "admin123"
}
response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data)
token = response.json()["access_token"]

# 设置认证头
headers = {"Authorization": f"Bearer {token}"}
```

### 2. 创建比对任务

```python
task_data = {
    "name": "用户表比对",
    "description": "比对生产和测试环境的用户表",
    "priority": 5,
    "source_connection": {
        "db_type": "db2",
        "host": "prod-db.company.com",
        "port": 50000,
        "username": "dbuser",
        "password": "password",
        "database": "PRODDB"
    },
    "target_connection": {
        "db_type": "mysql",
        "host": "test-mysql.company.com",
        "port": 3306,
        "username": "testuser",
        "password": "password",
        "database": "testdb"
    },
    "comparison_rules": [
        {
            "table_id": "users",
            "sql_1": "SELECT * FROM USER_TABLE",
            "sql_2": "SELECT * FROM USER_TABLE",
            "primary_keys": ["USER_ID"]
        }
    ]
}

response = requests.post(
    "http://localhost:8000/api/v1/tasks",
    json=task_data,
    headers=headers
)
task_id = response.json()["data"]["task_id"]
```

### 3. 启动任务

```python
action_data = {
    "action": "start",
    "reason": "开始执行比对任务"
}

response = requests.post(
    f"http://localhost:8000/api/v1/tasks/{task_id}/actions",
    json=action_data,
    headers=headers
)
```

### 4. 监控任务进度

```python
# 获取任务进度
response = requests.get(
    f"http://localhost:8000/api/v1/tasks/{task_id}/progress",
    headers=headers
)
progress = response.json()
print(f"进度: {progress['progress_percentage']}%")

# 使用WebSocket实时监控
import websocket
import json

def on_message(ws, message):
    data = json.loads(message)
    if data["type"] == "task_update":
        print(f"任务更新: {data['data']}")

ws = websocket.WebSocketApp(
    f"ws://localhost:8000/api/v1/monitoring/ws/tasks/{task_id}",
    on_message=on_message
)
ws.run_forever()
```

### 5. 查询比对结果

```python
# 获取比对结果
response = requests.get(
    f"http://localhost:8000/api/v1/results/{task_id}",
    headers=headers,
    params={"page": 1, "size": 20}
)
results = response.json()

# 获取比对摘要
response = requests.get(
    f"http://localhost:8000/api/v1/results/{task_id}/summary",
    headers=headers
)
summary = response.json()
print(f"总差异数: {summary['total_differences']}")
```

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| ENVIRONMENT | 运行环境 | development |
| HOST | 服务器主机 | 0.0.0.0 |
| PORT | 服务器端口 | 8000 |
| DEBUG | 调试模式 | false |
| SECRET_KEY | 应用密钥 | - |
| JWT_SECRET_KEY | JWT密钥 | - |
| DATABASE_URL | 数据库URL | sqlite:///./sqlcompare_api.db |
| LOG_LEVEL | 日志级别 | INFO |

### 性能配置

| 配置项 | 描述 | 默认值 |
|--------|------|--------|
| MAX_CONCURRENT_TASKS | 最大并发任务数 | 5 |
| TASK_TIMEOUT_SECONDS | 任务超时时间 | 3600 |
| RATE_LIMIT_REQUESTS | API限流请求数 | 100 |
| RATE_LIMIT_WINDOW | 限流时间窗口 | 60 |

## 部署指南

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 生产环境配置

```bash
# 设置生产环境变量
export ENVIRONMENT=production
export DEBUG=false
export SECRET_KEY=your-production-secret-key
export JWT_SECRET_KEY=your-production-jwt-key

# 启动服务
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 开发指南

### 项目结构

```
backend/
├── main.py                 # 应用入口
├── core/                   # 核心模块
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   ├── exceptions.py      # 异常定义
│   └── logging_config.py  # 日志配置
├── api/                    # API路由
│   └── v1/
│       ├── router.py      # 路由汇总
│       └── endpoints/     # API端点
├── models/                 # 数据模型
├── services/              # 业务服务层
└── requirements.txt       # 依赖列表
```

### 添加新的API端点

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `api/v1/endpoints/` 中创建API端点
4. 在 `api/v1/router.py` 中注册路由

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库路径和权限
   - 确认SQLite文件可读写

2. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认WebSocket支持

3. **认证失败**
   - 检查JWT密钥配置
   - 确认令牌未过期

### 日志查看

```bash
# 查看应用日志
tail -f logs/api.log

# 查看系统日志
curl http://localhost:8000/api/v1/monitoring/logs
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
