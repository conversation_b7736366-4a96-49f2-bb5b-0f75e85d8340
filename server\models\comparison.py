"""
比对结果相关的Django模型
实现数据库持久化存储，支持Agent到Server的数据同步
"""

from django.db import models
from django.contrib.auth.models import User
import json
import uuid
from datetime import datetime


class ComparisonTask(models.Model):
    """比对任务模型"""
    
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('canceled', '已取消'),
    ]
    
    # 基础字段
    task_id = models.CharField(max_length=64, unique=True, verbose_name='任务ID')
    name = models.CharField(max_length=200, blank=True, verbose_name='任务名称')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    
    # 配置信息
    source_config = models.JSONField(verbose_name='源数据库配置')
    target_config = models.JSONField(verbose_name='目标数据库配置')
    compare_config = models.JSONField(verbose_name='比对配置')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    started_at = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    
    # 进度和错误信息
    progress = models.IntegerField(default=0, verbose_name='进度百分比')
    error_message = models.TextField(blank=True, verbose_name='错误信息')
    
    # 用户关联
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='创建者')
    
    class Meta:
        db_table = 'comparison_task'
        verbose_name = '比对任务'
        verbose_name_plural = '比对任务'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.task_id} - {self.get_status_display()}"
    
    def save(self, *args, **kwargs):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
        super().save(*args, **kwargs)


class ComparisonSummary(models.Model):
    """比对结果摘要"""
    
    task = models.OneToOneField(ComparisonTask, on_delete=models.CASCADE, verbose_name='关联任务')
    
    # 统计数据
    total_records = models.IntegerField(default=0, verbose_name='总记录数')
    matched_records = models.IntegerField(default=0, verbose_name='匹配记录数')
    diff_records = models.IntegerField(default=0, verbose_name='差异记录数')
    source_only_records = models.IntegerField(default=0, verbose_name='源独有记录数')
    target_only_records = models.IntegerField(default=0, verbose_name='目标独有记录数')
    
    # 执行信息
    execution_time = models.FloatField(default=0, verbose_name='执行时间(秒)')
    algorithm_used = models.CharField(max_length=50, blank=True, verbose_name='使用的算法')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'comparison_summary'
        verbose_name = '比对摘要'
        verbose_name_plural = '比对摘要'
    
    def __str__(self):
        return f"Summary for {self.task.task_id}"
    
    @property
    def success_rate(self):
        """计算成功率"""
        if self.total_records == 0:
            return 0
        return (self.matched_records / self.total_records) * 100


class ComparisonDifference(models.Model):
    """差异记录"""
    
    DIFF_TYPE_CHOICES = [
        ('value_diff', '值差异'),
        ('type_diff', '类型差异'),
        ('format_diff', '格式差异'),
    ]
    
    task = models.ForeignKey(ComparisonTask, on_delete=models.CASCADE, verbose_name='关联任务')
    
    # 记录标识
    record_id = models.CharField(max_length=255, verbose_name='记录ID')
    
    # 数据内容
    source_data = models.JSONField(verbose_name='源数据')
    target_data = models.JSONField(verbose_name='目标数据')
    
    # 差异信息
    diff_fields = models.JSONField(verbose_name='差异字段列表')
    diff_type = models.CharField(max_length=20, choices=DIFF_TYPE_CHOICES, verbose_name='差异类型')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'comparison_difference'
        verbose_name = '差异记录'
        verbose_name_plural = '差异记录'
        indexes = [
            models.Index(fields=['task', 'record_id']),
            models.Index(fields=['task', 'diff_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Diff {self.record_id} in {self.task.task_id}"


class ComparisonSourceOnly(models.Model):
    """源独有记录"""
    
    task = models.ForeignKey(ComparisonTask, on_delete=models.CASCADE, verbose_name='关联任务')
    
    # 记录标识和数据
    record_id = models.CharField(max_length=255, verbose_name='记录ID')
    data = models.JSONField(verbose_name='记录数据')
    
    # 原因说明
    reason = models.CharField(max_length=200, blank=True, verbose_name='独有原因')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'comparison_source_only'
        verbose_name = '源独有记录'
        verbose_name_plural = '源独有记录'
        indexes = [
            models.Index(fields=['task', 'record_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Source only {self.record_id} in {self.task.task_id}"


class ComparisonTargetOnly(models.Model):
    """目标独有记录"""
    
    task = models.ForeignKey(ComparisonTask, on_delete=models.CASCADE, verbose_name='关联任务')
    
    # 记录标识和数据
    record_id = models.CharField(max_length=255, verbose_name='记录ID')
    data = models.JSONField(verbose_name='记录数据')
    
    # 原因说明
    reason = models.CharField(max_length=200, blank=True, verbose_name='独有原因')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'comparison_target_only'
        verbose_name = '目标独有记录'
        verbose_name_plural = '目标独有记录'
        indexes = [
            models.Index(fields=['task', 'record_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Target only {self.record_id} in {self.task.task_id}"


class ComparisonMetadata(models.Model):
    """比对元数据"""
    
    task = models.OneToOneField(ComparisonTask, on_delete=models.CASCADE, verbose_name='关联任务')
    
    # 数据源信息
    source_type = models.CharField(max_length=50, verbose_name='源数据库类型')
    target_type = models.CharField(max_length=50, verbose_name='目标数据库类型')
    
    # 比对配置
    algorithm = models.CharField(max_length=50, verbose_name='比对算法')
    batch_size = models.IntegerField(default=1000, verbose_name='批次大小')
    
    # 版本信息
    agent_version = models.CharField(max_length=20, blank=True, verbose_name='Agent版本')
    schema_version = models.CharField(max_length=20, default='1.0', verbose_name='数据格式版本')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'comparison_metadata'
        verbose_name = '比对元数据'
        verbose_name_plural = '比对元数据'
    
    def __str__(self):
        return f"Metadata for {self.task.task_id}"
